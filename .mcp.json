{"mcpServers": {"laravel Docs": {"serverUrl": "https://gitmcp.io/laravel/laravel"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "task-master": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"]}, "convex-backend Docs": {"serverUrl": "https://gitmcp.io/get-convex/convex-backend"}, "blocknode docs": {"serverUrl": "https://gitmcp.io/blocknode/blocknode"}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}}, "browser-use": {"command": "npx", "args": ["@agent-infra/mcp-server-browser"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:postgres@localhost:5432/lighthouse_api"]}}}