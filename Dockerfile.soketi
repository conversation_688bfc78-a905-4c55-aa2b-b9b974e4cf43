FROM quay.io/soketi/soketi:latest-16-alpine

ARG SOKETI_DEFAULT_APP_USER_AUTHENTICATION=true
ENV SOKETI_DEFAULT_APP_USER_AUTHENTICATION=${SOKETI_DEFAULT_APP_USER_AUTHENTICATION}

ENV SOKETI_DEBUG=${SOKETI_DEBUG}
ENV SOKETI_METRICS_ENABLED=true
ENV SOKETI_WEBHOOKS_BATCHING=true
ENV SOKETI_APP_MANAGER_CACHE_ENABLED=true
ENV SOKETI_DB_POOLING_ENABLED=true

ENV SOKETI_APP_MANAGER_DRIVER=postgres
ENV SOKETI_APP_MANAGER_POSTGRES_TABLE=applications
ENV SOKETI_APP_MANAGER_CACHE_TTL=3600

ENV SOKETI_DB_POSTGRES_HOST=${SOKETI_DB_POSTGRES_HOST}
ENV SOKETI_DB_POSTGRES_PORT=${SOKETI_DB_POSTGRES_PORT}
ENV SOKETI_DB_POSTGRES_USERNAME=${SOKETI_DB_POSTGRES_USERNAME}
ENV SOKETI_DB_POSTGRES_PASSWORD=${SOKETI_DB_POSTGRES_PASSWORD}
ENV SOKETI_DB_POSTGRES_DATABASE=${SOKETI_DB_POSTGRES_DATABASE}

ENV SOKETI_DB_REDIS_HOST=${SOKETI_DB_REDIS_HOST}
ENV SOKETI_DB_REDIS_PORT=${SOKETI_DB_REDIS_PORT}
ENV SOKETI_DB_REDIS_USERNAME=${SOKETI_DB_REDIS_USERNAME}
ENV SOKETI_DB_REDIS_PASSWORD=${SOKETI_DB_REDIS_PASSWORD}
ENV SOKETI_DB_REDIS_DB=${SOKETI_DB_REDIS_DB}
ENV SOKETI_DB_REDIS_KEY_PREFIX=${SOKETI_DB_REDIS_KEY_PREFIX}

EXPOSE 6001 9601

CMD ["node", "/usr/src/app", "--port", "6001", "--admin-port", "9601"]
