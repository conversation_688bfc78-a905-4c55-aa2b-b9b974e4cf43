<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/Developer/clients/GPJ/lighthouse-api/app/Domains/Auth/Http/Controllers</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=11.0.8" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/Developer/clients/GPJ/lighthouse-api/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Domains</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Auth</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="index.html">Controllers</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Api/ConfirmAccountController.php.html#12">App\Domains\Auth\Http\Controllers\Api\ConfirmAccountController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfirmablePasswordController.php.html#14">App\Domains\Auth\Http\Controllers\ConfirmablePasswordController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailVerificationNotificationController.php.html#14">App\Domains\Auth\Http\Controllers\EmailVerificationNotificationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewPasswordController.php.html#18">App\Domains\Auth\Http\Controllers\NewPasswordController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PasswordResetLinkController.php.html#14">App\Domains\Auth\Http\Controllers\PasswordResetLinkController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProfileInformationController.php.html#15">App\Domains\Auth\Http\Controllers\ProfileInformationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#16">App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#12">App\Domains\Auth\Http\Controllers\UserController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VerifyEmailController.php.html#13">App\Domains\Auth\Http\Controllers\VerifyEmailController</a></td><td class="text-right">80%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#16">App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ConfirmablePasswordController.php.html#14">App\Domains\Auth\Http\Controllers\ConfirmablePasswordController</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="EmailVerificationNotificationController.php.html#14">App\Domains\Auth\Http\Controllers\EmailVerificationNotificationController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ProfileInformationController.php.html#15">App\Domains\Auth\Http\Controllers\ProfileInformationController</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="NewPasswordController.php.html#18">App\Domains\Auth\Http\Controllers\NewPasswordController</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PasswordResetLinkController.php.html#14">App\Domains\Auth\Http\Controllers\PasswordResetLinkController</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="VerifyEmailController.php.html#13">App\Domains\Auth\Http\Controllers\VerifyEmailController</a></td><td class="text-right">3</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Api/ConfirmAccountController.php.html#14"><abbr title="App\Domains\Auth\Http\Controllers\Api\ConfirmAccountController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfirmablePasswordController.php.html#21"><abbr title="App\Domains\Auth\Http\Controllers\ConfirmablePasswordController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfirmablePasswordController.php.html#31"><abbr title="App\Domains\Auth\Http\Controllers\ConfirmablePasswordController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfirmablePasswordController.php.html#41"><abbr title="App\Domains\Auth\Http\Controllers\ConfirmablePasswordController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailVerificationNotificationController.php.html#21"><abbr title="App\Domains\Auth\Http\Controllers\EmailVerificationNotificationController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewPasswordController.php.html#25"><abbr title="App\Domains\Auth\Http\Controllers\NewPasswordController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PasswordResetLinkController.php.html#21"><abbr title="App\Domains\Auth\Http\Controllers\PasswordResetLinkController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProfileInformationController.php.html#24"><abbr title="App\Domains\Auth\Http\Controllers\ProfileInformationController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#28"><abbr title="App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#36"><abbr title="App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#50"><abbr title="App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#22"><abbr title="App\Domains\Auth\Http\Controllers\UserController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VerifyEmailController.php.html#18"><abbr title="App\Domains\Auth\Http\Controllers\VerifyEmailController::__invoke">__invoke</abbr></a></td><td class="text-right">80%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ConfirmablePasswordController.php.html#41"><abbr title="App\Domains\Auth\Http\Controllers\ConfirmablePasswordController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="EmailVerificationNotificationController.php.html#21"><abbr title="App\Domains\Auth\Http\Controllers\EmailVerificationNotificationController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ProfileInformationController.php.html#24"><abbr title="App\Domains\Auth\Http\Controllers\ProfileInformationController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#50"><abbr title="App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController::store">store</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="NewPasswordController.php.html#25"><abbr title="App\Domains\Auth\Http\Controllers\NewPasswordController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PasswordResetLinkController.php.html#21"><abbr title="App\Domains\Auth\Http\Controllers\PasswordResetLinkController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TwoFactorAuthenticatedSessionController.php.html#36"><abbr title="App\Domains\Auth\Http\Controllers\TwoFactorAuthenticatedSessionController::create">create</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="VerifyEmailController.php.html#18"><abbr title="App\Domains\Auth\Http\Controllers\VerifyEmailController::__invoke">__invoke</abbr></a></td><td class="text-right">3</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.8</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.16</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Thu Jan 23 11:19:27 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=11.0.8" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=11.0.8" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,1,1,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([12,0,0,0,0,0,0,0,0,1,2,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[94.**************,10,"<a href=\"Api\/AuthController.php.html#16\">App\\Domains\\Auth\\Http\\Controllers\\Api\\AuthController<\/a>"],[0,1,"<a href=\"Api\/ConfirmAccountController.php.html#12\">App\\Domains\\Auth\\Http\\Controllers\\Api\\ConfirmAccountController<\/a>"],[0,5,"<a href=\"ConfirmablePasswordController.php.html#14\">App\\Domains\\Auth\\Http\\Controllers\\ConfirmablePasswordController<\/a>"],[0,3,"<a href=\"EmailVerificationNotificationController.php.html#14\">App\\Domains\\Auth\\Http\\Controllers\\EmailVerificationNotificationController<\/a>"],[0,2,"<a href=\"NewPasswordController.php.html#18\">App\\Domains\\Auth\\Http\\Controllers\\NewPasswordController<\/a>"],[0,2,"<a href=\"PasswordResetLinkController.php.html#14\">App\\Domains\\Auth\\Http\\Controllers\\PasswordResetLinkController<\/a>"],[0,3,"<a href=\"ProfileInformationController.php.html#15\">App\\Domains\\Auth\\Http\\Controllers\\ProfileInformationController<\/a>"],[100,1,"<a href=\"RegisteredUserController.php.html#15\">App\\Domains\\Auth\\Http\\Controllers\\RegisteredUserController<\/a>"],[0,6,"<a href=\"TwoFactorAuthenticatedSessionController.php.html#16\">App\\Domains\\Auth\\Http\\Controllers\\TwoFactorAuthenticatedSessionController<\/a>"],[0,1,"<a href=\"UserController.php.html#12\">App\\Domains\\Auth\\Http\\Controllers\\UserController<\/a>"],[80,3,"<a href=\"VerifyEmailController.php.html#13\">App\\Domains\\Auth\\Http\\Controllers\\VerifyEmailController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[95.**************,6,"<a href=\"Api\/AuthController.php.html#25\">App\\Domains\\Auth\\Http\\Controllers\\Api\\AuthController::store<\/a>"],[91.**************,4,"<a href=\"Api\/AuthController.php.html#73\">App\\Domains\\Auth\\Http\\Controllers\\Api\\AuthController::destroy<\/a>"],[0,1,"<a href=\"Api\/ConfirmAccountController.php.html#14\">App\\Domains\\Auth\\Http\\Controllers\\Api\\ConfirmAccountController::__invoke<\/a>"],[0,1,"<a href=\"ConfirmablePasswordController.php.html#21\">App\\Domains\\Auth\\Http\\Controllers\\ConfirmablePasswordController::__construct<\/a>"],[0,1,"<a href=\"ConfirmablePasswordController.php.html#31\">App\\Domains\\Auth\\Http\\Controllers\\ConfirmablePasswordController::show<\/a>"],[0,3,"<a href=\"ConfirmablePasswordController.php.html#41\">App\\Domains\\Auth\\Http\\Controllers\\ConfirmablePasswordController::store<\/a>"],[0,3,"<a href=\"EmailVerificationNotificationController.php.html#21\">App\\Domains\\Auth\\Http\\Controllers\\EmailVerificationNotificationController::store<\/a>"],[0,2,"<a href=\"NewPasswordController.php.html#25\">App\\Domains\\Auth\\Http\\Controllers\\NewPasswordController::store<\/a>"],[0,2,"<a href=\"PasswordResetLinkController.php.html#21\">App\\Domains\\Auth\\Http\\Controllers\\PasswordResetLinkController::store<\/a>"],[0,3,"<a href=\"ProfileInformationController.php.html#24\">App\\Domains\\Auth\\Http\\Controllers\\ProfileInformationController::update<\/a>"],[100,1,"<a href=\"RegisteredUserController.php.html#22\">App\\Domains\\Auth\\Http\\Controllers\\RegisteredUserController::store<\/a>"],[0,1,"<a href=\"TwoFactorAuthenticatedSessionController.php.html#28\">App\\Domains\\Auth\\Http\\Controllers\\TwoFactorAuthenticatedSessionController::__construct<\/a>"],[0,2,"<a href=\"TwoFactorAuthenticatedSessionController.php.html#36\">App\\Domains\\Auth\\Http\\Controllers\\TwoFactorAuthenticatedSessionController::create<\/a>"],[0,3,"<a href=\"TwoFactorAuthenticatedSessionController.php.html#50\">App\\Domains\\Auth\\Http\\Controllers\\TwoFactorAuthenticatedSessionController::store<\/a>"],[0,1,"<a href=\"UserController.php.html#22\">App\\Domains\\Auth\\Http\\Controllers\\UserController::__invoke<\/a>"],[80,3,"<a href=\"VerifyEmailController.php.html#18\">App\\Domains\\Auth\\Http\\Controllers\\VerifyEmailController::__invoke<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
