<?php

namespace Tests\Unit\Domains\Betterflow\V1\Editorials\Models\Assets;

use App\Domains\Betterflow\V1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssetSegmentTest extends TestCase
{
    use RefreshDatabase;

    protected Editorial $editorial;
    protected Asset $asset;
    protected AssetContent $assetContent;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->editorial = Editorial::factory()->create();
        $this->asset = Asset::factory()->create(['editorial_id' => $this->editorial->id]);
        $this->assetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'en-EN',
        ]);
    }

    #[Test]
    public function it_can_retrieve_segment_by_name(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Test story content'],
        ]);
        
        $retrievedSegment = $this->asset->segment('story-content');
        
        $this->assertInstanceOf(AssetContentSegment::class, $retrievedSegment);
        $this->assertEquals($segment->id, $retrievedSegment->id);
        $this->assertEquals('story-content', $retrievedSegment->segment);
        $this->assertEquals(['content' => 'Test story content'], $retrievedSegment->data);
    }

    #[Test]
    public function it_can_retrieve_segment_with_specific_language(): void
    {
        $deContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'de-DE',
        ]);
        
        $enSegment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'English content'],
        ]);
        
        $deSegment = AssetContentSegment::factory()->create([
            'asset_content_id' => $deContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'German content'],
        ]);
        
        $retrievedEnSegment = $this->asset->segment('story-content', 'en-EN');
        $retrievedDeSegment = $this->asset->segment('story-content', 'de-DE');
        
        $this->assertEquals($enSegment->id, $retrievedEnSegment->id);
        $this->assertEquals('English content', $retrievedEnSegment->data['content']);
        
        $this->assertEquals($deSegment->id, $retrievedDeSegment->id);
        $this->assertEquals('German content', $retrievedDeSegment->data['content']);
    }

    #[Test]
    public function it_returns_null_for_non_existent_segment(): void
    {
        $retrievedSegment = $this->asset->segment('non-existent');
        
        $this->assertNull($retrievedSegment);
    }

    #[Test]
    public function it_returns_null_for_non_existent_language(): void
    {
        AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'English content'],
        ]);
        
        $retrievedSegment = $this->asset->segment('story-content', 'fr-FR');
        
        $this->assertNull($retrievedSegment);
    }

    #[Test]
    public function it_uses_row_level_locking_for_segment_retrieval(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Test content'],
        ]);
        
        $retrievedSegment = $this->asset->segment('story-content');
        
        $this->assertInstanceOf(AssetContentSegment::class, $retrievedSegment);
        $this->assertEquals($segment->id, $retrievedSegment->id);
    }

    #[Test]
    public function it_can_retrieve_different_segment_types(): void
    {
        $segmentTypes = [
            'story-content' => ['content' => 'Story content'],
            'photo' => ['images' => [['url' => 'image.jpg']]],
            'research' => ['notes' => 'Research notes'],
            'workshop' => ['materials' => 'Workshop materials'],
        ];
        
        foreach ($segmentTypes as $segmentType => $data) {
            AssetContentSegment::factory()->create([
                'asset_content_id' => $this->assetContent->id,
                'segment' => $segmentType,
                'data' => $data,
            ]);
        }
        
        foreach ($segmentTypes as $segmentType => $expectedData) {
            $retrievedSegment = $this->asset->segment($segmentType);
            
            $this->assertInstanceOf(AssetContentSegment::class, $retrievedSegment);
            $this->assertEquals($segmentType, $retrievedSegment->segment);
            $this->assertEquals($expectedData, $retrievedSegment->data);
        }
    }

    #[Test]
    public function it_handles_segments_with_null_data(): void
    {
        $segment = AssetContentSegment::factory()->withEmptyData()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
        ]);
        
        $retrievedSegment = $this->asset->segment('story-content');
        
        $this->assertInstanceOf(AssetContentSegment::class, $retrievedSegment);
        $this->assertEquals($segment->id, $retrievedSegment->id);
        $this->assertNull($retrievedSegment->data);
    }

    #[Test]
    public function it_defaults_to_en_en_language(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'English content'],
        ]);
        
        $retrievedSegment = $this->asset->segment('story-content');
        
        $this->assertInstanceOf(AssetContentSegment::class, $retrievedSegment);
        $this->assertEquals($segment->id, $retrievedSegment->id);
        $this->assertEquals('English content', $retrievedSegment->data['content']);
    }

    #[Test]
    public function it_returns_correct_segment_when_multiple_languages_exist(): void
    {
        $frContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'fr-FR',
        ]);
        
        $esContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'es-ES',
        ]);
        
        $enSegment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'English content'],
        ]);
        
        $frSegment = AssetContentSegment::factory()->create([
            'asset_content_id' => $frContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'French content'],
        ]);
        
        $esSegment = AssetContentSegment::factory()->create([
            'asset_content_id' => $esContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Spanish content'],
        ]);
        
        $retrievedEnSegment = $this->asset->segment('story-content', 'en-EN');
        $retrievedFrSegment = $this->asset->segment('story-content', 'fr-FR');
        $retrievedEsSegment = $this->asset->segment('story-content', 'es-ES');
        
        $this->assertEquals($enSegment->id, $retrievedEnSegment->id);
        $this->assertEquals('English content', $retrievedEnSegment->data['content']);
        
        $this->assertEquals($frSegment->id, $retrievedFrSegment->id);
        $this->assertEquals('French content', $retrievedFrSegment->data['content']);
        
        $this->assertEquals($esSegment->id, $retrievedEsSegment->id);
        $this->assertEquals('Spanish content', $retrievedEsSegment->data['content']);
    }

    #[Test]
    public function it_maintains_segment_relationship_consistency(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Test content'],
        ]);
        
        $retrievedSegment = $this->asset->segment('story-content');
        
        $this->assertEquals($this->asset->id, $retrievedSegment->asset->id);
        $this->assertEquals($this->assetContent->id, $retrievedSegment->assetContent->id);
        $this->assertEquals($this->assetContent->id, $retrievedSegment->asset_content_id);
    }
}