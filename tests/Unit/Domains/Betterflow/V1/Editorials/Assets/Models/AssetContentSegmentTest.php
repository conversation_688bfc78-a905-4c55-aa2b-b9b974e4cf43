<?php

namespace Tests\Unit\Domains\Betterflow\V1\Editorials\Assets\Models;

use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssetContentSegmentTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_has_correct_table_name(): void
    {
        $segment = new AssetContentSegment();

        $this->assertEquals('asset_content_segments', $segment->getTable());
    }

    public function test_it_has_correct_fillable_attributes(): void
    {
        $segment = new AssetContentSegment();

        $this->assertEquals([], $segment->getGuarded());
    }

    public function test_it_casts_data_to_json(): void
    {
        $segment = new AssetContentSegment();

        $this->assertEquals('json', $segment->getCasts()['data']);
    }

    public function test_it_belongs_to_asset_content(): void
    {
        $segment = new AssetContentSegment();

        $relation = $segment->assetContent();

        $this->assertInstanceOf(BelongsTo::class, $relation);
        $this->assertEquals('asset_content_id', $relation->getForeignKeyName());
        $this->assertEquals(AssetContent::class, $relation->getRelated()::class);
    }

    public function test_it_has_one_through_asset(): void
    {
        $segment = new AssetContentSegment();

        $relation = $segment->asset();

        $this->assertInstanceOf(HasOneThrough::class, $relation);
        $this->assertEquals(Asset::class, $relation->getRelated()::class);
    }

    public function test_it_can_create_segment_with_data(): void
    {
        $assetContent = AssetContent::factory()->create();
        $testData = [
            'title' => 'Test Title',
            'content' => 'Test Content',
            'metadata' => ['author' => 'Test Author']
        ];

        $segment = AssetContentSegment::factory()
            ->forAssetContent($assetContent)
            ->withSegment('test-segment')
            ->withData($testData)
            ->create();

        $this->assertDatabaseHas('asset_content_segments', [
            'id' => $segment->id,
            'asset_content_id' => $assetContent->id,
            'segment' => 'test-segment',
        ]);

        $this->assertEquals($testData, $segment->data);
    }

    public function test_it_can_create_segment_with_empty_data(): void
    {
        $assetContent = AssetContent::factory()->create();

        $segment = AssetContentSegment::factory()
            ->forAssetContent($assetContent)
            ->empty()
            ->create();

        $this->assertDatabaseHas('asset_content_segments', [
            'id' => $segment->id,
            'asset_content_id' => $assetContent->id,
        ]);

        $this->assertEquals([], $segment->data);
    }

    public function test_it_can_access_asset_through_asset_content(): void
    {
        $asset = Asset::factory()->create();
        $assetContent = AssetContent::factory()->create(['asset_id' => $asset->id]);
        $segment = AssetContentSegment::factory()->forAssetContent($assetContent)->create();

        $this->assertEquals($asset->id, $segment->asset->id);
    }

    public function test_it_can_access_asset_content_relationship(): void
    {
        $assetContent = AssetContent::factory()->create();
        $segment = AssetContentSegment::factory()->forAssetContent($assetContent)->create();

        $this->assertEquals($assetContent->id, $segment->assetContent->id);
    }

    public function test_data_is_properly_serialized_and_deserialized(): void
    {
        $complexData = [
            'title' => 'Complex Title',
            'nested' => [
                'array' => [1, 2, 3],
                'object' => ['key' => 'value']
            ],
            'boolean' => true,
            'null_value' => null
        ];

        $segment = AssetContentSegment::factory()
            ->withData($complexData)
            ->create();

        $segment->refresh();

        $this->assertEquals($complexData, $segment->data);
    }

    public function test_it_handles_null_data(): void
    {
        $segment = AssetContentSegment::factory()
            ->withData(null)
            ->create();

        $this->assertNull($segment->data);
    }

    public function test_unique_constraint_on_asset_content_and_segment(): void
    {
        $this->expectException(\Illuminate\Database\UniqueConstraintViolationException::class);

        $assetContent = AssetContent::factory()->create();
        $segmentName = 'duplicate-segment';

        // Create first segment
        AssetContentSegment::factory()
            ->forAssetContent($assetContent)
            ->withSegment($segmentName)
            ->create();

        // Try to create duplicate segment - should throw exception
        AssetContentSegment::factory()
            ->forAssetContent($assetContent)
            ->withSegment($segmentName)
            ->create();
    }

    public function test_same_segment_name_allowed_for_different_asset_contents(): void
    {
        $assetContent1 = AssetContent::factory()->create();
        $assetContent2 = AssetContent::factory()->create();
        $segmentName = 'same-segment';

        $segment1 = AssetContentSegment::factory()
            ->forAssetContent($assetContent1)
            ->withSegment($segmentName)
            ->create();

        $segment2 = AssetContentSegment::factory()
            ->forAssetContent($assetContent2)
            ->withSegment($segmentName)
            ->create();

        $this->assertNotEquals($segment1->id, $segment2->id);
        $this->assertEquals($segmentName, $segment1->segment);
        $this->assertEquals($segmentName, $segment2->segment);
    }
}
