<?php

namespace Tests\Unit\Domains\Betterflow\V1\Editorials\Assets\Models;

use App\Domains\Betterflow\V1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssetContentSegmentTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_has_correct_table_name(): void
    {
        $segment = new AssetContentSegment();
        
        $this->assertEquals('asset_content_segments', $segment->getTable());
    }

    #[Test]
    public function it_has_correct_fillable_attributes(): void
    {
        $segment = new AssetContentSegment();
        
        $this->assertEquals(['segment', 'data'], $segment->getFillable());
    }

    #[Test]
    public function it_casts_data_attribute_to_array(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'data' => ['content' => 'test content'],
        ]);
        
        $this->assertIsArray($segment->data);
        $this->assertEquals(['content' => 'test content'], $segment->data);
    }

    #[Test]
    public function it_belongs_to_asset_content(): void
    {
        $segment = new AssetContentSegment();
        
        $relation = $segment->assetContent();
        
        $this->assertInstanceOf(BelongsTo::class, $relation);
        $this->assertEquals(AssetContent::class, $relation->getModel()::class);
    }

    #[Test]
    public function it_has_asset_through_asset_content(): void
    {
        $segment = new AssetContentSegment();
        
        $relation = $segment->asset();
        
        $this->assertInstanceOf(HasOneThrough::class, $relation);
        $this->assertEquals(Asset::class, $relation->getModel()::class);
    }

    #[Test]
    public function it_can_store_complex_data_structure(): void
    {
        $complexData = [
            'content' => 'Lorem ipsum dolor sit amet',
            'metadata' => [
                'author' => 'John Doe',
                'version' => 2,
                'tags' => ['important', 'draft'],
            ],
            'settings' => [
                'word_count' => 150,
                'last_edited' => '2023-01-01 10:00:00',
            ],
        ];
        
        $segment = AssetContentSegment::factory()->create([
            'data' => $complexData,
        ]);
        
        $this->assertEquals($complexData, $segment->data);
        $this->assertEquals('John Doe', $segment->data['metadata']['author']);
        $this->assertEquals(['important', 'draft'], $segment->data['metadata']['tags']);
    }

    #[Test]
    public function it_can_have_null_data(): void
    {
        $segment = AssetContentSegment::factory()->withEmptyData()->create();
        
        $this->assertNull($segment->data);
    }

    #[Test]
    public function it_enforces_unique_constraint_on_asset_content_and_segment(): void
    {
        $assetContent = AssetContent::factory()->create();
        
        AssetContentSegment::factory()->create([
            'asset_content_id' => $assetContent->id,
            'segment' => 'story-content',
        ]);
        
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        AssetContentSegment::factory()->create([
            'asset_content_id' => $assetContent->id,
            'segment' => 'story-content',
        ]);
    }

    #[Test]
    public function it_can_create_multiple_segments_for_same_asset_content(): void
    {
        $assetContent = AssetContent::factory()->create();
        
        $segment1 = AssetContentSegment::factory()->create([
            'asset_content_id' => $assetContent->id,
            'segment' => 'story-content',
        ]);
        
        $segment2 = AssetContentSegment::factory()->create([
            'asset_content_id' => $assetContent->id,
            'segment' => 'photo',
        ]);
        
        $this->assertEquals($assetContent->id, $segment1->asset_content_id);
        $this->assertEquals($assetContent->id, $segment2->asset_content_id);
        $this->assertEquals('story-content', $segment1->segment);
        $this->assertEquals('photo', $segment2->segment);
    }

    #[Test]
    public function it_can_access_asset_through_relationship(): void
    {
        $asset = Asset::factory()->create();
        $assetContent = AssetContent::factory()->create(['asset_id' => $asset->id]);
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $assetContent->id,
        ]);
        
        $relatedAsset = $segment->asset;
        
        $this->assertInstanceOf(Asset::class, $relatedAsset);
        $this->assertEquals($asset->id, $relatedAsset->id);
    }

    #[Test]
    public function it_updates_timestamps_when_data_is_modified(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'data' => ['content' => 'original content'],
        ]);
        
        $originalUpdatedAt = $segment->updated_at;
        
        $this->travel(1)->minutes();
        
        $segment->update(['data' => ['content' => 'updated content']]);
        
        $this->assertNotEquals($originalUpdatedAt, $segment->fresh()->updated_at);
    }
}