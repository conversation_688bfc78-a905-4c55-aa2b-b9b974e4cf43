<?php

namespace Tests\Unit\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Actions\ReassignTask;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\UserReassigned;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class ReassignTaskTest extends TestCase
{
    use FastRefreshDatabase;
    use WithFaker;

    protected function createTask(array $attributes = []): Task
    {
        $pitch = Pitch::factory()->create([
            'public_id' => str()->orderedUuid(),
        ]);

        $user = User::factory()->create();

        return Task::factory()->create(array_merge([
            'taskable_type' => get_class($pitch),
            'taskable_id' => $pitch->id,
            'user_id' => $user->id,
            'type' => fake()->randomElement(PitchTaskType::cases()),
            'status' => TaskStatus::Pending,
        ], $attributes));
    }

    #[Test]
    public function it_reassigns_a_task_to_a_new_user_successfully(): void
    {
        // Arrange
        Event::fake();
        $task = $this->createTask();
        $newUser = User::factory()->create();
        $notes = 'Task reassigned to a different user';

        // Act
        $result = ReassignTask::run($task, $newUser, $notes);

        // Assert
        $this->assertEquals($newUser->id, $result->user_id);
        $this->assertEquals($notes, $result->notes);
        Event::assertDispatched(UserReassigned::class);
    }

    #[Test]
    public function it_throws_exception_when_reassigning_to_same_user(): void
    {
        // Arrange
        $task = $this->createTask();
        $sameUser = $task->user;

        // Act & Assert
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('task is already assigned to this user');

        ReassignTask::run($task, $sameUser, 'Notes');
    }

    #[Test]
    public function it_returns_task_resource_in_json_response(): void
    {
        // Arrange
        $task = $this->createTask();
        $action = new ReassignTask();

        // Act
        $response = $action->jsonResponse($task);

        // Assert
        $this->assertInstanceOf(TaskResource::class, $response);
    }

    #[Test]
    public function it_handles_controller_request_correctly(): void
    {
        // Arrange
        Event::fake();
        $task = $this->createTask();
        $newUser = User::factory()->create();

        // Create a mock request
        $request = new Request();
        $request->merge([
            'user_id' => $newUser->id,
            'notes' => 'Controller notes'
        ]);

        $action = new ReassignTask();

        // Act
        $result = $action->asController($task, $request);

        // Assert
        $this->assertEquals($newUser->id, $result->user_id);
        $this->assertEquals('Controller notes', $result->notes);
        Event::assertDispatched(UserReassigned::class);
    }

    #[Test]
    public function it_registers_routes_correctly(): void
    {
        // Arrange
        $router = new Router(new \Illuminate\Events\Dispatcher());

        // Act
        ReassignTask::routes($router);

        // Assert
        $routes = $router->getRoutes();
        $this->assertCount(1, $routes->getRoutes());

        $route = $routes->getRoutes()[0];
        $this->assertEquals('PUT', $route->methods()[0]);
        $this->assertEquals('shared/tasks/{task}/reassign', $route->uri());
        $this->assertEquals(ReassignTask::class . '@__invoke', $route->getAction()['uses']);
        $this->assertEquals('common.tasks.reassign', $route->getName());
    }

    #[Test]
    public function it_validates_input_correctly(): void
    {
        // Arrange
        $action = new ReassignTask();

        // Act
        $rules = $action->rules();

        // Assert
        $this->assertArrayHasKey('user_id', $rules);
        $this->assertEquals(['required', 'integer', 'exists:users,id'], $rules['user_id']);
        $this->assertArrayHasKey('notes', $rules);
        $this->assertEquals(['nullable', 'string'], $rules['notes']);
    }

    #[Test]
    public function it_updates_task_user_relationship(): void
    {
        // Arrange
        $task = $this->createTask();
        $originalUser = $task->user;
        $newUser = User::factory()->create();

        // Act
        ReassignTask::run($task, $newUser, 'Reassignment notes');
        $task->refresh();

        // Assert
        $this->assertTrue($task->user->is($newUser));
        $this->assertFalse($task->user->is($originalUser));
    }

    #[Test]
    public function it_preserves_task_status_when_reassigning(): void
    {
        // Arrange
        $task = $this->createTask(['status' => TaskStatus::Pending, 'progress' => 50]);
        $newUser = User::factory()->create();

        // Act
        ReassignTask::run($task, $newUser);
        $task->refresh();

        // Assert
        $this->assertEquals(TaskStatus::Pending, $task->status);
        $this->assertEquals(50, $task->progress);
    }

    #[Test]
    public function it_can_reassign_completed_tasks(): void
    {
        // Arrange
        $task = $this->createTask([
            'status' => TaskStatus::Completed,
            'completed_at' => now(),
            'progress' => 100
        ]);
        $newUser = User::factory()->create();

        // Act
        $result = ReassignTask::run($task, $newUser, 'Reassigning completed task');

        // Assert
        $this->assertEquals($newUser->id, $result->user_id);
        $this->assertEquals(TaskStatus::Completed, $result->status);
        $this->assertNotNull($result->completed_at);
    }
}
