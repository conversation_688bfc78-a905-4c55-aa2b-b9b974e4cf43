<?php

namespace Tests\Unit\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Actions\IncompleteTask;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Illuminate\Routing\Router;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class IncompleteTaskTest extends TestCase
{
    use FastRefreshDatabase;
    use WithFaker;

    protected function createCompletedTask(array $attributes = []): Task
    {
        $pitch = Pitch::factory()->create([
            'public_id' => str()->orderedUuid(),
        ]);

        return Task::factory()->create(array_merge([
            'taskable_type' => get_class($pitch),
            'taskable_id' => $pitch->id,
            'type' => fake()->randomElement(PitchTaskType::cases()),
            'status' => TaskStatus::Completed,
            'completed_at' => now(),
            'progress' => 100,
        ], $attributes));
    }

    #[Test]
    public function it_marks_a_task_as_incomplete_successfully(): void
    {
        // Arrange
        $task = $this->createCompletedTask();
        $notes = 'Task marked as incomplete';

        // Act
        IncompleteTask::run($task, $notes);

        // Assert
        $task->refresh();
        $this->assertEquals(TaskStatus::Pending, $task->status);
        $this->assertNull($task->completed_at);
        $this->assertEquals($notes, $task->notes);
        $this->assertEquals(0, $task->progress);
    }

    #[Test]
    public function it_throws_exception_when_marking_incomplete_a_pending_task(): void
    {
        // Arrange
        $task = Task::factory()->create([
            'taskable_type' => 'pitch',
            'taskable_id' => Pitch::factory(),
            'status' => TaskStatus::Pending,
        ]);

        // Act & Assert
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('This Task is not completed');

        IncompleteTask::run($task, 'Notes');
    }

    #[Test]
    public function it_returns_task_resource_in_json_response(): void
    {
        // Arrange
        $task = $this->createCompletedTask();
        $action = new IncompleteTask();

        // Act
        $response = $action->jsonResponse($task);

        // Assert
        $this->assertInstanceOf(TaskResource::class, $response);
    }

    #[Test]
    public function it_handles_controller_request_correctly(): void
    {
        // Arrange
        $task = $this->createCompletedTask();
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a mock request with notes
        $request = new Request();
        $request->merge(['notes' => 'Controller notes']);

        $action = new IncompleteTask();

        // Act
        $action->asController($task, $request);

        // Assert
        $task->refresh();
        $this->assertEquals(TaskStatus::Pending, $task->status);
        $this->assertNull($task->completed_at);
        $this->assertEquals('Controller notes', $task->notes);
    }

    #[Test]
    public function it_registers_routes_correctly(): void
    {
        // Arrange
        $router = new Router(new \Illuminate\Events\Dispatcher());

        // Act
        IncompleteTask::routes($router);

        // Assert
        $routes = $router->getRoutes();
        $this->assertCount(2, $routes->getRoutes());

        $route = $routes->getRoutes()[1];
        $this->assertEquals('PUT', $route->methods()[0]);
        $this->assertEquals('shared/tasks/{task}/incomplete', $route->uri());
        $this->assertEquals(IncompleteTask::class . '@__invoke', $route->getAction()['uses']);
        $this->assertEquals('common.tasks.incomplete', $route->getName());
    }

    #[Test]
    public function it_validates_notes_correctly(): void
    {
        // Arrange
        $action = new IncompleteTask();

        // Act
        $rules = $action->rules();

        // Assert
        $this->assertArrayHasKey('notes', $rules);
        $this->assertEquals(['nullable', 'string'], $rules['notes']);
    }

    // Skipping API test as it requires route registration
    // #[Test]
    // public function it_handles_api_request_correctly(): void
    // {
    //     // This test requires the route to be registered in the application
    //     // which is not available in unit tests
    // }
}
