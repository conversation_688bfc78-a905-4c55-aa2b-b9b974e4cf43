<?php

namespace Tests\Unit\Domains\Notifications;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Notifications\Http\Resources\NotificationAssetResource;
use App\Domains\Notifications\Http\Resources\NotificationEditorialResource;
use App\Domains\Notifications\Http\Resources\NotificationPitchResource;
use App\Domains\Notifications\Http\Resources\NotificationTaskResource;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class NotificationResourcesTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function notification_task_resource_handles_null_resource()
    {
        $resource = new NotificationTaskResource(null);
        $result = $resource->toArray(request());

        $this->assertNull($result);
    }

    #[Test]
    public function notification_task_resource_handles_missing_properties()
    {
        $task = new Task();
        $task->id = 1;
        // Leave other properties null

        $resource = new NotificationTaskResource($task);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEquals('Untitled Task', $result['title']);
        $this->assertEquals('unknown', $result['type']);
        $this->assertNull($result['description']);
        $this->assertNull($result['notes']);
        $this->assertNull($result['assignee']);
        $this->assertNull($result['due_at']);
    }

    #[Test]
    public function notification_task_resource_works_with_valid_data()
    {
        $user = User::factory()->create();
        $task = Task::factory()->create([
            'title' => 'Test Task',
            'description' => 'Test Description',
            'type' => 'test_type',
            'notes' => 'Test Notes',
            'user_id' => $user->id,
            'due_at' => now(),
        ]);

        $resource = new NotificationTaskResource($task);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEquals('Test Task', $result['title']);
        $this->assertEquals('test_type', $result['type']);
        $this->assertEquals('Test Description', $result['description']);
        $this->assertEquals('Test Notes', $result['notes']);
        $this->assertNotNull($result['assignee']);
        $this->assertNotNull($result['due_at']);
    }

    #[Test]
    public function notification_asset_resource_handles_null_resource()
    {
        $resource = new NotificationAssetResource(null);
        $result = $resource->toArray(request());

        $this->assertNull($result);
    }

    #[Test]
    public function notification_asset_resource_handles_missing_properties()
    {
        $asset = Asset::factory()->make([
            'name' => null,
            'type' => null,
        ]);

        $resource = new NotificationAssetResource($asset);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEquals('Unnamed Asset', $result['name']);
        $this->assertEquals('unknown', $result['type']);
    }

    #[Test]
    public function notification_editorial_resource_handles_null_resource()
    {
        $resource = new NotificationEditorialResource(null);
        $result = $resource->toArray(request());

        $this->assertNull($result);
    }

    #[Test]
    public function notification_editorial_resource_handles_missing_properties()
    {
        $editorial = Editorial::factory()->make([
            'name' => null,
            'phase' => null,
            'sub_phase' => null,
        ]);

        $resource = new NotificationEditorialResource($editorial);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEquals('Unnamed Editorial', $result['name']);
        $this->assertNull($result['phase']);
        $this->assertNull($result['sub_phase']);
    }

    #[Test]
    public function notification_pitch_resource_handles_null_resource()
    {
        $resource = new NotificationPitchResource(null);
        $result = $resource->toArray(request());

        $this->assertNull($result);
    }

    #[Test]
    public function notification_pitch_resource_handles_missing_properties()
    {
        $pitch = Pitch::factory()->make([
            'short_name' => null,
        ]);

        $resource = new NotificationPitchResource($pitch);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEquals('Unnamed Pitch', $result['short_name']);
    }

    #[Test]
    public function all_notification_resources_handle_missing_resource_key_method()
    {
        // Test fallback to id when getResourceKey() doesn't exist or returns null
        $mockAsset = new class {
            public $id = 123;
            public $name = 'Test Asset';
            public $type = null;
            
            public function loadMissing($relations) {
                return $this;
            }
            
            public function getResourceKey() {
                return null; // Simulate missing resource key
            }
        };

        $resource = new NotificationAssetResource($mockAsset);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']); // Should fallback to id
    }

    #[Test]
    public function notification_task_resource_handles_null_user_relationship()
    {
        $user = User::factory()->create();
        $task = Task::factory()->create(['user_id' => $user->id]);
        
        // Manually set the user relationship to null to simulate a deleted user
        $task->setRelation('user', null);

        $resource = new NotificationTaskResource($task);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertNull($result['assignee']);
    }

    #[Test]
    public function notification_resources_preserve_original_data_when_available()
    {
        $user = User::factory()->create();
        $task = Task::factory()->create([
            'title' => 'Original Task Title',
            'description' => 'Original Description',
            'user_id' => $user->id,
        ]);

        $resource = new NotificationTaskResource($task);
        $result = $resource->toArray(request());

        $this->assertEquals('Original Task Title', $result['title']);
        $this->assertEquals('Original Description', $result['description']);
        $this->assertNotNull($result['assignee']);
        $this->assertEquals($user->id, $result['assignee']['id']);
    }
}