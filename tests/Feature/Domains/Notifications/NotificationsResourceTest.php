<?php

namespace Tests\Feature\Domains\Notifications;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Notifications\Http\Resources\NotificationsResource;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class NotificationsResourceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected DatabaseNotification $notification;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_handles_null_resources_gracefully()
    {
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'task', 'id' => 999999], // Non-existent task
                ['type' => 'asset', 'id' => 'non-existent-id'], // Non-existent asset
                null, // Null resource
                [], // Empty resource
            ]
        ]);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertArrayHasKey('resources', $result);
        $this->assertEmpty($result['resources']); // Should filter out all null/invalid resources
    }

    #[Test]
    public function it_handles_malformed_resource_data()
    {
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'task'], // Missing ID
                ['id' => '123'], // Missing type
                'invalid-string', // Not an array
                ['type' => '', 'id' => ''], // Empty values
            ]
        ]);

        Log::shouldReceive('warning')->atLeast()->once();

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEmpty($result['resources']);
    }

    #[Test]
    public function it_handles_valid_resources_correctly()
    {
        $task = Task::factory()->create();
        $user = User::factory()->create();
        
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'task', 'id' => $task->id],
            ],
            'who_did_it' => ['id' => $user->id]
        ]);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertCount(1, $result['resources']);
        $this->assertNotNull($result['who_did_it']);
        $this->assertEquals($user->id, $result['who_did_it']['id']);
    }

    #[Test]
    public function it_handles_missing_who_did_it_user()
    {
        $notification = $this->createNotificationWithData([
            'who_did_it' => ['id' => 999999] // Non-existent user
        ]);

        Log::shouldReceive('warning')->once();

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertNull($result['who_did_it']);
    }

    #[Test]
    public function it_handles_malformed_who_did_it_data()
    {
        $notification = $this->createNotificationWithData([
            'who_did_it' => 'invalid-data' // Not an array
        ]);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertNull($result['who_did_it']);
    }

    #[Test]
    public function it_handles_mixed_valid_and_invalid_resources()
    {
        $task = Task::factory()->create();
        
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'task', 'id' => $task->id], // Valid
                ['type' => 'task', 'id' => 999999], // Invalid - doesn't exist
                ['type' => 'asset', 'id' => 'invalid'], // Invalid - doesn't exist
                null, // Invalid - null
            ]
        ]);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertCount(1, $result['resources']); // Only the valid task should remain
        $this->assertEquals($task->getResourceKey(), $result['resources'][0]['id']);
    }

    #[Test]
    public function it_logs_errors_for_model_lookup_failures()
    {
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'unknown-type', 'id' => '123'],
            ]
        ]);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertEmpty($result['resources']);
    }

    #[Test]
    public function it_handles_empty_or_null_notification_data()
    {
        $notification = $this->createNotificationWithData(null);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertIsArray($result);
        $this->assertEmpty($result['resources']);
        $this->assertNull($result['who_did_it']);
    }

    #[Test]
    public function it_maintains_data_spread_functionality()
    {
        $customData = [
            'custom_field' => 'custom_value',
            'another_field' => 123,
        ];

        $notification = $this->createNotificationWithData($customData);

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertEquals('custom_value', $result['custom_field']);
        $this->assertEquals(123, $result['another_field']);
    }

    #[Test]
    public function it_handles_exception_during_model_lookup()
    {
        // Create a notification with data that might cause an exception
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'task', 'id' => 'invalid-id-format'],
            ]
        ]);

        Log::shouldReceive('error')->once();

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        $this->assertEmpty($result['resources']);
    }

    #[Test]
    public function it_handles_exception_during_resource_transformation()
    {
        // This test ensures that if a resource transformation throws an exception,
        // it's handled gracefully
        $task = Task::factory()->create();
        
        $notification = $this->createNotificationWithData([
            'resources' => [
                ['type' => 'task', 'id' => $task->id],
            ]
        ]);

        Log::shouldReceive('error')->atMost()->once();

        $resource = new NotificationsResource($notification);
        $result = $resource->toArray(request());

        // Should handle any transformation errors gracefully
        $this->assertIsArray($result);
        $this->assertArrayHasKey('resources', $result);
    }

    protected function createNotificationWithData($data): DatabaseNotification
    {
        return DatabaseNotification::create([
            'id' => \Str::uuid(),
            'type' => 'App\\Notifications\\TestNotification',
            'notifiable_type' => User::class,
            'notifiable_id' => $this->user->id,
            'data' => $data ?? [],
            'read_at' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}