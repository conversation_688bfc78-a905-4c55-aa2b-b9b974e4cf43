<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Assets;

use App\Domains\Betterflow\V1\Editorials\Actions\ReassignAsset;
use App\Domains\Betterflow\V1\Editorials\Events\AssetReassigned;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Users\Models\User;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class ReassignAssetTest extends TestCase
{
    protected Editorial $editorial;
    protected Asset $asset;
    protected User $originalAssignee;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->originalAssignee = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
        $this->asset = Asset::factory()->create([
            'editorial_id' => $this->editorial->getKey(),
            'assigned_to_id' => $this->originalAssignee->getKey(),
        ]);

        // Create some tasks for the asset
        $this->asset->tasks()->create([
            'user_id' => $this->originalAssignee->id,
            'title' => 'Test task',
            'due_at' => now()->addDays(3),
            'type' => 'test_task',
        ]);
    }

    #[Test]
    public function it_reassigns_an_asset_to_a_new_user_successfully(): void
    {
        // Arrange
        Event::fake();
        $newAssignee = User::factory()->create();

        // Act
        $response = $this->actingAs($this->originalAssignee)
            ->putJson(route('betterflow.v1.editorials.assets.re-assign', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'assign_to' => $newAssignee->getKey(),
            ]);

        // Assert
        $response->assertOk();
        $this->asset->refresh();
        $this->assertEquals($newAssignee->id, $this->asset->assigned_to_id);
        Event::assertDispatched(AssetReassigned::class);
        Event::assertDispatched(fn (AssetReassigned $event) =>
            $event->asset->id === $this->asset->id &&
            $event->fromAssignee === $this->originalAssignee->id &&
            $event->toAssignee === $newAssignee->id
        );
    }

    #[Test]
    public function it_throws_exception_when_reassigning_asset_to_same_user(): void
    {
        // Arrange
        $sameUser = $this->originalAssignee;

        // Act & Assert
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('Asset is already assigned to this user');

        ReassignAsset::run($this->asset, $sameUser->id, $this->originalAssignee->id);
    }

    #[Test]
    public function it_reassigns_related_tasks_when_asset_is_reassigned(): void
    {
        // Arrange
        $newAssignee = User::factory()->create();

        // Act
        ReassignAsset::run($this->asset, $newAssignee->id, $this->originalAssignee->id);

        // Assert
        $this->asset->refresh();
        foreach ($this->asset->tasks as $task) {
            $this->assertEquals($newAssignee->id, $task->user_id);
        }
    }

    #[Test]
    public function it_dispatches_record_activity_job(): void
    {
        // Arrange
        Queue::fake([
            RecordActivity::class, 
        ]);

        Event::fake([
            AssetReassigned::class,
        ]);
        $newAssignee = User::factory()->create();

        // Act
        ReassignAsset::run($this->asset, $newAssignee->id, $this->originalAssignee->id);

        // Assert
        Queue::assertPushed(RecordActivity::class);
        Event::assertDispatched(AssetReassigned::class);
    }

    #[Test]
    public function it_handles_controller_request_for_asset_reassignment(): void
    {
        // Arrange
        Event::fake();
        $newAssignee = User::factory()->create();
        $user = User::factory()->create();

        // Act
        $response = $this->actingAs($user)
            ->putJson(route('betterflow.v1.editorials.assets.re-assign', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'assign_to' => $newAssignee->id,
            ]);

        // Assert
        $response->assertOk();
        $this->asset->refresh();
        $this->assertEquals($newAssignee->id, $this->asset->assigned_to_id);
        Event::assertDispatched(AssetReassigned::class);
    }

    #[Test]
    public function it_validates_input_correctly(): void
    {
        // Arrange
        $action = new ReassignAsset();

        // Act
        $rules = $action->rules();

        // Assert
        $this->assertArrayHasKey('assign_to', $rules);
        $this->assertEquals(['required', 'integer', 'exists:users,id'], $rules['assign_to']);
    }

    #[Test]
    public function it_returns_asset_resource_in_json_response(): void
    {
        // Arrange
        $action = new ReassignAsset();

        // Act
        $response = $action->jsonResponse($this->asset);

        // Assert
        $this->assertInstanceOf(\App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetResource::class, $response);
    }
}
