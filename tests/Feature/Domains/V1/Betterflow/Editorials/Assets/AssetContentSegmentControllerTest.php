<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Assets;

use App\Domains\Betterflow\V1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AssetContentSegmentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Editorial $editorial;
    protected Asset $asset;
    protected AssetContent $assetContent;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->seed([
            'RoleSeeder',
            'PermissionSeeder',
        ]);
        
        $this->user = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
        $this->asset = Asset::factory()->create(['editorial_id' => $this->editorial->id]);
        $this->assetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'en-EN',
        ]);
    }

    #[Test]
    public function it_can_show_existing_segment(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Test story content'],
        ]);
        
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segments.show', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'story-content',
            ]));
        
        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'segment',
                    'data',
                    'asset_id',
                    'asset_slug',
                ],
            ])
            ->assertJsonPath('data.segment', 'story-content')
            ->assertJsonPath('data.data.content', 'Test story content')
            ->assertJsonPath('data.asset_id', $this->asset->id);
    }

    #[Test]
    public function it_can_show_segment_with_language_parameter(): void
    {
        $enContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'en-EN',
        ]);
        
        $deContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'de-DE',
        ]);
        
        AssetContentSegment::factory()->create([
            'asset_content_id' => $enContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'English content'],
        ]);
        
        AssetContentSegment::factory()->create([
            'asset_content_id' => $deContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'German content'],
        ]);
        
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segments.show', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'story-content',
                'language' => 'de-DE',
            ]));
        
        $response->assertOk()
            ->assertJsonPath('data.data.content', 'German content');
    }

    #[Test]
    public function it_returns_404_for_non_existent_segment(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segments.show', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'non-existent',
            ]));
        
        $response->assertNotFound();
    }

    #[Test]
    public function it_can_update_existing_segment(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Original content'],
        ]);
        
        $updateData = [
            'content' => 'Updated content',
            'metadata' => [
                'author' => 'John Doe',
                'version' => 2,
            ],
        ];
        
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segments.update', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'story-content',
            ]), $updateData);
        
        $response->assertOk()
            ->assertJsonPath('data.data.content', 'Updated content')
            ->assertJsonPath('data.data.metadata.author', 'John Doe');
        
        $this->assertDatabaseHas('asset_content_segments', [
            'id' => $segment->id,
            'data' => json_encode($updateData),
        ]);
    }

    #[Test]
    public function it_can_update_segment_with_language_parameter(): void
    {
        $deContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'de-DE',
        ]);
        
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $deContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'German content'],
        ]);
        
        $updateData = ['content' => 'Updated German content'];
        
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segments.update', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'story-content',
                'language' => 'de-DE',
            ]), $updateData);
        
        $response->assertOk()
            ->assertJsonPath('data.data.content', 'Updated German content');
    }

    #[Test]
    public function it_returns_404_when_updating_non_existent_segment(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segments.update', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'non-existent',
            ]), ['content' => 'Updated content']);
        
        $response->assertNotFound();
    }

    #[Test]
    public function it_requires_authentication(): void
    {
        $response = $this->getJson(route('betterflow.v1.editorials.assets.segments.show', [
            'editorial' => $this->editorial->id,
            'asset' => $this->asset->id,
            'segment' => 'story-content',
        ]));
        
        $response->assertUnauthorized();
    }

    #[Test]
    public function it_validates_editorial_asset_relationship(): void
    {
        $otherEditorial = Editorial::factory()->create();
        $otherAsset = Asset::factory()->create(['editorial_id' => $otherEditorial->id]);
        
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segments.show', [
                'editorial' => $this->editorial->id,
                'asset' => $otherAsset->id,
                'segment' => 'story-content',
            ]));
        
        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_empty_data_gracefully(): void
    {
        $segment = AssetContentSegment::factory()->withEmptyData()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
        ]);
        
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segments.show', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'story-content',
            ]));
        
        $response->assertOk()
            ->assertJsonPath('data.data', null);
    }

    #[Test]
    public function it_can_update_segment_with_null_data(): void
    {
        $segment = AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Original content'],
        ]);
        
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segments.update', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'segment' => 'story-content',
            ]), null);
        
        $response->assertOk()
            ->assertJsonPath('data.data', null);
        
        $this->assertDatabaseHas('asset_content_segments', [
            'id' => $segment->id,
            'data' => null,
        ]);
    }

    #[Test]
    public function it_supports_all_segment_types(): void
    {
        $segmentTypes = [
            'story-content',
            'photo',
            'workshop',
            'plan',
            'final-illustrations',
            'graphics',
            'research',
            'sketches',
            'fact-content',
            'uploads',
            'research-links',
            'sources',
            'captions',
        ];
        
        foreach ($segmentTypes as $segmentType) {
            $segment = AssetContentSegment::factory()->create([
                'asset_content_id' => $this->assetContent->id,
                'segment' => $segmentType,
                'data' => ['content' => "Test {$segmentType} content"],
            ]);
            
            $response = $this->actingAs($this->user)
                ->getJson(route('betterflow.v1.editorials.assets.segments.show', [
                    'editorial' => $this->editorial->id,
                    'asset' => $this->asset->id,
                    'segment' => $segmentType,
                ]));
            
            $response->assertOk()
                ->assertJsonPath('data.segment', $segmentType);
        }
    }
}