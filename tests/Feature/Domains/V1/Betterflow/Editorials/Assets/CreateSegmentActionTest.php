<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Assets;

use App\Domains\Betterflow\V1\Editorials\Assets\Actions\CreateSegment;
use App\Domains\Betterflow\V1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CreateSegmentActionTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Editorial $editorial;
    protected Asset $asset;
    protected AssetContent $assetContent;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->seed([
            'RoleSeeder',
            'PermissionSeeder',
        ]);
        
        $this->user = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
        $this->asset = Asset::factory()->create(['editorial_id' => $this->editorial->id]);
        $this->assetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'en-EN',
        ]);
    }

    #[Test]
    public function it_can_create_new_segment(): void
    {
        $segmentData = [
            'content' => 'Test story content',
            'metadata' => [
                'author' => 'John Doe',
                'version' => 1,
            ],
        ];
        
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
            ]), [
                'segment' => 'story-content',
                'data' => $segmentData,
            ]);
        
        $response->assertCreated()
            ->assertJsonStructure([
                'data' => [
                    'segment',
                    'data',
                    'asset_id',
                    'asset_slug',
                ],
            ])
            ->assertJsonPath('data.segment', 'story-content')
            ->assertJsonPath('data.data.content', 'Test story content')
            ->assertJsonPath('data.data.metadata.author', 'John Doe');
        
        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => json_encode($segmentData),
        ]);
    }

    #[Test]
    public function it_can_create_segment_with_specific_language(): void
    {
        $deContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language' => 'de-DE',
        ]);
        
        $segmentData = ['content' => 'German content'];
        
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
                'language' => 'de-DE',
            ]), [
                'segment' => 'story-content',
                'data' => $segmentData,
            ]);
        
        $response->assertCreated()
            ->assertJsonPath('data.data.content', 'German content');
        
        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $deContent->id,
            'segment' => 'story-content',
            'data' => json_encode($segmentData),
        ]);
    }

    #[Test]
    public function it_handles_duplicate_segment_creation_gracefully(): void
    {
        AssetContentSegment::factory()->create([
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => ['content' => 'Original content'],
        ]);
        
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
            ]), [
                'segment' => 'story-content',
                'data' => ['content' => 'Duplicate content'],
            ]);
        
        $response->assertStatus(409);
        
        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => json_encode(['content' => 'Original content']),
        ]);
    }

    #[Test]
    public function it_can_create_segment_with_null_data(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
            ]), [
                'segment' => 'story-content',
                'data' => null,
            ]);
        
        $response->assertCreated()
            ->assertJsonPath('data.data', null);
        
        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'story-content',
            'data' => null,
        ]);
    }

    #[Test]
    public function it_requires_segment_parameter(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
            ]), [
                'data' => ['content' => 'Test content'],
            ]);
        
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['segment']);
    }

    #[Test]
    public function it_validates_segment_type(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
            ]), [
                'segment' => 'invalid-segment-type',
                'data' => ['content' => 'Test content'],
            ]);
        
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['segment']);
    }

    #[Test]
    public function it_can_create_multiple_segment_types(): void
    {
        $segmentTypes = [
            'story-content' => ['content' => 'Story content'],
            'photo' => ['images' => [['url' => 'https://example.com/image.jpg']]],
            'research' => ['notes' => 'Research notes'],
            'workshop' => ['materials' => 'Workshop materials'],
        ];
        
        foreach ($segmentTypes as $segmentType => $data) {
            $response = $this->actingAs($this->user)
                ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                    'editorial' => $this->editorial->id,
                    'asset' => $this->asset->id,
                ]), [
                    'segment' => $segmentType,
                    'data' => $data,
                ]);
            
            $response->assertCreated()
                ->assertJsonPath('data.segment', $segmentType);
            
            $this->assertDatabaseHas('asset_content_segments', [
                'asset_content_id' => $this->assetContent->id,
                'segment' => $segmentType,
                'data' => json_encode($data),
            ]);
        }
    }

    #[Test]
    public function it_requires_authentication(): void
    {
        $response = $this->postJson(route('betterflow.v1.editorials.assets.segments.store', [
            'editorial' => $this->editorial->id,
            'asset' => $this->asset->id,
        ]), [
            'segment' => 'story-content',
            'data' => ['content' => 'Test content'],
        ]);
        
        $response->assertUnauthorized();
    }

    #[Test]
    public function it_validates_editorial_asset_relationship(): void
    {
        $otherEditorial = Editorial::factory()->create();
        $otherAsset = Asset::factory()->create(['editorial_id' => $otherEditorial->id]);
        
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $otherAsset->id,
            ]), [
                'segment' => 'story-content',
                'data' => ['content' => 'Test content'],
            ]);
        
        $response->assertNotFound();
    }

    #[Test]
    public function it_creates_asset_content_if_not_exists(): void
    {
        $newAsset = Asset::factory()->create(['editorial_id' => $this->editorial->id]);
        
        $this->assertDatabaseMissing('asset_contents', [
            'asset_id' => $newAsset->id,
            'language' => 'en-EN',
        ]);
        
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $newAsset->id,
            ]), [
                'segment' => 'story-content',
                'data' => ['content' => 'Test content'],
            ]);
        
        $response->assertCreated();
        
        $this->assertDatabaseHas('asset_contents', [
            'asset_id' => $newAsset->id,
            'language' => 'en-EN',
        ]);
    }

    #[Test]
    public function it_returns_proper_resource_structure(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->id,
                'asset' => $this->asset->id,
            ]), [
                'segment' => 'story-content',
                'data' => ['content' => 'Test content'],
            ]);
        
        $response->assertCreated()
            ->assertJsonStructure([
                'data' => [
                    'segment',
                    'data',
                    'asset_id',
                    'asset_slug',
                ],
            ])
            ->assertJsonMissing([
                'id',
                'asset_content_id',
                'created_at',
                'updated_at',
            ]);
    }
}