<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\Editor;
use App\Exceptions\DuplicateLanguageCodeException;
use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Exceptions;
use Illuminate\Testing\Fluent\AssertableJson;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class EditorialPackagesControllerTest extends TestCase
{
    use FastRefreshDatabase;

    /** @var Authenticatable $user */
    private Editor $user;
    private Editorial $editorial;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(PermissionsSeeder::class);
        $this->seed(RoleSeeder::class);


        $role = Role::findOrCreate('editor', 'api');
        $role->givePermissionTo(Editor::getRolePermissions(), Permission::EDIT_EDITORIAL);

        $this->user = Editor::factory()->create();
        
        $this->user->assignRole($role);
        $this->editorial = Editorial::factory()->create();
    }

    #[Test]
    public function it_can_fetch_all_packages_for_an_editorial(): void
    {
        // Create multiple packages with different languages
        $packagEN = EditorialPackage::factory()->make([
            'language_code' => 'en-EN',
            'data' => ['title' => 'English Title', 'content' => 'English content'],
        ]);

        $packageFR = EditorialPackage::factory()->make([
            'language_code' => 'fr-FR',
            'data' => ['title' => 'French Title', 'content' => 'French content'],
        ]);

        $this->editorial->packages()->saveMany([$packagEN, $packageFR]);

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.packages.index', [
                'editorial' => $this->editorial->getPublicKey(),
            ]));

        $response->assertOk()
            ->assertJsonCount(2)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'editorial_id',
                    'language',
                    'data',
                    'created_at',
                    'updated_at',
                ],
            ]);
    }

    #[Test]
    public function it_can_bulk_store_packages_for_an_editorial(): void
    {
        $this->editorial->update([
            'phase' => EditorialPhase::Editing
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.packages.bulk-store', $this->editorial->getPublicKey()), [
                'packages' => [
                    ['language_code' => 'fr-FR', 'data' => ['title' => 'Package 1']],
                    ['language_code' => 'en-EN', 'data' => ['title' => 'Package 2']]
                ]
            ]);

        $response->assertOk()
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'editorial_id',
                    'language',
                    'data'
                ]
            ]);
    }

    #[Test]
    public function it_cannot_bulk_store_duplicate_language_packages(): void
    {
        Exceptions::fake();

        $this->editorial->update([
            'phase' => EditorialPhase::Editing
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.packages.bulk-store', $this->editorial->getPublicKey()), [
                'packages' => [
                    ['language_code' => 'fr-FR', 'data' => ['title' => 'Package 1']],
                    ['language_code' => 'fr-FR', 'data' => ['title' => 'Package 2']]
                ]
            ]);

        $response->assertStatus(Response::HTTP_CONFLICT);
     
        Exceptions::assertReported(function (DuplicateLanguageCodeException $exception) {
            return $exception->getMessage() === 'Duplicate language codes in request'; 
        });
            
    }    

    #[Test]
    public function it_can_fetch_packages_by_language(): void
    {
        // Create multiple packages with different languages
        $packagEN = EditorialPackage::factory()->make([
            'language_code' => 'en-EN',
            'data' => ['title' => 'English Title', 'content' => 'English content'],
        ]);

        $packageFR = EditorialPackage::factory()->make([
            'language_code' => 'fr-FR',
            'data' => ['title' => 'French Title', 'content' => 'French content'],
        ]);

        $this->editorial->packages()->saveMany([$packagEN, $packageFR]);

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.packages.index', [
                'editorial' => $this->editorial->getPublicKey(),
                'language' => 'fr-FR',
            ]));

        $response->assertOk()
            ->assertJsonCount(1)
            ->assertJson(
                fn(AssertableJson $json) =>
                $json->first(
                    fn(AssertableJson $json) =>
                    $json->where('language.code', 'fr-FR')
                        ->etc()
                )
            );
    }

    #[Test]
    public function it_can_store_a_package_with_language(): void
    {
        $packageData = [
            'language_code' => 'es-ES',
            'data' => ['title' => 'Spanish Title', 'content' => 'Spanish content'],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(
                route('betterflow.v1.editorials.packages.store', [
                    'editorial' => $this->editorial->getPublicKey(),
                ]),
                $packageData
            );

        $response->assertCreated()
            ->assertJsonStructure([
                'id',
                'editorial_id',
                'language',
                'data',
                'created_at',
                'updated_at',
            ])
            ->assertJson(
                fn(AssertableJson $json) =>
                $json->where('language.code', 'es-ES')
                    ->etc()
            );

        $this->assertDatabaseHas('editorial_packages', [
            'editorial_id' => $this->editorial->getKey(),
            'language_code' => 'es-ES',
        ]);
    }

    #[Test]
    public function it_cannot_store_duplicate_language_packages(): void
    {
        // Create an existing package
        EditorialPackage::factory()->create([
            'editorial_id' => $this->editorial->getKey(),
            'language_code' => 'en-EN',
        ]);

        // Try to create another package with the same language
        $packageData = [
            'language_code' => 'en-EN',
            'data' => ['title' => 'Another English Title', 'content' => 'Another English content'],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(
                route('betterflow.v1.editorials.packages.store', [
                    'editorial' => $this->editorial->getPublicKey(),
                ]),
                $packageData
            );

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
            ->assertJsonValidationErrors(['language_code']);
    }

    #[Test]
    public function it_can_update_a_package_by_language(): void
    {
        // Create an existing package
        $package = $this->editorial->packages()->create(EditorialPackage::factory()->make([
            'language_code' => 'en-EN',
            'data' => ['title' => 'Original Title', 'content' => 'Original content'],
        ])->toArray());

        // Update data
        $updateData = [
            'data' => ['title' => 'Updated Title', 'content' => 'Updated content'],
        ];

        $response = $this->actingAs($this->user)
            ->putJson(
                route('betterflow.v1.editorials.packages.update', [
                    'editorial' => $this->editorial->getPublicKey(),
                    'package' => $package->getLanguageCode(),
                ]),
                $updateData
            );

        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'editorial_id',
                'language',
                'language',
                'data',
                'created_at',
                'updated_at',
            ])
            ->assertJson(
                fn(AssertableJson $json) =>
                $json->where('language.code', 'en-EN')
                    ->where('data', ['title' => 'Updated Title', 'content' => 'Updated content'])
                    ->etc()
            );

        $this->assertDatabaseHas('editorial_packages', [
            'id' => $package->getKey(),
            'editorial_id' => $this->editorial->getKey(),
            'language_code' => 'en-EN',
        ]);
    }

    #[Test]
    public function it_can_update_a_package_language(): void
    {
        $this->markTestSkipped('Cannot update a package language after it is creeted');
        // Create an existing package
        // $package = EditorialPackage::factory()->create([
        //     'editorial_id' => $this->editorial->getKey(),
        //     'language_code' => 'en-EN',
        //     'data' => ['title' => 'Original Title', 'content' => 'Original content'],
        // ]);

        // // Update language
        // $updateData = [
        //     'language_code' => 'de-DE',
        // ];

        // $response = $this->actingAs($this->user)
        //     ->putJson(
        //         route('betterflow.v1.editorials.packages.update', [
        //             'editorial' => $this->editorial->getPublicKey(),
        //             'package' => $package->getLanguageCode(),
        //         ]),
        //         $updateData
        //     );

        // $response->assertOk()
        //     ->assertJsonStructure([
        //         'id',
        //         'editorial_id',
        //         'language_code',
        //         'language',
        //         'data',
        //         'created_at',
        //         'updated_at',
        //     ])
        //     ->assertJson(
        //         fn(AssertableJson $json) =>
        //         $json->where('language_code', 'de-DE')
        //             ->etc()
        //     );

        // $this->assertDatabaseHas('editorial_packages', [
        //     'id' => $package->getKey(),
        //     'editorial_id' => $this->editorial->getKey(),
        //     'language_code' => 'de-DE',
        // ]);
    }

    #[Test]
    public function it_cannot_update_to_duplicate_languages(): void
    {
        $this->markTestSkipped('Cannot update a package language after it is creeted');
        // Create two packages with different languages
        EditorialPackage::factory()->create([
            'editorial_id' => $this->editorial->getKey(),
            'language_code' => 'en-EN',
        ]);

        $frenchPackage = EditorialPackage::factory()->create([
            'editorial_id' => $this->editorial->getKey(),
            'language_code' => 'fr-FR',
        ]);

        // Try to update the French package to English (which would create a duplicate)
        $updateData = [
            'language_code' => 'en-EN',
        ];

        $response = $this->actingAs($this->user)
            ->putJson(
                route('betterflow.v1.editorials.packages.update', [
                    'editorial' => $this->editorial->getPublicKey(),
                    'package' => $frenchPackage->getPublicKey(),
                ]),
                $updateData
            );

        $response->assertStatus(Response::HTTP_CONFLICT)
            ->assertJsonValidationErrors(['language_code']);
    }

    #[Test]
    public function it_can_store_attachments_for_package(): void
    {
        $this->actingAs($this->user);
        
        $package = $this->editorial->packages()->create([
            'language_code' => 'en-EN',
            'data' => []
        ]);

        $response = $this->postJson(route('betterflow.v1.editorials.packages.attachments.store', [
            'editorial' => $this->editorial,
            'package' => 'en-EN'
        ]), [
            'attachments' => [
                UploadedFile::fake()->create('document1.pdf', 100),
                UploadedFile::fake()->create('document2.pdf', 100)
            ]
        ]);

        $response->assertStatus(Response::HTTP_CREATED);

        $this->assertDatabaseHas('media', [
            'model_type' => 'editorial_package',
            'model_id' => $package->getKey(),
        ]);

        $this->assertCount(2, $package->getMedia('package-attachments'));
    }
}
