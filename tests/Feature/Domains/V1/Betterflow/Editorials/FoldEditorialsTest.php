<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials;

use PHPUnit\Framework\Attributes\Test;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Tests\TestCase;

class FoldEditorialsTest extends TestCase
{
    protected Editorial $editorial;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->editorial = Editorial::factory()->create();
        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_can_fold_an_editorial()
    {
        $this->actingAs($this->user);

        $response = $this->putJson(route('betterflow.v1.editorials.fold', $this->editorial), [
            'folded_by' => $this->user->getKey(),
        ]);

        $response->assertOk();

        $this->assertDatabaseHas('editorials', [
            'id' => $this->editorial->getKey(),
            'public_id' => $this->editorial->getPublic<PERSON>ey(),
            'deleted_at' => now(),
        ]);
    }

    #[Test]
    public function it_can_fold_multiple_editorials()
    {
        $editorial2 = Editorial::factory()->create();

        $this->actingAs($this->user);

        $response = $this->putJson(route('betterflow.v1.editorials.bulk-fold'), [
            'editorials' => [
                $this->editorial->getKey(),
                $editorial2->getKey(),
            ],
            'folded_by' => $this->user->getKey(),
        ]);

        $response->assertOk();

        $this->assertDatabaseHas('editorials', [
            'id' => $this->editorial->getKey(),
            'public_id' => $this->editorial->getPublicKey(),
            'deleted_at' => now(),
        ]);

        $this->assertDatabaseHas('editorials', [
            'id' => $editorial2->getKey(),
            'public_id' => $editorial2->getPublicKey(),
            'deleted_at' => now(),
        ]);
    }
}
