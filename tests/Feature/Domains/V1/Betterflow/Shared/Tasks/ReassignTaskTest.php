<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Tasks;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\UserReassigned;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class ReassignTaskTest extends TestCase
{
    use FastRefreshDatabase;
    use WithFaker;

    protected function createTask(array $attributes = []): Task
    {
        $pitch = Pitch::factory()->create([
            'public_id' => str()->orderedUuid(),
        ]);

        $user = User::factory()->create();

        return Task::factory()->create(array_merge([
            'taskable_type' => get_class($pitch),
            'taskable_id' => $pitch->id,
            'user_id' => $user->id,
            'type' => fake()->randomElement(PitchTaskType::cases()),
            'status' => TaskStatus::Pending,
        ], $attributes));
    }

    #[Test]
    public function it_reassignes_a_task_to_a_new_user_successfully_with_user_id(): void
    {
        // Arrange
        Event::fake();
        $task = $this->createTask();
        $newUser = User::factory()->create();
        $notes = 'Task reassigned to a different user';

        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.reassign', $task), [
            'user_id' => $newUser->id,
            'notes' => $notes,
        ]);

        // Assert
        $response->assertOk();
        $this->assertEquals($newUser->id, $task->fresh()->user_id);
        $this->assertEquals($notes, $task->fresh()->notes);
        Event::assertDispatched(UserReassigned::class);

        $this->assertDatabaseHas('tasks', [
            'id' => $task->id,
            'user_id' => $newUser->id,
            'notes' => $notes,
        ]);
    }

    #[Test]
    public function it_throws_exception_when_reassigning_to_same_user_with_same_user_id(): void
    {
        // Arrange
        $task = $this->createTask();
        $sameUser = $task->user;
        $notes = 'Task reassigned to a different user';

        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.reassign', $task), [
            'user_id' => $sameUser->id,
            'notes' => $notes,
        ]);

        // Assert
        $response->assertStatus(Response::HTTP_CONFLICT);
    }
}
