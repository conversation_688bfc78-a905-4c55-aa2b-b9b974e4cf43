<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Tasks;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class IncompleteTaskTest extends TestCase
{
    use FastRefreshDatabase;
    use WithFaker;

    protected function createCompletedTask(array $attributes = []): Task
    {
        $pitch = Pitch::factory()->create([
            'public_id' => str()->orderedUuid(),
        ]);

        return Task::factory()->create(array_merge([
            'taskable_type' => get_class($pitch),
            'taskable_id' => $pitch->id,
            'type' => fake()->randomElement(PitchTaskType::cases()),
            'status' => TaskStatus::Completed,
            'completed_at' => now(),
            'progress' => 100,
        ], $attributes));
    }

    #[Test]
    public function it_marks_a_task_as_incomplete_successfully_with_notes(): void
    {
        // Arrange
        $task = $this->createCompletedTask();
        $notes = 'Task marked as incomplete';

        // Act
        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.incomplete', $task), [
            'notes' => $notes,
        ]);

        // Assert
        $response->assertOk();
        $this->assertEquals(TaskStatus::Pending, $task->fresh()->status);
        $this->assertNull($task->fresh()->completed_at);
        $this->assertEquals($notes, $task->fresh()->notes);
        $this->assertEquals(0, $task->fresh()->progress);
    }

    #[Test]
    public function it_throws_exception_when_marking_incomplete_a_pending_task_with_notes(): void
    {
        // Arrange
        $task = Task::factory()->create([
            'taskable_type' => 'pitch',
            'taskable_id' => Pitch::factory(),
            'status' => TaskStatus::Pending,
        ]);

        // Act
        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.incomplete', $task), [
            'notes' => 'Notes',
        ]);

        // Assert
        $response->assertStatus(Response::HTTP_CONFLICT);
    }
}
