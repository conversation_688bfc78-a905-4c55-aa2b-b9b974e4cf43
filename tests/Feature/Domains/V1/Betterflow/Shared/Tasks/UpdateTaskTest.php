<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Tasks;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskProgressUpdated;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class UpdateTaskTest extends TestCase
{
    use FastRefreshDatabase;
    use WithFaker;

    protected function createTask(array $attributes = []): Task
    {
        $pitch = Pitch::factory()->create([
            'public_id' => str()->orderedUuid(),
        ]);

        return Task::factory()->create(array_merge([
            'taskable_type' => get_class($pitch),
            'taskable_id' => $pitch->id,
            'type' => fake()->randomElement(PitchTaskType::cases()),
            'status' => TaskStatus::Pending,
        ], $attributes));
    }

    #[Test]
    public function it_completes_a_task_when_complete_is_passed_with_true(): void
    {
        // Arrange
        Event::fake();
        $task = $this->createTask();

        // Act
        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.update', $task), [
            'complete' => true,
        ]);

        // Assert
        $response->assertOk();
        $this->assertEquals(TaskStatus::Completed, $task->fresh()->status);
        $this->assertNotNull($task->fresh()->completed_at);
        $this->assertEquals(100, $task->fresh()->progress);
        $this->assertEquals('', $task->fresh()->notes);
        Event::assertDispatched(TaskCompleted::class);
    }

    #[Test]
    public function it_updates_task_progress_when_progress_is_passed(): void
    {
        // Arrange
        Event::fake();
        $task = $this->createTask();

        // Act
        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.update', $task), [
            'progress' => 50,
        ]);

        // Assert
        $response->assertOk();
        $this->assertEquals(50, $task->fresh()->progress);
        $this->assertEquals(TaskStatus::Pending, $task->fresh()->status);
        $this->assertNull($task->fresh()->completed_at);
        Event::assertDispatched(TaskProgressUpdated::class);
    }

    #[Test]
    public function it_completes_task_when_progress_is_passed_with_100(): void
    {
        // Arrange
        Event::fake([TaskCompleted::class]);
        $task = $this->createTask();

        // Act
        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.update', $task), [
            'progress' => 100,
        ]);

        // Assert
        $response->assertOk();
        $this->assertEquals(100, $task->fresh()->progress);
        $this->assertEquals(TaskStatus::Completed, $task->fresh()->status);
        $this->assertNotNull($task->fresh()->completed_at);
        $this->assertEquals('', $task->fresh()->notes);
        Event::assertDispatched(TaskCompleted::class);
    }

    #[Test]
    public function it_throws_exception_when_updating_completed_task_with_progress_is_passed(): void
    {
        // Arrange
        $task = $this->createTask([
            'status' => TaskStatus::Completed,
            'completed_at' => now(),
            'progress' => 100,
        ]);

        // Act
        $response = $this->actingAs($task->user)->putJson(route('betterflow.v1.common.tasks.update', $task), [
            'progress' => 50,
        ]);

        // Assert
        $response->assertStatus(Response::HTTP_CONFLICT);
    }
}
