<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Notifications;

use Tests\TestCase;
use App\Domains\Users\Models\User;
use PHPUnit\Framework\Attributes\Test;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Notifications\AssetAssistCompletedNotification;

class NotificationsTest extends TestCase
{
    #[Test]
    public function it_returns_unread_notifications()
    {
        // Arrange
        $user = User::factory()->create([
            'type' => 'editor'
        ]);

        $asset = Asset::factory()->create();
        $task = Task::factory()->create([
            'taskable_id' => $asset->id,
            'taskable_type' => Asset::class,
            'user_id' => $user->getKey(),
        ]);

        $user->notify(new AssetAssistCompletedNotification($asset, 'title', 'message', $task));

        // Act
        $response = $this->actingAs($user)->get(route('notifications.index'));

        // Assert
        $response->assertStatus(200);

        $response->assertJsonPath('0.read', false);
    }
}
