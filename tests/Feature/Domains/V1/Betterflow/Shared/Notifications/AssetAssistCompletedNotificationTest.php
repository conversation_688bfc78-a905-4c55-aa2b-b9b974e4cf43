<?php

namespace Tests\Feature\Domains\V1\Betterflow\Shared\Notifications;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Betterflow\V1\Editorials\Notifications\AssetAssistCompletedNotification;
use App\Domains\Users\Models\User;
use Tests\TestCase;

class AssetAssistCompletedNotificationTest extends TestCase
{
    public function test_asset_assist_completed_notification()
    {
        // Arrange
        $user = User::factory()->create();
        $asset = Asset::factory()->create();
        $task = Task::factory()->create([
            'taskable_id' => $asset->id,
            'taskable_type' => Asset::class,
            'user_id' => $user->id,
        ]);

        $user->notify(new AssetAssistCompletedNotification($asset, 'title', 'message', $task));

        // Assert
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $user->getKey(),
            'notifiable_type' => $user->getMorphClass(),
            'type' => 'assist_completed',
        ]);

        $notification = $user->unreadNotifications()->first();

        $this->assertEquals('assist_completed', $notification->type);
        $this->assertEquals($user->id, data_get($notification->data, 'who_did_it.id'));
        $this->assertEquals($user->name, data_get($notification->data, 'who_did_it.name'));

        $this->assertEquals($task->getResourceKey(), data_get($notification->data, 'resources.task.id'));
        $this->assertEquals($asset->getResourceKey(), data_get($notification->data, 'resources.asset.id'));
        $this->assertEquals(config('lighthouse.betterflow.namespace'), data_get($notification->data, 'from_where'));
    }
}
