<?php

namespace Tests\Feature\Domains\V1\Betterflow;

use Tests\TestCase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use PHPUnit\Framework\Attributes\Test;
use App\Domains\Users\Models\Editor;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;

class CompassEditorialsDashboardControllerTest extends TestCase
{
    use FastRefreshDatabase;

    #[Test]
    public function it_can_get_editorials_for_user_dashboard(): void
    {
        $user = User::factory()->hasTasks(3)->create();
        $editorials = Editorial::factory(3)->create([
            'reporter_id' => $user->getKey(),
        ]);
        
        $toplineEditor = Editor::factory()->create([]);

        $editorials->each(function ($editorial) use ($toplineEditor, $user) {
            // $editorial->assignUser($user, 'reporter');
            $editorial->assignUser($toplineEditor, 'topline_editor');
            $editorial->save();
        });

        $response = $this->actingAs($user)->getJson(route('betterflow.v1.dashboard.editorials'));

        $response->assertOk();

        $editorialsFromResponse = $response->collect();

        $editorial = $editorialsFromResponse->last();

        $this->assertTrue(data_get($editorial, 'user_roles.is_reporter'));
        $this->assertFalse(data_get($editorial, 'user_roles.has_editorial_tasks'));
        $this->assertFalse(data_get($editorial, 'user_roles.is_topline_editor'));
        $this->assertEquals($toplineEditor->getKey(), data_get($editorial, 'topline_editor.id'));

    }
}
