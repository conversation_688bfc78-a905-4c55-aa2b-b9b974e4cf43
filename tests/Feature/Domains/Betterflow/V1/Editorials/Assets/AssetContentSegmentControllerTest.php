<?php

declare(strict_types=1);

namespace Tests\Feature\Domains\Betterflow\V1\Editorials\Assets;

use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\Editor;

use Database\Seeders\Crud\RoleSeeder;
use Database\Seeders\PermissionsSeeder;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

final class AssetContentSegmentControllerTest extends TestCase
{
    use FastRefreshDatabase;

    private Editor $user;
    private Editorial $editorial;
    private Asset $asset;
    private AssetContent $assetContent;
    private AssetContentSegment $segment;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(PermissionsSeeder::class);
        $this->seed(RoleSeeder::class);

        $role = Role::findOrCreate('editor', 'api');
        $role->givePermissionTo([
            Permission::VIEW_EDITORIAL,
            Permission::EDIT_EDITORIAL,
            Permission::UPDATE_ASSET,
        ]);

        $this->user = Editor::factory()->create();
        $this->user->assignRole($role);

        $this->editorial = Editorial::factory()->create();

        // Create asset manually to avoid factory issues
        $this->asset = new Asset([
            'name' => 'Test Asset',
            'editorial_id' => $this->editorial->id,
            'public_id' => fake()->uuid(),
            'url' => fake()->url(),
            'type' => 'reporting',
            'attributes' => '{}',
            'slug' => 'test-asset',
        ]);
        $this->asset->save();

        $this->assetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'en-EN',
        ]);
        $this->segment = AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('test-segment')
            ->withData([
                'title' => 'Test Segment Title',
                'content' => 'Test segment content',
                'metadata' => ['author' => 'Test Author'],
            ])
            ->create();
    }

    #[Test]
    public function it_can_show_a_segment(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'asset_id',
                'asset',
                'segment',
                'data',
            ])
            ->assertJsonPath('segment', 'test-segment')
            ->assertJsonPath('data.title', 'Test Segment Title')
            ->assertJsonPath('data.content', 'Test segment content')
            ->assertJsonPath('data.metadata.author', 'Test Author')
            ->assertJsonPath('asset', $this->asset->slug);
    }

    #[Test]
    public function it_can_show_segment_for_specific_language(): void
    {
        // Create Spanish asset content and segment
        $spanishAssetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'es-ES',
        ]);
        
        AssetContentSegment::factory()
            ->forAssetContent($spanishAssetContent)
            ->withSegment('test-segment')
            ->withData([
                'title' => 'Título en Español',
                'content' => 'Contenido en español',
            ])
            ->create();

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
                'language' => 'es-ES',
            ]));

        $response->assertOk()
            ->assertJsonPath('segment', 'test-segment')
            ->assertJsonPath('data.title', 'Título en Español')
            ->assertJsonPath('data.content', 'Contenido en español');
    }

    #[Test]
    public function it_defaults_to_english_language_when_not_specified(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]));

        $response->assertOk()
            ->assertJsonPath('data.title', 'Test Segment Title'); // English content
    }

    #[Test]
    public function it_returns_404_when_segment_does_not_exist(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'non-existent-segment',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_returns_404_when_asset_content_for_language_does_not_exist(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
                'language' => 'fr-FR', // French content doesn't exist
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_can_update_a_segment(): void
    {
        $updatedData = [
            'title' => 'Updated Segment Title',
            'content' => 'Updated segment content',
            'metadata' => [
                'author' => 'Updated Author',
                'updated_at' => now()->toISOString(),
            ],
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => $updatedData,
            ]);

        $response->assertOk()
            ->assertJsonPath('segment', 'test-segment')
            ->assertJsonPath('data.title', 'Updated Segment Title')
            ->assertJsonPath('data.content', 'Updated segment content')
            ->assertJsonPath('data.metadata.author', 'Updated Author');

        $this->segment->refresh();
        $this->assertEquals($updatedData, $this->segment->data);
    }

    #[Test]
    public function it_can_update_segment_for_specific_language(): void
    {
        // Create Spanish asset content and segment
        $spanishAssetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'es-ES',
        ]);
        
        $spanishSegment = AssetContentSegment::factory()
            ->forAssetContent($spanishAssetContent)
            ->withSegment('test-segment')
            ->withData(['title' => 'Título Original'])
            ->create();

        $updatedData = [
            'title' => 'Título Actualizado',
            'content' => 'Contenido actualizado',
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
                'language' => 'es-ES',
            ]), [
                'data' => $updatedData,
            ]);

        $response->assertOk()
            ->assertJsonPath('data.title', 'Título Actualizado')
            ->assertJsonPath('data.content', 'Contenido actualizado');

        $spanishSegment->refresh();
        $this->assertEquals($updatedData, $spanishSegment->data);
    }

    #[Test]
    public function it_can_update_segment_with_partial_data(): void
    {
        $partialUpdate = [
            'title' => 'Partially Updated Title',
            'new_field' => 'New field value',
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => $partialUpdate,
            ]);

        $response->assertOk()
            ->assertJsonPath('data.title', 'Partially Updated Title')
            ->assertJsonPath('data.new_field', 'New field value');

        $this->segment->refresh();
        $this->assertEquals($partialUpdate, $this->segment->data);
    }

    #[Test]
    public function it_can_clear_segment_data_with_empty_update(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => [],
            ]);

        $response->assertOk()
            ->assertJsonPath('data', []);

        $this->segment->refresh();
        $this->assertEquals([], $this->segment->data);
    }

    #[Test]
    public function it_can_update_segment_with_complex_nested_data(): void
    {
        $complexData = [
            'title' => 'Complex Updated Title',
            'sections' => [
                [
                    'type' => 'text',
                    'content' => 'Updated section content',
                    'metadata' => ['position' => 1, 'updated' => true],
                ],
                [
                    'type' => 'image',
                    'url' => 'https://example.com/updated-image.jpg',
                    'alt' => 'Updated image',
                    'metadata' => ['position' => 2],
                ],
            ],
            'settings' => [
                'visible' => false,
                'priority' => 'low',
                'tags' => ['updated', 'modified'],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => $complexData,
            ]);

        $response->assertOk()
            ->assertJsonPath('data.title', 'Complex Updated Title')
            ->assertJsonPath('data.sections.0.type', 'text')
            ->assertJsonPath('data.sections.0.metadata.updated', true)
            ->assertJsonPath('data.sections.1.url', 'https://example.com/updated-image.jpg')
            ->assertJsonPath('data.settings.visible', false)
            ->assertJsonPath('data.settings.tags.0', 'updated');

        $this->segment->refresh();
        $this->assertEquals($complexData, $this->segment->data);
    }

    #[Test]
    public function it_returns_404_when_updating_non_existent_segment(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'non-existent-segment',
            ]), [
                'data' => ['title' => 'Updated Title'],
            ]);

        $response->assertNotFound();
    }

    #[Test]
    public function it_returns_404_when_updating_segment_for_non_existent_language(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
                'language' => 'fr-FR', // French content doesn't exist
            ]), [
                'data' => ['title' => 'Updated Title'],
            ]);

        $response->assertNotFound();
    }

    #[Test]
    public function it_requires_authentication_for_show_endpoint(): void
    {
        $response = $this->getJson(route('betterflow.v1.editorials.assets.segment', [
            'editorial' => $this->editorial->getPublicKey(),
            'asset' => $this->asset->slug,
            'segment' => 'test-segment',
        ]));

        $response->assertUnauthorized();
    }

    #[Test]
    public function it_requires_authentication_for_update_endpoint(): void
    {
        $response = $this->putJson(route('betterflow.v1.editorials.assets.segment.update', [
            'editorial' => $this->editorial->getPublicKey(),
            'asset' => $this->asset->slug,
            'segment' => 'test-segment',
        ]), [
            'data' => ['title' => 'Updated Title'],
        ]);

        $response->assertUnauthorized();
    }

    #[Test]
    public function it_handles_invalid_editorial_public_key(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => 'invalid-uuid',
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_invalid_asset_slug(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => 'invalid-slug',
                'segment' => 'test-segment',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_asset_not_belonging_to_editorial(): void
    {
        $otherEditorial = Editorial::factory()->create();
        $otherAsset = Asset::factory()->create([
            'editorial_id' => $otherEditorial->id,
            'slug' => 'other-asset',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $otherAsset->slug,
                'segment' => 'test-segment',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_malformed_json_in_update_request(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => 'invalid-json-structure',
            ]);

        // The controller should handle this gracefully
        $response->assertOk();

        $this->segment->refresh();
        $this->assertEquals('invalid-json-structure', $this->segment->data);
    }

    #[Test]
    public function it_handles_null_data_in_update_request(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => null,
            ]);

        $response->assertOk()
            ->assertJsonPath('data', null);

        $this->segment->refresh();
        $this->assertNull($this->segment->data);
    }

    #[Test]
    public function it_handles_missing_data_field_in_update_request(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), []);

        $response->assertOk()
            ->assertJsonPath('data', null);

        $this->segment->refresh();
        $this->assertNull($this->segment->data);
    }

    #[Test]
    public function it_returns_fresh_data_after_update(): void
    {
        $originalData = $this->segment->data;

        $updatedData = [
            'title' => 'Fresh Data Title',
            'timestamp' => now()->toISOString(),
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => $updatedData,
            ]);

        $response->assertOk()
            ->assertJsonPath('data.title', 'Fresh Data Title');

        // Verify the data was actually updated in the database
        $this->assertDatabaseHas('asset_content_segments', [
            'id' => $this->segment->id,
        ]);

        $freshSegment = AssetContentSegment::find($this->segment->id);
        $this->assertEquals($updatedData, $freshSegment->data);
        $this->assertNotEquals($originalData, $freshSegment->data);
    }

    #[Test]
    public function it_preserves_segment_and_asset_content_relationship_after_update(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => ['title' => 'Relationship Test'],
            ]);

        $response->assertOk()
            ->assertJsonPath('segment', 'test-segment')
            ->assertJsonPath('asset', $this->asset->slug);

        $this->segment->refresh();
        $this->assertEquals($this->assetContent->id, $this->segment->asset_content_id);
        $this->assertEquals('test-segment', $this->segment->segment);
    }

    #[Test]
    public function it_handles_concurrent_updates_with_lock(): void
    {
        // This test verifies that the lockForUpdate() in the Asset::segment() method works
        $updatedData1 = ['title' => 'First Update', 'counter' => 1];
        $updatedData2 = ['title' => 'Second Update', 'counter' => 2];

        // First update
        $response1 = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => $updatedData1,
            ]);

        $response1->assertOk();

        // Second update
        $response2 = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'test-segment',
            ]), [
                'data' => $updatedData2,
            ]);

        $response2->assertOk()
            ->assertJsonPath('data.title', 'Second Update')
            ->assertJsonPath('data.counter', 2);

        $this->segment->refresh();
        $this->assertEquals($updatedData2, $this->segment->data);
    }

    #[Test]
    public function it_handles_different_segment_names_correctly(): void
    {
        // Create additional segments
        AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('intro')
            ->withData(['title' => 'Intro Title'])
            ->create();

        AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('conclusion')
            ->withData(['title' => 'Conclusion Title'])
            ->create();

        // Test retrieving intro segment
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'intro',
            ]));

        $response->assertOk()
            ->assertJsonPath('segment', 'intro')
            ->assertJsonPath('data.title', 'Intro Title');

        // Test retrieving conclusion segment
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'conclusion',
            ]));

        $response->assertOk()
            ->assertJsonPath('segment', 'conclusion')
            ->assertJsonPath('data.title', 'Conclusion Title');
    }

    #[Test]
    public function it_handles_special_characters_in_segment_names(): void
    {
        AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('special-segment_123')
            ->withData(['title' => 'Special Segment'])
            ->create();

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'special-segment_123',
            ]));

        $response->assertOk()
            ->assertJsonPath('segment', 'special-segment_123')
            ->assertJsonPath('data.title', 'Special Segment');
    }
}
