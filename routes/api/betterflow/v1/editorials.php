<?php

use App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset;
use App\Domains\Betterflow\V1\Editorials\Actions\FoldEditorial;
use App\Domains\Betterflow\V1\Editorials\Actions\FoldEditorials;
use App\Domains\Betterflow\V1\Editorials\Actions\MoveEditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Actions\ReassignAsset;
use App\Domains\Betterflow\V1\Editorials\Actions\ReassignEditorial;
use App\Domains\Betterflow\V1\Editorials\Actions\RequestAssetAssist;
use App\Domains\Betterflow\V1\Editorials\Actions\RequestEditorialAssist;
use App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteEditorialTask;
use App\Domains\Betterflow\V1\Editorials\Actions\Tasks\CompleteTask;
use App\Domains\Betterflow\V1\Editorials\Actions\Tasks\IncompleteTask;
use App\Domains\Betterflow\V1\Editorials\Actions\UpdateAsset;
use App\Domains\Betterflow\V1\Editorials\Actions\WatchEditorial;
use App\Domains\Betterflow\V1\Editorials\Assets\Actions\CreateSegment;
use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets\AssetContentSegmentController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets\AssetsActivitiesController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets\AssetsCommentsController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets\AssetsController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\EditorialAssignemntsController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\EditorialCommentsController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\EditorialPackagesController;
use App\Domains\Betterflow\V1\Editorials\Http\Controllers\EditorialsController;
use Illuminate\Support\Facades\Route;

Route::apiResource('', EditorialsController::class)->except('show', 'update', 'destroy');
Route::get('{editorial:public_id}', [EditorialsController::class, 'show'])->name('show')->whereUuid('editorial');
Route::put('{editorial:public_id}/assign', EditorialAssignemntsController::class)->name('assign')->whereUuid('editorial');
Route::put('{editorial:public_id}/re-assign', ReassignEditorial::class)->name('re-assign')->whereUuid('editorial');

// Fold
Route::put('{editorial:public_id}/fold', FoldEditorial::class)->name('fold')->whereUuid('editorial');
Route::put('fold/bulk', FoldEditorials::class)->name('bulk-fold');

// Move Phase
Route::put('{editorial:public_id}/move-phase', MoveEditorialPhase::class)->name('move-phase')->whereUuid('editorial');

// Watch editorial
Route::put('{editorial:public_id}/toggle-watch', WatchEditorial::class)->name('toggle-watch')->whereUuid('editorial');

// Packages
Route::apiResource('{editorial:public_id}/packages', EditorialPackagesController::class)
    ->whereUuid('editorial')
    ->where(['package' => '^[a-z]{2}(-[A-Z]{2})?$'])
    ->scoped([
        'editorial' => 'public_id',
        'package' => 'language_code',
    ]);
Route::post('{editorial:public_id}/packages/bulk', [EditorialPackagesController::class, 'bulkStore'])->name('packages.bulk-store')->whereUuid('editorial');

// Editorial Comments
Route::get('{editorial:public_id}/comments', [EditorialCommentsController::class, 'index'])->name('comments.index')->whereUuid('editorial');
Route::post('{editorial:public_id}/comments', [EditorialCommentsController::class, 'store'])->name('comments.store')->whereUuid('editorial');

// Editorial Assets
Route::get('{editorial:public_id}/assets', [AssetsController::class, 'index'])->name('assets.index')->whereUuid('editorial');
Route::get('{editorial:public_id}/assets/{asset:slug}', [AssetsController::class, 'show'])->name('assets.show')
    ->whereUuid('editorial')
    ->scopeBindings();
Route::get('{editorial:public_id}/assets/{asset:slug}/activities', AssetsActivitiesController::class)->name('assets.activities')
    ->whereUuid('editorial')
    ->scopeBindings();
Route::post('{editorial:public_id}/assets', CreateAsset::class)
    ->name('assets.store')
    ->whereUuid('editorial');
Route::put('{editorial:public_id}/assets/{asset:slug}', UpdateAsset::class)->name('assets.update')
    ->whereUuid('editorial')
    ->scopeBindings();
// TODO: Toggle asset complete?
// Route::delete('{editorial:public_id}/assets/{asset:public_id}', [AssetsController::class, 'destroy'])->name('assets.destroy')->whereUuid('editorial')->whereUuid('asset')->scopeBindings();
Route::put('{editorial:public_id}/assets/{asset:slug}/re-assign', ReassignAsset::class)->name('assets.re-assign')->scopeBindings();

// Asset Content Segments
Route::get('{editorial:public_id}/assets/{asset:slug}/{segment}', [AssetContentSegmentController::class, 'show'])->name('assets.segment')->whereUuid('editorial')->scopeBindings();
Route::put('{editorial:public_id}/assets/{asset:slug}/{segment}', [AssetContentSegmentController::class, 'update'])->name('assets.segment.update')->whereUuid('editorial')->scopeBindings();
Route::post('{editorial:public_id}/assets/{asset:slug}/segments', CreateSegment::class)->name('assets.segments.store')->whereUuid('editorial')->scopeBindings();

// Assists
// Request Assist for Editorial
Route::post('{editorial:public_id}/request-assist', RequestEditorialAssist::class)
    ->name('editorial.request-assist')
    ->whereUuid('editorial');

// Complete Editorial Assist
Route::put('{editorial:public_id}/tasks/{task:id}/complete', CompleteEditorialTask::class)->name('tasks.complete')->scopeBindings();

// Request Assist
Route::post('{editorial:public_id}/assets/{asset:slug}/request-assist', RequestAssetAssist::class)
    ->where('asset', '^[a-z]+(?:-[a-z]+)*$')
    ->name('assets.request-assist');

// Assign Task
Route::post('{editorial:public_id}/assets/{asset:slug}/assign', RequestAssetAssist::class)
    ->whereUuid('editorial')
    ->where('asset', '^[a-z0-9]+(?:-[a-z0-9]+)*$')
    ->whereIn('asset', AssetType::cases())
    ->name('assets.assign-task')->scopeBindings();

// Complete Task
Route::put('{editorial:public_id}/assets/{asset:slug}/tasks/{task:id}/complete', CompleteTask::class)
    ->whereUuid('editorial')
    ->name('assets.tasks.complete')
    ->scopeBindings();

// Complete Any Task
Route::put('tasks/{task}/complete', CompleteTask::class)->name('tasks.complete')->scopeBindings();

// Incomplete Any Task
Route::put('tasks/{task:id}/incomplete', IncompleteTask::class)->name('tasks.incomplete')->scopeBindings();

// Assets Comments
Route::get('{editorial:public_id}/assets/{asset:slug}/comments', [AssetsCommentsController::class, 'index'])->name('assets.comments.index')->whereUuid('editorial')->scopeBindings();
Route::post('{editorial:public_id}/assets/{asset:slug}/comments', [AssetsCommentsController::class, 'store'])->name('assets.comments.store')->whereUuid('editorial')->scopeBindings();
Route::post('{editorial:public_id}/assets/{asset:slug}/comments/{comment}', [AssetsCommentsController::class, 'reply'])->name('assets.comments.reply')->whereUuid('editorial')->scopeBindings();

// Attachments
Route::post('{editorial:public_id}/assets/{asset:slug}/attachments', [EditorialsController::class, 'storeAttachment'])->name('attachments.store')->whereUuid('editorial');
Route::post('{editorial:public_id}/packages/{package:language_code}/attachments', [EditorialPackagesController::class, 'storeAttachments'])->name('packages.attachments.store')->whereUuid('editorial')->scopeBindings();
