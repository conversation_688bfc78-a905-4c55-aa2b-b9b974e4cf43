<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('editorial_packages', function (Blueprint $table) {
            $table->id();
            $table->uuid('public_id');
            $table->foreignId('editorial_id')->constrained('editorials')->cascadeOnDelete();
            $table->string('language_code')->default('en-EN');
            $table->json('data')->nullable();
            
            $table->softDeletes();
            $table->timestamps();

            $table->unique(['editorial_id', 'language_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('editorial_packages');
    }
};
