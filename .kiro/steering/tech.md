# Technology Stack

## Framework & Runtime
- **<PERSON><PERSON> 12** - PHP web framework
- **PHP 8.2/8.3** - Server-side language
- **Node.js 18.20.8** - Frontend tooling (managed via Volta)

## Key Dependencies
- **Laravel Sanctum** - API authentication
- **<PERSON>vel Fortify** - Authentication scaffolding
- **Laravel Horizon** - Queue monitoring
- **Laravel Octane** - High-performance application server
- **<PERSON><PERSON> Reverb** - WebSocket server for real-time features
- **<PERSON><PERSON> Permission** - Role and permission management
- **<PERSON><PERSON>** - Data transfer objects
- **Spatie Laravel Media Library** - File management
- **Spatie <PERSON>vel Activity Log** - Audit logging
- **Laravel Actions (Lorisleiva)** - Action-based architecture
- **<PERSON>vel DDD (Lunarstorm)** - Domain-driven design structure

## Database & Storage
- **PostgreSQL** - Primary database (MySQL for tests)
- **Redis** - Caching and queue backend
- **AWS S3** - File storage via Flysystem

## Development Tools
- **<PERSON><PERSON> Pint** - Code formatting (PSR-12 + <PERSON><PERSON> preset)
- **PHPStan/Larastan** - Static analysis
- **PHPUnit** - Testing framework
- **Rector** - Code refactoring
- **Laravel Sail** - Docker development environment

## Common Commands

### Development
```bash
# Install dependencies
composer install

# Setup environment
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate:fresh --seed

# Code quality
composer lint          # Format code with Pint
composer stan          # Run static analysis
composer test          # Run tests
composer test:cover    # Run tests with coverage
```

### Deployment
```bash
# Production deployment
composer deploy

# Development deployment  
composer deploy:dev

# Staging deployment
composer deploy:s
```

### Code Quality Checks
```bash
# Check for dump statements
composer ds:check

# Run all verification checks
composer verify

# Rector dry-run
composer rector
```
