# Product Overview

Lighthouse API is a Laravel-based editorial workflow management system for GlobalPress. It manages the complete editorial lifecycle from pitch creation to publication, supporting multi-language content and collaborative workflows.

## Core Features

- **Editorial Management**: Complete editorial workflow with phases, assignments, and version control
- **Pitch System**: Story pitch creation, review, and approval workflows
- **User Management**: Role-based access control with editors, reporters, translators, and administrators
- **Multi-language Support**: Content translation and localization workflows
- **Asset Management**: Media library integration for editorial assets
- **Task Management**: Editorial task assignment and tracking
- **Real-time Features**: WebSocket support for live updates and notifications
- **Event Management**: Editorial calendar and event coordination

## Key Domains

- **Betterflow**: Core editorial workflow management
- **Users**: User management and authentication
- **Spaces**: Multi-tenant workspace organization
- **Comments**: Collaborative commenting system
- **Notifications**: Real-time notification system
