# Project Structure

## Domain-Driven Design Architecture

This project follows Domain-Driven Design (DDD) principles using the `lunarstorm/laravel-ddd` package. All business logic is organized into domains under `app/Domains/`.

## Core Directory Structure

```
app/
├── Domains/                    # Domain layer (business logic)
│   ├── Betterflow/V1/         # Core editorial workflow domain
│   │   ├── Editorials/        # Editorial management
│   │   ├── Pitches/           # Story pitch system
│   │   ├── Projects/          # Project management
│   │   ├── Admin/             # Administrative functions
│   │   └── Shared/            # Shared domain utilities
│   ├── Users/                 # User management domain
│   ├── Spaces/                # Multi-tenant workspaces
│   ├── Comments/              # Commenting system
│   ├── Notifications/         # Notification system
│   ├── Auth/                  # Authentication domain
│   └── Shared/                # Cross-domain shared code
├── Http/                      # Application layer HTTP concerns
├── Console/Commands/          # Artisan commands
└── Providers/                 # Service providers
```

## Domain Structure Pattern

Each domain follows a consistent structure:

```
Domain/
├── Actions/                   # Business actions (using Laravel Actions)
├── Data/                      # Data Transfer Objects (Spatie Laravel Data)
├── Enums/                     # Domain enumerations
├── Events/                    # Domain events
├── Exceptions/                # Domain-specific exceptions
├── Http/
│   ├── Controllers/           # HTTP controllers
│   ├── Requests/              # Form request validation
│   ├── Resources/             # API resources
│   └── Middleware/            # Domain middleware
├── Jobs/                      # Queue jobs
├── Models/                    # Eloquent models
├── Notifications/             # Domain notifications
├── Observers/                 # Model observers
├── Policies/                  # Authorization policies
├── Rules/                     # Validation rules
└── Database/
    ├── Factories/             # Model factories
    ├── Migrations/            # Database migrations
    └── Seeders/               # Database seeders
```

## Key Architectural Patterns

### Base Classes
- **Models**: Extend `App\Domains\Shared\Models\BaseDomainModel`
- **DTOs**: Extend `Spatie\LaravelData\Data`
- **Actions**: Use `lorisleiva/laravel-actions` package

### Shared Concerns
- **HasComments**: Adds commenting functionality
- **HasTasks**: Adds task management
- **HasPublicKey**: Adds public UUID identifiers
- **HasState**: State management for workflows

### Naming Conventions
- **Controllers**: `{Entity}Controller` (e.g., `EditorialController`)
- **Models**: Singular entity names (e.g., `Editorial`, `User`)
- **Actions**: Descriptive verb phrases (e.g., `MoveEditorialPhase`)
- **Enums**: Descriptive names with backing values
- **Resources**: `{Entity}Resource` for API responses

## Module System

The project supports modular architecture:
- **Modules**: Located in `modules/` directory
- **Packages**: Custom packages in `packages/` directory
- **Auto-discovery**: Modules are auto-registered via composer

## Configuration

### DDD Configuration
- **Domain Path**: `app/Domains`
- **Domain Namespace**: `App\Domains`
- **Autoloading**: Providers, commands, policies, factories, and migrations are auto-discovered
- **Base Model**: `App\Domains\Shared\Models\BaseDomainModel`

### Code Style
- **Standard**: Laravel preset with strict typing
- **Formatting**: Laravel Pint with custom rules
- **Imports**: Alphabetically ordered, global namespace imports enabled
- **Strict Mode**: `declare(strict_types=1)` enforced
