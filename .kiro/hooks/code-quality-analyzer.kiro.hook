{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides automated code quality analysis including code smells, design patterns, and best practices suggestions", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/**/*.php", "modules/**/*.php", "packages/**/*.php", "tests/**/*.php"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified PHP code files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify long methods, large classes, duplicate code, complex conditionals, and other maintainability issues\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check for Laravel best practices, PSR standards, and domain-driven design principles\n4. **Performance**: Identify potential performance bottlenecks and optimization opportunities\n5. **Readability**: Suggest improvements for code clarity and documentation\n6. **Security**: Flag potential security vulnerabilities or concerns\n7. **Testing**: Recommend areas that need better test coverage\n\nFor each suggestion, provide:\n- Clear explanation of the issue\n- Specific code examples showing the problem\n- Recommended solution with example implementation\n- Rationale for why the change improves code quality\n\nMaintain focus on actionable improvements that preserve existing functionality while enhancing maintainability, performance, and readability."}}