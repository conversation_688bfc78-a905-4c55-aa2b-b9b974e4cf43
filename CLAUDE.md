# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Setup
```bash
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate:fresh --seed
```

### Testing
```bash
# Run all tests
php artisan test

# Run with coverage
php artisan test --coverage-html=build/coverage

# Run specific test suites
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature
php artisan test --testsuite=Modules

# Watch tests (using package.json script)
npm run test:watch
```

### Code Quality
```bash
# Linting with Laravel Pint
composer lint
# or
./vendor/bin/pint

# Static analysis with PHPStan
composer stan
# or
./vendor/bin/phpstan analyze

# Check for dumps/dd calls
composer ds:check

# Rector refactoring (dry run)
composer rector
```

### Deployment
```bash
# Production
composer deploy

# Development environment
composer deploy:dev

# Staging
composer deploy:s
```

## Architecture Overview

This is a Laravel 12 API application built using **Domain-Driven Design (DDD)** principles with the `lunarstorm/laravel-ddd` package.

### Domain Structure

The application is organized into domains under `app/Domains/`:

- **Betterflow/V1/** - Main business logic domain containing:
  - **Pitches** - Story pitch management with complex state workflows
  - **Editorials** - Editorial content management with multi-phase workflows
  - **Shared** - Shared components including task management system
  - **Admin/Crud** - Administrative CRUD operations
  - **Dashboard** - Dashboard controllers and resources

- **Auth/** - Authentication and user management
- **Users/** - User models with role-based permissions
- **Notifications/** - Notification system
- **Comments/** - Commenting system
- **Topics/** - Topic categorization
- **Verticals/** - Content vertical organization
- **Spaces/** - Multi-tenant spaces

### Key Architectural Patterns

#### Domain Organization
- **Actions** - Business logic operations (using `lorisleiva/laravel-actions`)
- **Models** - Domain entities with rich behavior
- **Events** - Domain events for decoupled communication
- **Commands** - Command pattern for complex operations
- **Listeners** - Event handlers
- **Policies** - Authorization logic
- **Resources** - API resource transformations

#### State Management
- Pitches and Editorials use state machines with dedicated command handlers
- Complex workflows with multiple phases and stages
- Event-driven architecture for state transitions

#### Task System
- Shared task management system across domains
- Task types defined per domain (Editorial, Asset, Pitch)
- Automated task creation based on workflow transitions

### Database & Testing

- **Primary Database**: PostgreSQL
- **Test Database**: PostgreSQL (`lighthouse_test`)
- **Factories**: Domain-specific factories in each domain's `Database/Factories/` directory
- **Seeders**: Comprehensive seeders for development data

### Key Technologies

- **Laravel 12** with **PHP 8.2+**
- **Sanctum** for API authentication
- **Horizon** for queue management
- **Spatie packages** for permissions, activity logging, and media handling
- **Domain-Driven Design (DDD)** with `lunarstorm/laravel-ddd` for domain isolation
- **Real-time features** with Laravel Reverb

### Important Conventions

- Domain models extend `BaseDomainModel`
- Use Actions for business operations instead of service classes
- Event-driven communication between domains
- Resource transformations for all API responses
- Comprehensive policy-based authorization

### Common Workflows

1. **Pitch Workflow**: Draft → Submitted → Review → Approved/Declined → Editorial Creation
2. **Editorial Workflow**: Multiple phases (Research, Writing, Editing, Translation, Publication)
3. **Task Management**: Automated task creation and assignment based on workflow states
4. **Notification System**: Event-driven notifications across all domains

When working with this codebase, always consider the domain context and follow the established patterns for Actions, Events, and State management.