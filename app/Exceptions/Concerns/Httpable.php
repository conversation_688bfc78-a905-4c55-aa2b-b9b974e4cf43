<?php

namespace App\Exceptions\Concerns;

use Illuminate\Foundation\Application;
use Illuminate\Http\Response;

trait Httpable
{
    protected int $statusCode = Response::HTTP_CONFLICT;
    protected array $headers = [];
    public function report(Application $app)
    {
        // Report only when running in a queued job or scheduled task.
        return $app->runningInConsole();
    }
 
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }
 
    public function getHeaders()
    {
        return $this->headers;
    }
}