<?php

namespace App\Exceptions;

use App\Exceptions\Concerns\Httpable;
use Exception;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Throwable;

class DuplicateLanguageCodeException extends Exception implements HttpExceptionInterface
{
    use Httpable;

    protected int $statusCode = Response::HTTP_CONFLICT;

    public function getHeaders(): array
    {
        return [];
    }
    
    // Create custom error with status cde 422
    public function __construct($message = "Duplicate language code", $code = 422, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    public function report()
    {
        
    }
}
