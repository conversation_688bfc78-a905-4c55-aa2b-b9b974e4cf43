<?php

namespace App\Console\Commands;

use App\Domains\Users\Models\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Spatie\Permission\Models\Role;

class SwitchUserRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:switch-role
                            {--u|user=* : User IDs or emails to switch roles for}
                            {--r|roles=* : Role names to assign}
                            {--keep-existing : Keep existing roles when assigning new ones}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Switch roles for one or more users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Model::shouldBeStrict(false);

        $userIdentifiers = collect($this->option('user'));
        $roleNames = collect($this->option('roles'));
        $removeExisting = !$this->option('keep-existing');

        if ($userIdentifiers->isEmpty()) {
            $this->error('You must specify at least one user with --user option');
            return 1;
        }

        if ($roleNames->isEmpty()) {
            $this->error('You must specify at least one role with --roles option');
            return 1;
        }

        // Find users by ID or email
        $users = collect();
        foreach ($userIdentifiers as $identifier) {
            $user = is_numeric($identifier)
                ? User::find($identifier)
                : User::where('email', $identifier)->first();

            if ($user) {
                $users->push($user);
            } else {
                $this->warn("User with ID/email '{$identifier}' not found");
            }
        }

        if ($users->isEmpty()) {
            $this->error('No valid users found');
            return 1;
        }

        // Validate roles
        $validRoles = collect();
        foreach ($roleNames as $roleName) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $validRoles->push($role);
            } else {
                $this->warn("Role '{$roleName}' not found");
            }
        }

        if ($validRoles->isEmpty()) {
            $this->error('No valid roles found');
            return 1;
        }

        // Switch roles for users
        foreach ($users as $user) {
            $this->info("Processing user '{$user->name}' (ID: {$user->id})");

            // Show current roles
            $currentRoles = $user->getRoleNames();
            if ($currentRoles->isNotEmpty()) {
                $this->info("Current roles: " . $currentRoles->implode(', '));
            } else {
                $this->info("Current roles: none");
            }

            // Switch roles
            if ($removeExisting) {
                $user->roles()->detach();
                $this->info("Removed all existing roles");
            }

            // Assign new roles
            $roleNames = $validRoles->pluck('name')->toArray();
            $user->assignRole($roleNames);
            $this->info("Assigned roles: " . implode(', ', $roleNames));

            // Update user type based on first role
            if (!empty($roleNames)) {
                $firstRole = $roleNames[0];
                $newType = $this->determineTypeFromRole($firstRole);
                if ($newType && $user->type !== $newType) {
                    $user->type = $newType;
                    $user->save();
                    $this->info("Updated user type to: {$newType}");
                }
            }

            $this->newLine();
        }

        return 0;
    }

    /**
     * Determine the user type from a role name.
     */
    protected function determineTypeFromRole(string $roleName): ?string
    {
        $morphMap = Relation::morphMap();

        // Direct match
        if (isset($morphMap[$roleName])) {
            return $roleName;
        }

        // Check if it's a snake_case version of a camelCase key
        foreach ($morphMap as $key => $value) {
            if (str($roleName)->camel()->snake()->toString() === $key) {
                return $key;
            }
        }

        return null;
    }
}
