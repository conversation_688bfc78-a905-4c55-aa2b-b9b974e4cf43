<?php

namespace App\Console\Commands;

use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission as ModelsPermission;

class RevokeUserPermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:revoke
                            {--u|user=* : User IDs or emails to revoke permissions from}
                            {--p|permissions=* : Permission names to revoke}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Revokes specific permissions directly from users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Model::shouldBeStrict(false);

        $userIdentifiers = collect($this->option('user'));
        $permissionNames = collect($this->option('permissions'));

        if ($userIdentifiers->isEmpty()) {
            $this->error('You must specify at least one user with --user option');
            return 1;
        }

        if ($permissionNames->isEmpty()) {
            $this->error('You must specify at least one permission with --permissions option');
            return 1;
        }

        // Find users by ID or email
        $users = collect();
        foreach ($userIdentifiers as $identifier) {
            $user = is_numeric($identifier)
                ? User::find($identifier)
                : User::where('email', $identifier)->first();

            if ($user) {
                $users->push($user);
            } else {
                $this->warn("User with ID/email '{$identifier}' not found");
            }
        }

        if ($users->isEmpty()) {
            $this->error('No valid users found');
            return 1;
        }

        // Ensure permissions exist
        $permissionNames->each(fn($permission) => ModelsPermission::findOrCreate($permission, 'api'));

        // Convert permission names to enum values
        $permissions = $permissionNames->map(fn($permission) => Permission::tryFrom($permission))
            ->filter(); // Remove null values

        if ($permissions->isEmpty()) {
            $this->error('No valid permissions found');
            return 1;
        }

        // Revoke permissions from users
        foreach ($users as $user) {
            foreach ($permissions as $permission) {
                if ($user->hasDirectPermission($permission)) {
                    $this->info("Revoking permission '{$permission->value}' from user '{$user->name}' (ID: {$user->id})");
                    $user->revokePermissionTo($permission);
                } else {
                    $this->info("User '{$user->name}' does not have direct permission '{$permission->value}'");
                }
            }
        }

        return 0;
    }
}
