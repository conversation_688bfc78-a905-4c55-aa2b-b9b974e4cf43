<?php

namespace App\Console\Commands;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission as ModelsPermission;
use Spatie\Permission\Models\Role;

class RevokePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:revoke-permission {--r|roles=* : Role names to revoke permissions from} {--p|permissions=* : Permission names}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Removes permssion/s from role/s';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Model::shouldBeStrict(false);

        $allRoles = false;
        $permissionNames = collect($this->option('permissions'));
        $roleNames = collect($this->option('roles'));
        if($roleNames->isEmpty()) {
            $allRoles = true;
        }        
       
        if($permissionNames->isEmpty()) {
            $permissionNames = collect(Permission::cases())->map(fn($p) => $p->value);
        }

        $roles = Role::with('permissions')
            ->when(
                !$allRoles,
                fn($query) => $query->whereIn('name', $roleNames)
            )->get();

        $permissionNames->each(fn($permission) => ModelsPermission::findOrCreate($permission, 'api'));
        
        $permissions = $permissionNames->map(fn($permission) => Permission::tryFrom($permission));
        
        foreach ($permissions as $p) {
            $roles->each(function($role) use ($p) {
                if($role->hasPermissionTo($p)) {
                    $this->info("Revoking permission {$p->value} from role {$role->name}");
                    $role->revokePermissionTo($p);
                } else {
                    $this->info("Role {$role->name} does not have permission {$p->value}");
                }
            });
        }
    }
}
