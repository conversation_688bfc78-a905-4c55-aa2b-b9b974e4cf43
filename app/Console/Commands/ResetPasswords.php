<?php

namespace App\Console\Commands;

use App\Domains\Users\Models\User;
use Exception;
use Illuminate\Console\Command;

class ResetPasswords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reset-passwords';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset passwords for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if(app()->isProduction()) {
            throw new Exception("Cannot run command in production");
        }

        User::notSystem()->update(['password' => bcrypt('password')]);

        $this->info("All users reset");
    }
}
