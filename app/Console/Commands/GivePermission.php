<?php

namespace App\Console\Commands;

use App\Domains\Shared\Enums\Permission as EnumsPermission;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class GivePermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:permission {--s|system : Include system role } {--r|roles=* : Role names to give permissions to} {--p|permissions=* : Permission names}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gives given roles some permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Model::shouldBeStrict(false);

        $allRoles = false;
        $systemRole = $this->option('system');

        $permissionNames = collect($this->option('permissions'));

        $roleNames = collect($this->option('roles'));

        if ($roleNames->isEmpty() && !$systemRole) {
            $allRoles = true;
        }

        if ($permissionNames->isEmpty()) {
            $permissionNames = collect(EnumsPermission::cases())->map(fn($p) => $p->value);
        }

        $roles = $systemRole ? Role::with('permissions')->whereName('system') : Role::with('permissions')
            ->whereNot('name', 'system')
            ->when(
                !$allRoles,
                fn($query) => $query->whereIn('name', $roleNames)
            );

        $roles = $roles->get();

        $permissionNames->each(fn($permission) => Permission::findOrCreate($permission, 'api'));

        $permissions = $permissionNames->map(fn($permission) => EnumsPermission::tryFrom($permission));


        foreach ($permissions as $p) {
            $roles->each(function ($role) use ($p) {
                if (!$role->hasPermissionTo($p)) {
                    $this->info("Giving permission {$p->value} to role {$role->name}");
                    $role->givePermissionTo($p);
                } else {
                    $this->info("Role {$role->name} already has permission {$p->value}");
                }
            });
        }
    }
}
