<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeadersMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        if (app()->isProduction() && $request->secure()) {
            $response->headers->set('Content-Security-Policy', "default-src 'self'; script-src 'self'");
        }

        return $response;
    }
}
