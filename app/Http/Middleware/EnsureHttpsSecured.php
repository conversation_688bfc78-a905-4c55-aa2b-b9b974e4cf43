<?php

namespace App\Http\Middleware;

class EnsureHttpsSecured
{
    /**
     * The cookies that should not be encrypted.
     *
     * @var array<int, string>
     */
    protected $except = [
        //
    ];

    /**
     * Add the HSTS header to the response if the app is running under HTTPS.
     */
    public function handle($request, \Closure $next)
    {
        $response = $next($request);

        if (app()->isProduction() && $request->secure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        return $response;
    }
}
