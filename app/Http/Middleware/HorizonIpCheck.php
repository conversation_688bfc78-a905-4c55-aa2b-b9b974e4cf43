<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Horizon\Exceptions\ForbiddenException;
use Symfony\Component\HttpFoundation\Response;

class HorizonIpCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if(collect(explode(',',config('horizon.ips', "")))->doesntContain($this->getCLientIpAddressFrom($request))) {
            throw new ForbiddenException("You don't have access here!");
        }
        
        return $next($request);
    }

    private function getCLientIpAddressFrom(Request $request): string
    {
        // Prioritized headers for real client IP, most specific first
        $ipHeaders = [
            'HTTP_TRUE_CLIENT_IP',     // Cloudflare
            'HTTP_CF_CONNECTING_IP',   // Cloudflare
            'HTTP_X_REAL_IP',          // Nginx
            'HTTP_X_FORWARDED_FOR',    // Common proxy header
            'REMOTE_ADDR' ,
        ];

        // Log all headers for diagnostic purposes
        $allHeaders = [];
        foreach ($ipHeaders as $header) {
            $value = $request->server($header);
            if ($value) {
                $allHeaders[$header] = $value;
            }
        }
        Log::channel('daily')->info('Horizon IP Detection Diagnostic', [
            'headers' => $allHeaders,
            'request_ip' => $request->ip(),
            'server_ip' => $request->server('SERVER_ADDR') ?? 'N/A',
        ]);

        foreach ($ipHeaders as $header) {
            $ip = $request->server($header);
            
            if ($ip) {
                // Handle comma-separated IP lists (common in X-Forwarded-For)
                $ipArray = explode(',', $ip);
                $clientIp = trim($ipArray[0]);

                // Validate IP and prefer IPv4
                if (filter_var($clientIp, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    Log::channel('daily')->info('Horizon IP Detected', [
                        'detected_ip' => $clientIp,
                        'header_used' => $header
                    ]);
                    return $clientIp;
                }
            }
        }

        // Final fallback
        $fallbackIp = $request->ip();
        Log::channel('daily')->warning('Horizon IP Fallback Used', [
            'fallback_ip' => $fallbackIp
        ]);
        return $fallbackIp;
    }
}
