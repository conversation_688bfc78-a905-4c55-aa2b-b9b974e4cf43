<?php

namespace App\Providers;

use App\Domains\Shared\Notifications\JobFailedNotificaiton;
use App\Domains\Users\Models\SuperAdmin;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(UrlGenerator $url): void
    {
        Queue::failing(function (JobFailed $event) {
            SuperAdmin::query()
                ->get()
                ->each
                ->notify(new JobFailedNotificaiton($event));
        });

        if (env('APP_ENV') == 'production') {
            $url->forceScheme('https');
        }
    }
}
