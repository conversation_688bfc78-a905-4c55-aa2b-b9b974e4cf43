<?php

namespace App\Providers;

use App\Domains\Users\Models\User;
use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class ScrambleServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        Scramble::ignoreDefaultRoutes();
    }

    public function boot(): void
    {
        Scramble::routes(function (Route $route) {
            $routeName = str($route->uri)->before('/');
            $ignore = collect(['_ignition']);

            return $ignore->doesntContain($routeName);
        });

        Scramble::afterOpenApiGenerated(function (OpenApi $openApi): void {
            $openApi->secure(
                SecurityScheme::http('bearer'),
            );
        });

        Gate::define('viewApiDocs', function (User $user): true {
            return true;
            // return $user->hasAnyRole('super_admin', 'admin');
        });
    }
}
