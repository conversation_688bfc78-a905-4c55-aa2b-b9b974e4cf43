<?php

namespace App\Providers;

use App\Domains\Auth\Actions\Fortify\CreateNewUser;
use App\Domains\Auth\Actions\Fortify\ResetUserPassword;
use App\Domains\Auth\Actions\Fortify\UpdateUserPassword;
use App\Domains\Auth\Actions\Fortify\UpdateUserProfileInformation;
use App\Domains\Auth\Http\Responses\Fortify\LoginResponse;
use App\Domains\Auth\Http\Responses\Fortify\LogoutResponse;
use App\Domains\Auth\Http\Responses\Fortify\ProfileInformationUpdatedResponse;
use App\Domains\Users\Notifications\ConfirmAccount;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Laravel\Fortify\Contracts\LoginResponse as LoginResponseContract;
use Laravel\Fortify\Contracts\LogoutResponse as LogoutResponseContract;
use Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse as ProfileInformationUpdatedResponseContract;
use Laravel\Fortify\Fortify;

class FortifyServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(LogoutResponseContract::class, LogoutResponse::class);
        $this->app->bind(LoginResponseContract::class, LoginResponse::class);
        $this->app->bind(ProfileInformationUpdatedResponseContract::class, ProfileInformationUpdatedResponse::class);
        Fortify::ignoreRoutes();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        ResetPassword::createUrlUsing(function (object $notifiable, string $token) {
            return URL::signedFrontendUrlFromRoute(
                name: 'password.reset',
                parameters: [
                    'token' => $token,
                    'email' => $notifiable->getEmailForPasswordReset(),
                ],
            );
        });

        VerifyEmail::createUrlUsing(function (object $notifiable) {
            return URL::signedFrontendUrlFromRoute(
                name: 'verification.verify',
                parameters: [
                    'id' => $notifiable->getKey(),
                    'hash' => sha1($notifiable->getEmailForVerification()),
                ],
            );
        });

        ConfirmAccount::createUrlUsing(function (object $notifiable) {
            return URL::signedFrontendUrlFromRoute(
                name: 'account.confirm',
                parameters: [
                    'id' => $notifiable->getKey(),
                    'hash' => sha1($notifiable->getEmailForVerification()),
                ],
            );
        });

        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        RateLimiter::for('login', function (Request $request) {
            $throttleKey = Str::transliterate(Str::lower($request->input(Fortify::username())) . '|' . $request->ip());

            return Limit::perMinute(5)->by($throttleKey);
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });
    }
}
