<?php

namespace App\Domains\Betterflow\V1\Pitches\Enums;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchStage as PitchStageModel;
use App\Domains\Shared\Helpers\CrudHelper;

enum PitchStage: string
{
    case Draft = 'draft';
    case Submitted = 'submitted';
    case Approved = 'approved';

    case Declined = 'declined';
    case OnHold = 'on-hold';

    case ReturnForRevision = 'return-for-revision';
    case PitchMeeting = 'pitch-meeting';
    case AwaitingTravelApproval = 'awaiting-travel-approval';

    public function model(): PitchStageModel
    {
        return CrudHelper::pitch_stages()->firstWhere('slug', $this->value);
    }

    public function niceName(): string
    {
        return str($this->name)->headline();
    }
}
