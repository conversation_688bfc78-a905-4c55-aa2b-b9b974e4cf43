<?php

namespace App\Domains\Betterflow\V1\Pitches\Enums;

use App\Domains\Shared\Contracts\RecordableEvent;

enum RecordableEvents: string implements RecordableEvent
{
    case DraftCreated = 'DraftCreated';
    case PitchUpdated = 'PitchUpdated';
    case PitchDeleted = 'PitchDeleted';

    case CommentAdded = 'CommentAdded';
    case ReplyAdded = 'ReplyAdded';
    case PitchStageChanged = 'PitchStageChanged';
    case AssignedTo = 'AssignedTo';
    case CollaboratorsUpdated = 'CollaboratorsUpdated';
    case AttachmentsDeleted = 'AttachmentsDeleted';
    case TaskCreated = 'TaskCreated';
}
