<?php

namespace App\Domains\Betterflow\V1\Pitches\Database\Factories;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchApproved;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchAwaitingTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchDeclined;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchReturnedForRevision;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchSubmitted;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Events\PitchCreated;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Helpers\CrudHelper;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\Reporter;
use App\Domains\Users\Models\User;
use App\Domains\Verticals\Models\Vertical;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Lottery;
use Illuminate\Support\Str;

class PitchFactory extends Factory
{
    protected $model = Pitch::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $data = [
            'created_by' => $this->getRandomUserId(),
        ];

        $created_at = now()->subWeeks(rand(0, 99));
        $updated_at_weeks = (int) $created_at->diffInWeeks(now());
        $updated_at = $created_at->addWeeks(rand(0, $updated_at_weeks));
        $headline = fake()->randomElement(['HIV Prisons', 'Gender Phobia', 'SantaRita', 'Environment Deforestation', 'Women Migration', 'Political Corruption in Zimbabwe', 'Turtles And Coast Erosion', 'Indigenous Rights', 'Agriculture Awareness', 'Mothers Migrating', 'Pride Project', 'Teacher Shortage', 'Vertical Famrms', 'Cervical Cancer', 'Polluted Birendranagar']);
        $topic = Topic::inRandomOrder()->first() ?? Topic::factory()->create();
        $vertical = Vertical::where('topic_id', $topic->getKey())->inRandomOrder()->first() ?? Vertical::factory()->create([
            'topic_id' => $topic->getKey(),
        ]);

        return [
            ...$data,
            'public_id' => Str::orderedUuid()->toString(),
            'short_name' => $headline,
            'slug' => str($headline)->slug(),
            'created_by' => Reporter::inRandomOrder()->first() ?? Reporter::factory()->create(),
            'pitch_type' => PitchType::inRandomOrder()->first(),
            'created_at' => $created_at,
            'updated_at' => $updated_at,
            'country_code' => fake()->randomElement(['NE', 'AR', 'MX', 'ZW', 'UG', 'MN']),
            'primary_language' => fake()->randomElement(['en-EN']),
            'topic_id' => $topic,
            'vertical_id' => $vertical,
        ];
    }

    protected function getRandomUserId(): int
    {
        return $this->getRandomRecycledModel(User::class)?->getKey() ?? fake()->randomElement($this->getRandomUsers()) ?? User::factory()->create()->getKey();
    }

    protected function getRandomUsers(): array
    {
        return User::whereIn('type', ['reporter', 'editor'])->inRandomOrder()->pluck('id')->toArray();
    }

    public function withStates(): static
    {
        /* @phpstan-ignore-next-line */
        return $this->afterCreating(function (Pitch $pitch): Pitch {

            PitchCreated::dispatch($pitch);

            Lottery::odds(10, 50)->winner(function () use ($pitch): void {
                $stage = fake()->randomElement(CrudHelper::pitch_stages()->pluck('slug')->toArray());

                PitchSubmitted::dispatch($pitch->getKey(), $pitch->author->getKey());

                if ($stage == PitchStage::Approved->value) {
                    PitchApproved::dispatch($pitch->getKey(), User::inRandomOrder()->first()->getKey());
                }

                if ($stage == PitchStage::Declined->value) {
                    PitchDeclined::dispatch($pitch->getKey(), User::inRandomOrder()->first()->getKey());
                }

                if ($stage == PitchStage::AwaitingTravelApproval->value) {
                    PitchAwaitingTravelApproval::dispatch($pitch->getKey(), User::inRandomOrder()->first()->getKey());
                }

                // Meeting
                if ($stage == PitchStage::PitchMeeting->value) {
                    $pitch->states()->create([
                        'state' => PitchStage::tryFrom($stage)->value,
                        'attributes' => [
                            'reviewed_at' => $pitch->created_at
                                ->addWeeks(rand(0, (int) $pitch->created_at->diffInWeeks(now())))
                                ->setTime(rand(0, 23), rand(0, 59), rand(0, 59)),
                            'reviewed_by' => $this->getRandomUserId(),
                        ],
                    ]);

                    if ($verticalEditor = $pitch->vertical->editor) {
                        $pitch->assignTo($verticalEditor);
                    }

                    $pitch->save();

                    // PitchScheduledForPitchMeeting::dispatch($pitch->getKey(), User::inRandomOrder()->first()->getKey());
                }

                // Return for revision
                if ($stage == PitchStage::ReturnForRevision->value) {
                    PitchReturnedForRevision::dispatch($pitch->getKey(), User::inRandomOrder()->first()->getKey());
                }
            })->choose();

            return $pitch;
        });
    }

    public function withComments(int $comments, int $replies = 4): static
    {
        /* @phpstan-ignore-next-line */
        return $this->afterCreating(function (Pitch $pitch) use ($comments, $replies): void {
            $commentsData = Comment::factory($comments)->make([
                'user_id' => fake()->randomElement(User::query()->pluck('id')->toArray()),
                'parent_id' => null,
                'comment' => fake()->randomElement(File::json(resource_path('misc/comments.json'))),
            ]);

            $pitch->comments()->saveMany($commentsData);

            $replies = Comment::factory(random_int(1, $replies))->create([
                'user_id' => fake()->randomElement(User::query()->pluck('id')->toArray()),
                'parent_id' => fake()->randomElement($commentsData->pluck('id')->toArray()),
                'comment' => fake()->randomElement(File::json(resource_path('misc/comments.json'))),
            ]);

            $pitch->comments()->saveMany($replies);

        });
    }

    public function asDraft(): Factory
    {
        /* @phpstan-ignore-next-line */
        return $this->afterCreating(function (Pitch $pitch): Pitch {
            $pitch->states()->create([
                'state' => PitchStage::Draft,
            ]);

            return $pitch;
        });
    }

    public function asSubmitted(): Factory
    {
        /* @phpstan-ignore-next-line */
        return $this->afterCreating(function (Pitch $pitch): Pitch {
            $pitch->states()->create([
                'state' => PitchStage::Draft,
            ]);
            $pitch->states()->create([
                'state' => PitchStage::Submitted,
            ]);

            return $pitch;
        });
    }

    public function withCollaborators(int $count = 1): Factory
    {
        /* @phpstan-ignore-next-line */
        return $this->afterCreating(function (Pitch $pitch) use ($count): Pitch {
            $users = User::whereIn('type', ['reporter', 'translator', 'editor'])->where('id', '!=', $pitch->created_by)->inRandomOrder()->limit($count);
            $pitch->collaborators()->attach($users->pluck('id'));

            return $pitch;
        });
    }

    public function withUpdates(int $count = 1): Factory
    {
        /* @phpstan-ignore-next-line */
        return $this->afterCreating(function (Pitch $pitch): Pitch {
            return $pitch;
        });
    }
}
