<?php

namespace App\Domains\Betterflow\V1\Pitches\Events\Reactions;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\AuthorResource;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Comments\Models\Comment;
use App\Domains\Users\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LikedComment
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(public readonly string|int $userId, public readonly string|int $commentId) {}

    // public function broadcastOn(): array
    // {
    //     return [
    //         new PrivateChannel('Betterflow.'.$this->user_id),
    //     ];
    // }

    // public function broadcastAs(): ?string
    // {
    //     return 'comment.liked';
    // }

    // public function broadcastWith(): array
    // {
    //     return [
    //         'user' => AuthorResource::make(User::find($this->user_id)),
    //         'comment' => CommentsResource::make(Comment::find($this->comment_id)),
    //     ];
    // }

    // public function broadcastQueue(): string
    // {
    //     return 'events';
    // }
}
