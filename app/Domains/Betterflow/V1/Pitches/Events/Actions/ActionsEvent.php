<?php

namespace App\Domains\Betterflow\V1\Pitches\Events\Actions;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationPitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Users\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

abstract class ActionsEvent implements ShouldDispatchAfterCommit
{
    use Dispatchable;

    // use InteractsWithSockets;
    use SerializesModels;

    public ?Model $pitch = null;

    public ?User $user = null;

    protected bool $toCollaborators = true;

    public function __construct(
        public int $pitchId,
        public int $userId,
    ) {
        $this->pitch = Pitch::findOrFail($pitchId);
        $this->user = User::findOrFail($userId);
    }

    public function broadcastQueue(): string
    {
        return 'betterflow';
    }

    abstract public function broadcastAs(): string;

    abstract public function broadcastChannels(): array;

    // public function broadcastWith(): array
    // {
    //     return [
    //         'pitch' => NotificationPitchResource::make($this->pitch),
    //         'user' => SimpleUserResource::make($this->user),
    //     ];
    // }

    // public function broadcastOn(): array
    // {
    //     $channels = [
    //         ...$this->broadcastChannels(),
    //     ];

    //     if ($this->toCollaborators) {
    //         $channels[] = $this->pitch->collaborators->filter(fn ($user) => $user->getKey() !== $this->pitch->created_by)->map(fn ($user) => new PrivateChannel('Betterflow.users.'.$user->getKey()));
    //     }

    //     return [
    //         ...$channels,
    //         new PrivateChannel('Betterflow.users.'.$this->pitch->created_by),
    //         new PrivateChannel('Betterflow.pitches.'.$this->pitch->getPublicKey()),
    //         new PrivateChannel('Betterflow.editors'),
    //     ];
    // }
}
