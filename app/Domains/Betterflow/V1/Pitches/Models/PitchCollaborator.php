<?php

namespace App\Domains\Betterflow\V1\Pitches\Models;

use App\Domains\Shared\Models\BasePivot;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PitchCollaborator extends BasePivot
{
    use LogsActivity;

    public $incrementing = false;

    protected $table = 'pitch_collaborators';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function pitch(): BelongsTo
    {
        return $this->belongsTo(Pitch::class, 'pitch_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logOnlyDirty()->dontSubmitEmptyLogs();
    }
}
