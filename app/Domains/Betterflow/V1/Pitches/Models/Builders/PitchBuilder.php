<?php

namespace App\Domains\Betterflow\V1\Pitches\Models\Builders;

use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use Illuminate\Database\Eloquent\Builder;
use InvalidArgumentException;

class PitchBuilder extends Builder
{
    public function findByPublicId(string $publicId): ?Pitch
    {
        if (! str($publicId)->isUuid()) {
            throw new InvalidArgumentException('Invalid public id');
        }

        $pitch = Pitch::query()
            ->where('public_id', $publicId)
            ->first();

        return $pitch instanceof Pitch ? $pitch : null;
    }

    public function drafts(): self
    {
        return $this->whereRelation('state', 'state', PitchStage::Draft);
    }

    public function notOwnedBy(?string $userId = null): self
    {
        return $this->whereNot('created_by', $userId);
    }

    public function notDrafts(): self
    {
        return $this->whereRelation(relation: 'state', column: 'state', operator: '!=', value: PitchStage::Draft);
    }

    public function notApproved(): self
    {
        return $this->whereRelation(
            relation: 'state',
            column: 'state',
            operator: '!=',
            value: PitchStage::Approved,
        );
    }

    public function withStates(...$states): self
    {
        return $this->whereHas('state', function ($query) use ($states): void {
            $query->whereIn('state', $states);
        });
    }

    public function ownedByUser(string $userId): self
    {
        return $this->where('created_by', $userId);
    }

    public function ownDrafts(string $userId): self
    {
        return $this->ownedByUser($userId)->whereRelation('state', 'state', PitchStage::Draft);
    }
}
