<?php

namespace App\Domains\Betterflow\V1\Pitches\Models;

use App\Domains\Shared\Models\BaseDomainModel;
use Database\Factories\PitchFormFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use M<PERSON>ciot\Versionable\VersionableTrait;

class PitchForm extends BaseDomainModel
{
    use SoftDeletes;
    use VersionableTrait;

    protected $guarded = ['pitch_id', 'id', 'created_at', 'updated_at'];

    protected $casts = [
        'form' => 'json',
    ];

    public function pitch(): BelongsTo
    {
        return $this->belongsTo(Pitch::class);
    }

    protected static function newFactory(): PitchFormFactory
    {
        return PitchFormFactory::new();
    }
}
