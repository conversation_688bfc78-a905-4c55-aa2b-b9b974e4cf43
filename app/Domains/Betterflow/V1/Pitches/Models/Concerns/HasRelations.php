<?php

namespace App\Domains\Betterflow\V1\Pitches\Models\Concerns;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\PitchCollaborator;
use App\Domains\Betterflow\V1\Pitches\Models\PitchForm;
use App\Domains\Betterflow\V1\Pitches\Models\PitchState;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\User;
use App\Domains\Verticals\Models\Vertical;
use GlobalPress\Events\Models\Event;
use GlobalPress\Events\Models\EventPitch;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HasRelations
{
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assigned(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function collaborators(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'pitch_collaborators', 'pitch_id', 'user_id')
            ->withTimestamps()
            ->as('collaborator')
            ->using(PitchCollaborator::class);
    }

    public function forms(): HasMany
    {
        return $this->hasMany(PitchForm::class);
    }

    public function formForLanguage(?string $code = null): ?PitchForm
    {
        $form = $this->forms()->where('language_code', $code)->sole();
        return $form instanceof PitchForm ? $form : null;
    }

    public function primaryForm(): HasOne
    {
        return $this->forms()->one()->where('language_code', $this->primary_language ?? 'en-EN');
    }

    public function revisions(): MorphMany
    {
        return $this->morphMany(PitchState::class, 'statable')
            ->where('state', PitchStage::ReturnForRevision);
    }

    public function topic(): BelongsTo
    {
        return $this->belongsTo(Topic::class);
    }

    public function vertical(): BelongsTo
    {
        return $this->belongsTo(Vertical::class);
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(PitchType::class, 'pitch_type');
    }

    public function editorial(): HasOne
    {
        return $this->hasOne(Editorial::class, 'pitch_id');
    }

    public function pitchEvent(): BelongsTo
    {
        return $this->belongsTo(EventPitch::class, 'id', 'pitch_id');
    }

    public function meeting(): ?Event
    {
        return EventPitch::where('pitch_id', $this->getKey())->first()?->calendarEvent();
    }
}
