<?php

namespace App\Domains\Betterflow\V1\Pitches\Models\Concerns;

use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State;
use App\Domains\Betterflow\V1\Pitches\Models\PitchState;
use App\Domains\Shared\Exceptions\IllegalActionException;
use App\Domains\Users\Models\User;
use GlobalPress\Events\Models\EventPitch;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;

trait HandlesStages
{
    public function approve(#[CurrentUser] ?User $approvedBy = null): void
    {
        throw_if($this->hasEditorial(), new IllegalActionException('The pitch has already been approved for editorial.'));

        throw_unless($approvedBy->can('approve', $this), new IllegalActionException('You do not have permission to approve this pitch.'));

        $this->changeState(PitchStage::Approved, $approvedBy, [
            'approved_by' => $approvedBy,
        ], 'This pitch has already been approved', 'approving', 'approved');

        // Remove meetings if there are any.
        EventPitch::where('pitch_id', $this->getKey())->delete();
    }

    public function decline(#[CurrentUser] ?User $declinedBy = null, ?string $declineReason = ''): void
    {
        $this->changeState(PitchStage::Declined, $declinedBy, [
            'declined_by' => $declinedBy->getKey(),
            'decline_reason' => $declineReason,
        ], 'This pitch has already been declined', 'declining', 'declined');
    }

    public function submit(#[CurrentUser] ?User $submittedBy = null): void
    {
        throw_unless($submittedBy->can('submit', $this), new IllegalActionException('You do not have permission to submit this pitch.'));

        throw_if($this->currentStateId === PitchStage::Approved, new IllegalActionException('Approved pitches can not be changed.'));
        // TODO: this creates a task for the vertical editor to review their pitch.
        // NOTE: If a ptich is in draft, eg initially submitted, there is no task

        $this->changeState(PitchStage::Submitted, $submittedBy, [
            'submitted_by' => $submittedBy,
        ], 'This pitch has already been submitted', 'submitting', 'submitted');
    }

    public function returnForRevision(#[CurrentUser] ?User $returnedBy = null, ?string $reason = null): void
    {
        throw_if($this->currentStateId === PitchStage::Approved, new IllegalActionException('Approved pitches can not be changed.'));

        // TODO this creates a task for the reporter to review their pitch.

        $this->changeState(PitchStage::ReturnForRevision, $returnedBy, [
            'returned_for_revision_by' => $returnedBy->getKey(),
            'returned_for_revision_reason' => $reason ?? 'No reason provided',
        ], 'This pitch has already been sent to revision', 'returningForRevision', 'returnedForRevision');
    }

    public function scheduleToPitchMeeting(string $eventId, #[CurrentUser] ?User $scheduledBy = null): void
    {
        throw_if($this->currentStateId === PitchStage::Approved, new IllegalActionException('Approved pitches can not be changed.'));

        if (EventPitch::query()->where('pitch_id', $this->getKey())->where('event_id', $eventId)->exists()) {
            throw new IllegalActionException('This pitch has already been added to this pitch meeting');
        }

        EventPitch::create([
            'pitch_id' => $this->getKey(),
            'event_id' => $eventId,
        ]);

        $this->changeState(PitchStage::PitchMeeting, $scheduledBy, [
            'scheduled_for_pitch_meeting_by' => $scheduledBy->getKey(),
        ], 'This pitch has already been added to a pitch meeting', 'sendingToPitchMeeting', 'sentToPitchMeeting');
    }

    public function reschedulePitchMeeting(string $eventId, #[CurrentUser] ?User $rescheduledBy = null): void
    {
        throw_if($this->currentStateId === PitchStage::Approved, new IllegalActionException('Approved pitches can not be changed.'));
        throw_if($this->currentStateId === PitchStage::Draft, new IllegalActionException('The pitch is still in draft state. It cannot be changed before it is submitted.'));
        throw_if($this->currentStateId !== PitchStage::PitchMeeting, new IllegalActionException('The pitch can only be rescheduled to a different pitch meeting once it has been sent to a pitch meeting.'));

        EventPitch::where('pitch_id', $this->getKey())
            ->update([
                'event_id' => $eventId,
            ]);

        $this->fireModelEvent('sendingToPitchMeeting');

        $this->states()->create([
            'state' => PitchStage::PitchMeeting,
            'attributes' => [
                'rescheduled_for_pitch_meeting_by' => $rescheduledBy->getKey(),
            ],
        ]);

        DB::afterCommit(function (): void {
            // broadcast(new State\PitchScheduledForPitchMeeting($this->getKey(), Auth::id()))->toOthers();
            $this->fireModelEvent('sentToPitchMeeting');
        });
    }

    // public function onHold(CarbonInterface|DateTimeInterface|null $until, string|int $reason = '', ?string $userId = null): void
    // {
    //     $this->changeState(PitchStage::OnHold, [
    //         'on_hold_by' => $userId ,
    //         'on_hold_until' => $until,
    //         'on_hold_reason' => $reason,
    //         'on_hold_reason_is_id' => is_int($reason) ? true : false,
    //     ], 'This pitch has already been put on hold', 'puttingOnHold', 'putOnHold', State\PitchPutOnHold::class);
    // }

    public function travelApproval(#[CurrentUser] ?User $requestedBy = null, ?string $holdUntil = null): void
    {
        throw_if($this->currentStateId === PitchStage::Approved, new IllegalActionException('Approved pitches can not be changed.'));

        $this->changeState(PitchStage::AwaitingTravelApproval, $requestedBy, [
            'awaiting_travel_approval_by' => $requestedBy->getKey(),
            'hold_for_approval_until' => Date::createFromDate($holdUntil),
        ], 'This pitch is already awaiting travel approval', 'awaitingTravelApproval', 'awaitTravelApproval');
    }

    public function approveTravel(#[CurrentUser] ?User $approvedBy = null): void
    {
        throw_if($this->currentStateId === PitchStage::Approved, new IllegalActionException('Approved pitches can not be changed.'));

        $this->changeState(PitchStage::Submitted, $approvedBy, [
            'travel_approved_by' => $approvedBy->getKey(),
        ], 'This pitch has already been approved for travel', 'approvingTravel', 'travelApproved', State\PitchTravelApproved::class);
    }

    private function changeState(PitchStage $newState, User $byUser, array $attributes, string $errorMessage, string $beforeEvent, string $afterEvent, ?string $broadcastEventClass = null): void
    {
        /** @var PitchStage $currentState */
        $currentState = $this->currentStateId;

        throw_if($currentState === PitchStage::Draft && $newState !== PitchStage::Submitted, new IllegalActionException('The pitch is still in draft state. It cannot be changed before it is submitted.'));

        throw_if($currentState === $newState, new IllegalActionException($errorMessage));

        $this->fireModelEvent($beforeEvent);

        $this->createNewState($newState, $attributes);

        DB::afterCommit(function () use ($broadcastEventClass, $afterEvent, $byUser): void {
            if ($broadcastEventClass) {
                broadcast(new $broadcastEventClass($this->getKey(), $byUser->getKey()))->toOthers();
            }

            $this->fireModelEvent($afterEvent);
        });
    }

    private function createNewState(PitchStage $state, array $attributes, ?string $userId = null): void
    {
        $state = $this->addNewState($state, $attributes, $userId);

        if(app()->runningInConsole()) {
            $this->states()->save($state);
        } else {
            $this->rawStates->push($state);
        }
    }

    private function addNewState(PitchStage $state, array $attributes, ?string $userId = null): PitchState
    {
        $defaultAttributes = [
            // 'changed_by' => $userId ,
            // 'changed_at' => now(),
        ];

        $newState = new PitchState();
        $newState->state = $state->value;
        $newState->setAttribute('attributes', array_merge($defaultAttributes, $attributes));

        return $newState;
    }
}
