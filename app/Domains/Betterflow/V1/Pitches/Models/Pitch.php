<?php

namespace App\Domains\Betterflow\V1\Pitches\Models;

use App\Domains\Betterflow\V1\Pitches\Database\Factories\PitchFactory;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\AttachmentAdded;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\AttachmentsAdded;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\CollaboratorsAdded;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Builders\PitchBuilder;
use App\Domains\Betterflow\V1\Pitches\Models\Concerns\HandlesStages;
use App\Domains\Betterflow\V1\Pitches\Models\Concerns\HasRelations;
use App\Domains\Betterflow\V1\Pitches\Observers\PitchObserver;
use App\Domains\Shared\Concerns\HasComments;
use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Concerns\HasState;
use App\Domains\Shared\Concerns\HasTasks;
use App\Domains\Shared\Contracts\HasName;
use App\Domains\Shared\Contracts\HasPublicKey as ContractsHasPublicKey;
use App\Domains\Shared\Contracts\HasState as ContractsHasState;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\Concerns\HasPivotEvents;
use App\Domains\Shared\Models\Concerns\HasPublicKey;
use App\Domains\Shared\Models\Concerns\HasResourceKey;
use App\Domains\Shared\Models\Concerns\TracksActivity;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\User;
use App\Domains\Verticals\Models\Vertical;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use GPJ\Watchable\Models\Watch;
use GPJ\Watchable\Traits\Watchable;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Mateusjatenee\Persist\Persist;
use Mpociot\Versionable\VersionableTrait;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Image\Enums\Fit;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\FileAdder;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[ObservedBy(PitchObserver::class)]
class Pitch extends BaseDomainModel implements ContractsHasPublicKey, ContractsHasState, HasMedia, HasName
{
    use CascadeSoftDeletes;
    use HandlesStages;
    use HasComments;
    use HasFactory;
    use HasPivotEvents;
    use HasPublicKey;
    use HasResourceKey;
    use HasRelations;
    use HasState;
    use HasTasks;
    use InteractsWithMedia;
    use LogsActivity;
    use Persist;
    use SoftDeletes;
    use TracksActivity;
    use VersionableTrait;
    use Watchable;

    protected $cascadeDeletes = ['forms', 'editorial'];

    protected $guarded = ['public_id', 'id'];

    protected $stateModelClass = PitchState::class;

    protected $observables = [
        'approving',
        'approved',
        'declining',
        'declined',
        'addedAttachemnt',
        'topicUpdated',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $with = [];

    public function getName(): string {
        return $this->short_name;
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this
            ->addMediaConversion('preview')
            ->fit(Fit::Contain, 300, 300)
            ->withResponsiveImages();

        $this
            ->addMediaConversion('large')
            ->fit(Fit::Contain, 1920, 1080)
            ->withResponsiveImages();
    }

    public function addForm(array $formData): self
    {
        $pitchForm = new PitchForm();
        $pitchForm->fill($formData);
        // dd($pitchForm);
        $this->forms->push($pitchForm);

        return $this;
    }

    public function attachments(): MorphMany
    {
        return $this->morphMany(Media::class, 'model')->where('model_type', str(class_basename($this))->lower())->where('collection_name', 'attachments');
    }

    public function trackCollaboratorsSynced($changes = null): void
    {
        $attached = collect(data_get($changes, 'attached', []))->flatten();
        $detached = collect(data_get($changes, 'detached', []))->flatten();
        $updated = collect(data_get($changes, 'updated', []))->flatten();

        if ($attached->isEmpty() && $detached->isEmpty() && $updated->isEmpty()) {
            return;
        }

        /* @var Collection $collaborators */

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $this,
            by: auth()->id(),
            event: RecordableEvents::CollaboratorsUpdated,
            description: 'Updated collaborators',
            properties: [
                'changes' => [
                    'added' => $attached,
                    'removed' => $detached,
                    'updated' => $updated,
                ],
            ],
        );

        RecordActivity::dispatch($activity);

        if (! $this->wasRecentlyCreated) {
            event(new CollaboratorsAdded($this->getKey(), collect([$attached, $detached, $updated])->unique()->flatten()));
        }
    }

    public function getRouteKeyName(): string
    {
        $length = str(request()->route('pitch'))->length();
        $uuid_length = 36;

        return $length == $uuid_length ? $this->getPublicKeyName() : $this->getKeyName();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('pitches')
            ->dontSubmitEmptyLogs();
    }

    public function assignTo(User $user): self
    {
        $this->assignee()->associate($user->getKey());

        return $this;
    }

    public function assignToAuthor(): self
    {
        $this->assigned()->associate($this->created_by);

        return $this;
    }

    public function unAssign(): self
    {
        $this->assigned()->dissociate();

        return $this;
    }

    public function addState(PitchStage $state, array $attributes = [], ?string $userId = null): self
    {
        $defaultAttributes = [
            // 'changed_by' => $user_id ?? Auth::id(),
            // 'changed_at' => now(),
        ];

        $newState = new PitchState();
        $newState->state = $state->value;
        $newState->setAttribute('attributes', array_merge($defaultAttributes, $attributes));

        $this->rawStates->push($newState);

        return $this;
    }

    public function createState(PitchStage $state, array $attributes = [], ?string $userId = null): self
    {
        $defaultAttributes = [
            // 'changed_by' => $user_id ?? Auth::id(),
            // 'changed_at' => now(),
        ];

        $newState = new PitchState();
        $newState->state = $state->value;
        $newState->setAttribute('attributes', array_merge($defaultAttributes, $attributes));

        $this->states()->save($newState);

        return $this;
    }

    public function getRelationForKey(string $key): ?Model
    {
        return match($key) {
            'assigned_to_id' => $this->assignee,
            'vertical_id' => $this->vertical,
            'topic_id' => $this->topic,
            'created_by' => $this->author,
            default => null
        };
    }

    public function assignVertical(Vertical $vertical): self
    {
        $this->vertical()->associate($vertical->getKey())->save();

        return $this;
    }

    public function assignTopic(Topic $topic): self
    {
        $this->topic()->associate($topic->getKey())->save();

        return $this;
    }

    // Check if all users are Collaborators
    public function hasCollaborators($users): bool
    {
        $collaborators = $this->collaborators->whereIn('id', $users);

        return count($users) === $collaborators->count();
    }

    public function hasAnyCollaborators($users): bool
    {
        $collaborators = $this->collaborators->whereIn('id', $users);

        return $collaborators->count() > 0;
    }

    // Check if user is Collaborator
    public function hasCollaborator(User $user): bool
    {
        return $this->collaborators->contains($user->getKey());
    }

    public function hasEditorial(): bool
    {
        return (bool) $this->editorial()->exists();
    }

    public function attachmentsCount(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->selectRaw('model_id, COUNT(*) as aggregrate')
            ->groupBy('model_id');
    }

    public function getAttachmentsCountAttribute(): int
    {
        $related = $this->getRelationValue('attachmentsCount');

        return ($related) ? (int)  $related->aggregrate : 0;
    }

    public function watchersCount(): MorphOne
    {
        return $this->morphOne(Watch::class, 'watchable')
            ->selectRaw('watchable_id, COUNT(*) as aggregrate')
            ->groupBy('watchable_id');
    }

    public function getWatchersCountAttribute(): int
    {
        $related = $this->getRelationValue('watchersCount');

        return ($related) ? (int)  $related->aggregrate : 0;
    }

    public function commentsCount(): MorphOne
    {
        return $this->morphOne(Comment::class, 'commentable')
            ->selectRaw('commentable_id, COUNT(*) as aggregrate')
            ->groupBy('commentable_id');
    }

    public function getCommentsCountAttribute(): int
    {
        $related = $this->getRelationValue('commentsCount');

        return ($related) ? (int)  $related->aggregrate : 0;
    }

    public function delete(): bool
    {
        $this->clearMediaCollection('attachments');
        parent::delete();

        return true;
    }

    public function triggerEvent(string $event): void
    {
        $this->fireModelEvent($event);
    }

    public function publicUrl(): string
    {
        return route('betterflow.v1.pitches.show', $this->getPublicKey());
    }

    public function frontendUrl(): string
    {
        return sprintf('%s/pitches/%s', config('app.frontend_url'), $this->getPublicKey());
    }

    public function addAllAttachmentsFromRequest(Request $request): Collection
    {
        $uploadedAttachments = $request->file('attachments');
        $fileAdderCollection = collect($this->addMultipleMediaFromRequest(['attachments']));
        $addedAttachmentIds = collect();

        $fileAdderCollection->each(function (FileAdder $fileAdder, int $index) use ($uploadedAttachments, $request, $addedAttachmentIds): void {
            $uploadedAttachment = $uploadedAttachments[$index];
            $attachmentExtension = $uploadedAttachment->getClientOriginalExtension();

            $addedAttachment = $fileAdder
                ->usingName($uploadedAttachment->getClientOriginalName())
                ->usingFileName(Str::uuid()->toString() . '.' . $attachmentExtension)
                ->withResponsiveImages()
                ->preservingOriginal()
                ->withCustomProperties([
                    'uploaded_by' => $request->user()->id,
                ])
                ->toMediaCollection('attachments', 'r2');

            AttachmentAdded::dispatch($this->getKey(), $addedAttachment);

            $addedAttachmentIds->add($addedAttachment->getKey());
        });

        AttachmentsAdded::dispatch($this->getKey(), $request->user(), $addedAttachmentIds->toArray());

        return $addedAttachmentIds;
    }

    public function newEloquentBuilder($query): PitchBuilder
    {
        return new PitchBuilder($query);
    }

    protected static function newFactory(): PitchFactory
    {
        return PitchFactory::new();
    }
}
