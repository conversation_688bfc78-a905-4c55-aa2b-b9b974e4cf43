<?php

namespace App\Domains\Betterflow\V1\Pitches\Models;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage as EnumsPitchStage;
use App\Domains\Shared\Models\State;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PitchState extends State
{
    protected function casts()
    {
        return [
            'attributes' => 'json',
            'state' => EnumsPitchStage::class,
        ];
    }

    public function stateModel(): BelongsTo
    {
        return $this->belongsTo(PitchStage::class, 'state', 'slug')->where('type', 'pitch');
    }
}
