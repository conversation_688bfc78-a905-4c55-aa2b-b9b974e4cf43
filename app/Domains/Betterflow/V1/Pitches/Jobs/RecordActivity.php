<?php

namespace App\Domains\Betterflow\V1\Pitches\Jobs;

use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class RecordActivity implements ShouldQueueAfterCommit
{
    use Dispatchable;
    use HandlesActivity;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    

    public function __construct(protected ActivityObject $activityObject)
    {
      $this->queue = 'compass';
    }

    public function handle(): void
    {
        $this->recordActivity($this->activityObject);
    }

    public function failed(Throwable $exception): void
    {
        \Log::error('RecordActivity job failed', [
            'exception' => $exception->getMessage(),
            'activity' => $this->activityObject,
        ]);
    }
}
