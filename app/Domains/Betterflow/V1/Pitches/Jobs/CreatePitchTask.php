<?php

namespace App\Domains\Betterflow\V1\Pitches\Jobs;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Shared\Data\ActivityObject;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Queue\Queueable;
use Throwable;

class CreatePitchTask implements ShouldQueueAfterCommit
{
    use HandlesActivity;
    use HasActivityProperties;
    use Queueable;

    public $deleteWhenMissingModels = true;

    public $tries = 3;

    public $backoff = [2, 5, 10]; // Exponential backoff in seconds

    public function __construct(
        protected string $pitchId,
        protected Task $task,
    ) {

        $this->onQueue('compass');
    }

    public function handle(): void
    {
        $pitch = Pitch::find($this->pitchId);

        if (! $pitch) {
            $this->fail(new Exception('Pitch not found'));
        }

        $pitch->createTask($this->task);

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            event: RecordableEvents::TaskCreated,
            description: 'Created a new pitch task',
            properties: [
                // ...$this->properties($pitch),
                // 'state' => $this->state($pitch),
            ],
        );

        // RecordActivity::dispatch($activity);
    }

    public function failed(Throwable $exception): void
    {
        logger('CreatePitchTask job failed', [
            'exception' => $exception->getMessage(),
            'pitchId' => $this->pitchId,
            'task' => $this->task,
        ]);
    }
}
