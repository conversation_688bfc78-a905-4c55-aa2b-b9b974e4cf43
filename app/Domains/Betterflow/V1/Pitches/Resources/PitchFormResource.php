<?php

namespace App\Domains\Betterflow\V1\Pitches\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PitchFormResource extends JsonResource
{
    private bool $onPitch = false;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->getKey(),
            'pitch_id' => $this->when(! $this->onPitch, $this->loadMissing('pitch')->pitch->getPublicKey()),
            'language_code' => $this->language_code,
            'form' => $this->form,
        ];
    }

    public function onPitch(bool $onPitch = true): self
    {
        $this->onPitch = $onPitch;

        return $this;
    }
}
