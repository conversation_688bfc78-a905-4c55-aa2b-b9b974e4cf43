<?php

namespace App\Domains\Betterflow\V1\Pitches\Policies;

use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\User;

class PitchPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Pitch $pitch): bool
    {
        if ($user->is($pitch->author)) {
            return true;
        }

        return (bool) $pitch->collaborators()->contains($user->getKey());
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Pitch $pitch): bool
    {
        // The author can update their own pitch only when the pitch is in DRAFT or REVISION state
        if ($pitch->currentStateId !== PitchStage::Draft && $pitch->currentStateId !== PitchStage::ReturnForRevision) {
            return false;
        }
        
        if ($user->is($pitch->author)) {
            return true;
        }
        
        if ($user->can(Permission::UPDATE_PITCH)) {
            return true;
        }

        return $pitch->collaborators->contains($user->getKey());
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Pitch $pitch): bool
    {
        return $user->is($pitch->author);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Pitch $pitch): bool
    {
        return $user->is($pitch->author);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Pitch $pitch): bool
    {
        return $user->is($pitch->author);
    }

    public function submit(User $user, Pitch $pitch): bool
    {
        if ($user->is($pitch->author)) {
            return true;
        }

        return $pitch->hasCollaborator($user);
    }

    public function approve(User $user, Pitch $pitch): bool
    {
        if ($user->is($pitch->vertical->editor)) {
            return true;
        }

        return $user->can(Permission::APPROVE_PITCH);
    }

    public function approveTravel(User $user, Pitch $pitch): bool
    {
        return $user->can(Permission::APPROVE_TRAVEL);
    }
}
