<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Notifications\Http\Resources\NotificationUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class AssignEditors extends Notification
{
    use Queueable;

    public function __construct(
        public ?Editorial $editorial,
        public Task $task,
        public ?User $approvedBy,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        $editorial = $this->editorial;

        return [
            'who_did_it' => NotificationUserResource::make($this->approvedBy) ?? null,
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'pitch' => NotificationSubjectResource::make($editorial->pitch),
                'task' => NotificationSubjectResource::make($this->task),
                'editorial' => NotificationSubjectResource::make($editorial),
            ]
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'assign_editors';
    }
}
