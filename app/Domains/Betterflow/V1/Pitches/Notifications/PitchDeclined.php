<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationPitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Notifications\Http\Resources\NotificationUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class PitchDeclined extends Notification
{
    use Queueable;

    public function __construct(
        public string $pitchId,
        public ?User $byUser,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        $pitch = Pitch::find($this->pitchId);

        return [
            'who_did_it' => NotificationUserResource::make($this->byUser) ?? null,
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'pitch' => NotificationPitchResource::make($pitch),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'pitch_declined';
    }
}
