<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationUserResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class EditorialCreated extends Notification
{
    use Queueable;

    public function __construct(
        public string $pitchId,
        public ?User $createdBy,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        $pitch = Pitch::find($this->pitchId);
        $editorial = $pitch->editorial;

        return [
            'who_did_it' => NotificationUserResource::make($this->createdBy) ?? null,
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'pitch' => NotificationSubjectResource::make($pitch),
                'editorial' => NotificationSubjectResource::make($editorial),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'editorial_created';
    }
}
