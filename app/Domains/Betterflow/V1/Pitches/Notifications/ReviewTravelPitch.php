<?php

namespace App\Domains\Betterflow\V1\Pitches\Notifications;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Notifications\Http\Resources\NotificationUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class ReviewTravelPitch extends Notification
{
    use Queueable;

    public function __construct(
        public string $pitchId,
        public ?User $byUser,
    ) {}

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        $pitch = Pitch::find($this->pitchId);

        return [
            'who_did_it' => NotificationUserResource::make($this->byUser) ?? null,
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'pitch' => NotificationSubjectResource::make($pitch),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'pitch_sent_for_travel_review';
    }
}
