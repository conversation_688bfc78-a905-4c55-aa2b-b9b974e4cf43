<?php

namespace App\Domains\Betterflow\V1\Pitches;

use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchApprovedHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchAwaitingTravelApprovalHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchDeclinedHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchReturnedForRevisionHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchScheduledForPitchMeetingHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchSubmittedHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\Handlers\PitchUpdatedHandler;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchApproved;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchAwaitingTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchDeclined;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchReturnedForRevision;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchScheduledForPitchMeeting;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchSubmitted;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchUpdated;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Policies\PitchPolicy;
use App\Domains\Shared\Bus\LogCommand;
use App\Domains\Shared\Bus\UseDatabaseTransactions;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class PitchesServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Gate::policy(Pitch::class, PitchPolicy::class);    //

        Bus::map([
            PitchUpdated::class => PitchUpdatedHandler::class,
            PitchSubmitted::class => PitchSubmittedHandler::class,
            PitchApproved::class => PitchApprovedHandler::class,
            PitchDeclined::class => PitchDeclinedHandler::class,
            PitchScheduledForPitchMeeting::class => PitchScheduledForPitchMeetingHandler::class,
            PitchAwaitingTravelApproval::class => PitchAwaitingTravelApprovalHandler::class,
            PitchReturnedForRevision::class => PitchReturnedForRevisionHandler::class,
        ]);

        Bus::pipeThrough([
            UseDatabaseTransactions::class,
            LogCommand::class,
        ]);
    }
}
