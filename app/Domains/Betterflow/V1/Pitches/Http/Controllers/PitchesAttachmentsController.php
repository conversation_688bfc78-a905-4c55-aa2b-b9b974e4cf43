<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers;

use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\AttachmentDeleted;
use App\Domains\Betterflow\V1\Pitches\Http\Requests\StoreAttachmentsRequest;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\AttachmentsResource;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\ActivityObject;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @tags Pitches / Attachments
 */
class PitchesAttachmentsController extends Controller
{
    public function index(Request $request, Pitch $pitch): JsonResource
    {
        $attachments = $pitch->getMedia('attachments');

        return AttachmentsResource::collection($attachments);
    }

    public function store(StoreAttachmentsRequest $request, Pitch $pitch): JsonResponse
    {
        $attachmentIds = $pitch->addAllAttachmentsFromRequest($request);

        return response()->json([
            'attachments' => $attachmentIds,
        ], 201);
    }

    public function bulkDestroy(Request $request, Pitch $pitch): JsonResponse
    {
        $recordSingleEvent = true;
        $deletedAttachments = collect();
        $deletedAttachmentNames = collect();

        $ids = $request->get('ids');
        if (empty($ids)) {
            return response()->json([
                'message' => 'No attachments selected',
            ], 204);
        }

        if (count($ids) > 1) {
            $recordSingleEvent = false;
        }

        if ($request->has('ids')) {
            foreach ($request->get('ids') as $id) {
                $media = $pitch->getMedia('attachments')->find($id);
                if ($media) {
                    $deletedAttachments->add($media);
                    $deletedAttachmentNames->add($media->name);
                    $pitch->deleteMedia($id);
                }
            }
        }

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            event: RecordableEvents::AttachmentsDeleted,
            description: 'Deleted attachments',
            properties: [
                'changes' => $deletedAttachmentNames,
            ],
        );

        RecordActivity::dispatch($activity);

        if ($recordSingleEvent) {
            AttachmentDeleted::dispatch($pitch, $request->get('ids'));
        } else {
            // TODO: implement bulk delete event
        }

        return response()->json([], 204);
    }
}
