<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\PitchResource;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserPitchesController extends Controller
{
    public function index(Request $request): JsonResource
    {
        $pitches = collect();

        $with = [
            'activities',
            'attachments',
            'attachmentsCount',
            'collaborators',
            'comments',
            'commentsCount',
            'pitchEvent',
            'revisions',
            'watchers',
            'watchersCount',
        ];

        $userPitchesBuilder = $request->user()->ownPitches()->with($with);

        $userPitches = $userPitchesBuilder->clone()->notDrafts()->get();

        $userDraftPitches = $userPitchesBuilder->clone()->drafts()->get();

        $userCollaboratingPitches = $request->user()->collaboratingPitches()->with($with)->get();

        // Followed pitches.
        $watchedPitches = $request->user()->watchedPitches()->with($with)->get();

        $pitches->put('pitches', $userPitches);
        $pitches->put('drafts', $userDraftPitches);
        $pitches->put('collaborating', $userCollaboratingPitches);
        $pitches->put('watched', $watchedPitches);

        return PitchResource::collection($pitches->map);
    }
}
