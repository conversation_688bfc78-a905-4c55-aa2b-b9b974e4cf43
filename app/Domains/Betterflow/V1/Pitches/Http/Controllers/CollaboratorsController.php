<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers;

use App\Domains\Betterflow\V1\Pitches\Actions\SyncCollaborators;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Http\Controllers\Controller;
use Illuminate\Database\DatabaseManager;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Collaborators
 */
class CollaboratorsController extends Controller
{
    public function __construct(
        protected DatabaseManager $db,
    ) {}

    public function store(Request $request, Pitch $pitch, SyncCollaborators $action): JsonResponse
    {
        $request->validate([
            'users' => ['nullable', 'array'],
            'users.*' => ['integer', 'exists:users,id', Rule::notIn([$pitch->created_by])],
        ], [
            'users.*.not_in' => 'The selected user at #:position is not allowed as a collaborator.',
        ]);

        $action->execute($pitch, $request->collect('users'));

        return response()->json($pitch->refresh(), 201);
    }
}
