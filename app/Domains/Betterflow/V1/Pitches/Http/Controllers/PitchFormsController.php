<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Models\PitchForm;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class PitchFormsController extends Controller
{
    public function index(Request $request, Pitch $pitch): JsonResponse
    {
        $languageCode = $request->input('language_code') ?? 'en-EN';

        $pitchForm = $pitch->formForLanguage($languageCode);

        if ($pitchForm !== null) {
            return response()->json($pitchForm);
        }

        return response()->json([
            'message' => 'Pitch form not found for language code: ' . $languageCode,
        ]);
    }

    public function store(Request $request, Pitch $pitch): JsonResponse
    {
        $request->validate([
            'language_code' => ['required', 'string', Rule::unique('pitch_forms', 'language_code')->where('pitch_id', $pitch->getKey())],
            'form' => ['required', 'array'],
        ], [
            'language_code.unique' => 'A pitch form already exists for language code: ' . $request->input('language_code'),
        ]);

        return response()->json($pitch->forms()->create($request->all()));
    }

    public function update(Request $request, Pitch $pitch, PitchForm $form): JsonResponse
    {
        $form->update([
            'form' => $request->all(),
        ]);

        return response()->json($form->refresh());
    }

    public function destroy(Pitch $pitch, PitchForm $form): JsonResponse
    {
        $pitch->forms()->where('id', $form->getKey())->delete();

        return response()->json([
            'message' => 'Pitch form deleted successfully',
        ]);
    }
}
