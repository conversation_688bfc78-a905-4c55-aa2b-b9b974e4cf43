<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchApproved;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Actions
 */
class ApprovePitchController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): EditorialResource
    {
        $data = $request->validate([
            'task' => ['sometimes', 'integer', 'exists:tasks,id', Rule::in($pitch->tasks()->pluck('id'))],
            'assignee' => ['sometimes', 'integer', 'exists:users,id'],
        ]);

        PitchApproved::dispatch(
            pitchId: $pitch->getKey(),
            userId: $request->user()->getKey(),
            assigneeId: data_get($data, 'assignee'),
            taskId: $data['task'] ?? null
        );

        return EditorialResource::make($pitch->fresh()->load('editorial')->editorial->loadMissing('tasks', 'assists'));
    }
}
