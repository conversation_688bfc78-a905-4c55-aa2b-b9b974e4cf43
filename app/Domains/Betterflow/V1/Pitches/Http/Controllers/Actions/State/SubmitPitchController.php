<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\PitchSubmitted;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Actions
 */
class SubmitPitchController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): JsonResponse
    {
        $data = $request->validate([
            'task' => ['sometimes', 'integer', 'exists:tasks,id', Rule::in($pitch->tasks()->pluck('id'))],
        ]);
        /* @phpstan-ignore-next-line */
        PitchSubmitted::dispatch($pitch->getKey(), $request->user()->getKey(), taskId: $data['task'] ?? null);

        return response()->json($pitch->fresh(), Response::HTTP_OK);
    }
}
