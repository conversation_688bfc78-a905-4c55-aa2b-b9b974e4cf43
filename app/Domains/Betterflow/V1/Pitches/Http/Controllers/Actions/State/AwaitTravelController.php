<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\PitchAwaitingTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Actions
 */
class AwaitTravelController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): JsonResponse
    {
        $data = $request->validate([
            'hold_until' => ['sometimes', 'date', 'after:today'],
            'task' => ['sometimes', 'integer', 'exists:tasks,id', Rule::in($pitch->tasks()->pluck('id'))],
            'instructions' => ['sometimes', 'string', 'max:1000'],
        ]);

        PitchAwaitingTravelApproval::dispatch($pitch->getKey(), $request->user()->getKey(), $data, $data['task'] ?? null);

        return new JsonResponse($pitch, JsonResponse::HTTP_OK);
    }
}
