<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\PitchReturnedForRevision;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Actions
 */
class SubmitForRevisionController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): JsonResponse
    {
        $data = $request->validate([
            'reason' => 'sometimes|string',
            'task' => ['sometimes', 'integer', 'exists:tasks,id', Rule::in($pitch->tasks()->pluck('id'))],
            'instructions' => ['sometimes', 'string', 'max:1000'],
        ]);
        /* @phpstan-ignore-next-line */
        PitchReturnedForRevision::dispatch($pitch->getKey(), $request->user()->getKey(), $data, $data['task'] ?? null);

        return new JsonResponse($pitch, Response::HTTP_OK);
    }
}
