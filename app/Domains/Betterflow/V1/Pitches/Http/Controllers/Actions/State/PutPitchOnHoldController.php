<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @tags Pitches / Actions
 */
class PutPitchOnHoldController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): JsonResponse
    {
        throw (new \Exception('Not implemented'));
    }
}
