<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\PitchDeclined;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\PitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Actions
 */
class DeclinePitchController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): JsonResponse
    {
        $data = $request->validate([
            'reason' => ['sometimes', 'string', 'max:3000'],
            'task' => ['sometimes', 'integer', Rule::exists(Task::class, 'id')],
        ]);

        PitchDeclined::dispatch($pitch->getKey(), $request->user()->getKey(), $data, $data['task'] ?? null);

        return response()->json(PitchResource::make($pitch->fresh()), Response::HTTP_OK);
    }
}
