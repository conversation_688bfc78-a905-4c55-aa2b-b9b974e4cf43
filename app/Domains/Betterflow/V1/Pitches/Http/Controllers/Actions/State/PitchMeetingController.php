<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\PitchScheduledForPitchMeeting;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;

/**
 * @tags Pitches / Actions
 */
class PitchMeetingController extends Controller
{
    public function store(Request $request, Pitch $pitch): JsonResponse
    {
        $data = $request->validate([
            'event_id' => ['required', 'string'],
            'task' => ['sometimes', 'integer', 'exists:tasks,id', Rule::in($pitch->tasks()->pluck('id'))],
        ]);

        PitchScheduledForPitchMeeting::dispatch($pitch->getKey(), $request->user()->getKey(), $data, $data['task'] ?? null);

        return new JsonResponse($pitch->refresh(), Response::HTTP_OK);
    }

    public function update(Request $request, Pitch $pitch): JsonResponse
    {
        $params = $request->validate([
            'event_id' => ['required', 'string'],
        ]);

        extract($params);

        $event_id = $params['event_id'];

        $this->db->transaction(function () use ($request, $pitch, $event_id): void {
            $pitch->reschedulePitchMeeting($event_id, rescheduledBy: $request->user());
        }, 3);

        return new JsonResponse($pitch->refresh(), Response::HTTP_OK);
    }
}
