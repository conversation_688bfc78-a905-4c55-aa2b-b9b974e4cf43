<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Jobs\ProcessPitchTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ApproveTravelController extends Controller
{
    public function __invoke(Request $request, Pitch $pitch): JsonResponse
    {
        try {
            $this->db->transaction(function (): void {
                // ProcessPitchTravelApproval::dispatchSync($pitch->getKey(), $request->user()->getKey());
            }, 3);

            return response()->json($pitch, Response::HTTP_OK);
        } catch (\Throwable $throwable) {
            $this->db->rollBack();
            throw $throwable;
        }
    }
}
