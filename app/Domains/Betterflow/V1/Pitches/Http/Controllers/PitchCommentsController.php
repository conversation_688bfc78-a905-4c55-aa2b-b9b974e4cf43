<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers;

use App\Domains\Betterflow\V1\Pitches\Actions\AddComment;
use App\Domains\Betterflow\V1\Pitches\Actions\AddReply;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Concerns\IdentifiesModel;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * @tags Pitches / Comments
 */
class PitchCommentsController extends Controller
{
    use IdentifiesModel;

    /**
     * Retrieves all comments for a given pitch.
     *
     * This method retrieves all comments associated with a pitch. It takes in a
     * request object and a pitch object, and returns a resource collection that
     * contains all of the comments for the pitch.
     *
     * @param  Request  $request  The HTTP request object.
     * @param  Pitch  $pitch  The pitch object.
     * @return ResourceCollection The resource collection containing all of the
     *                            comments for the pitch.
     */
    public function index(Request $request, Pitch $pitch): ResourceCollection
    {
        // Retrieve all comments for the pitch and return them as a resource collection.
        return CommentsResource::collection($pitch->comments);
    }

    /**
     * Store a new comment for a pitch.
     *
     * This function creates a new comment for a pitch. It takes in a request object
     * that contains the comment content and a pitch object that represents the
     * pitch to add the comment to. It returns a resource collection that contains
     * the newly created comment.
     *
     * @param  Request  $request  The HTTP request object that contains the comment
     *                            content.
     * @param  Pitch  $pitch  The pitch object that the comment should be added to.
     * @return CommentsResource The newly created comment resource.
     */
    public function store(Request $request, Pitch $pitch, AddComment $action): CommentsResource
    {
        // Create a new comment object.
        $comment = $action->execute($pitch, $request);

        // Return the newly created comment as a resource.
        return CommentsResource::make($comment);
    }

    /**
     * Store a reply to a comment.
     *
     * @param  Request  $request  The HTTP request instance.
     * @param  Comment  $comment  The comment to reply to.
     * @return CommentsResource The newly created comment resource.
     */
    public function replyStore(Request $request, Comment $comment, AddReply $action): CommentsResource
    {
        // Create a new comment instance.
        $reply = $action->execute(
            $comment,
            $request,
        );

        // Return the newly created comment resource.
        return CommentsResource::make($reply);
    }
}
