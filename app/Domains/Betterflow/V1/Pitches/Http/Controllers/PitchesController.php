<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Controllers;

use App\Domains\Betterflow\V1\Pitches\Actions\CreatePitch;
use App\Domains\Betterflow\V1\Pitches\Actions\SyncCollaborators;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchUpdated;
use App\Domains\Betterflow\V1\Pitches\Data\CreatePitchData;
use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Http\QueryFilters\FilterPitchType;
use App\Domains\Betterflow\V1\Pitches\Http\QueryFilters\FilterStage;
use App\Domains\Betterflow\V1\Pitches\Http\Requests\StorePitchRequest;
use App\Domains\Betterflow\V1\Pitches\Http\Requests\UpdatePitchRequest;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\PitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Models\PitchForm;
use App\Domains\Shared\Http\QueryFilters\FilterVertical;
use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Pipeline;
use Spatie\Permission\Exceptions\UnauthorizedException;

class PitchesController extends Controller
{
    private function with(array $relations = []): array
    {
        return [
            ...$relations,
            'activities.causer.roles',
            'activities.causer.session',
            'activities.causer',
            'activities',
            'activities',
            'assigned',
            'attachments',
            'attachmentsCount',
            'author.roles',
            'author.session',
            'collaborators.roles',
            'collaborators.session',
            'comments.replies.user.roles',
            'comments.replies.user.session',
            'comments.user.roles',
            'comments.user.session',
            'comments',
            'commentsCount',
            'pitchEvent',
            'revisions',
            'state',
            'state',
            'tasks' => fn($query) => $query->with(['taskable', 'user']),
            'topic',
            'vertical',
            'watchers.user.roles',
            'watchers.user.session',
            'watchers',
            'watchersCount',
            'editorial'
        ];
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $request->validate([
            'grouped' => ['nullable', 'boolean'],
            'drafts' => ['nullable', 'boolean'],
            'collaborating' => ['nullable', 'boolean'],
        ]);

        $includeCollaboratingPitches = $request->boolean('collaborating');
        $includeDrafts = $request->boolean('drafts');
        $groupPitches = $request->boolean('grouped');

        $with = $this->with();

        $pitchesQuery = Pipeline::send(
            Pitch::query()
                ->notDrafts()
                ->notApproved()
                ->with($with)
        )
            ->through([
                FilterVertical::class,
                FilterPitchType::class,
                FilterStage::class,
            ])->thenReturn();

        $userDraftsQuery = Pitch::query()->with($with)->ownDrafts(auth()->id());
        $collaboratingPitchesQuery = $request->user()->collaboratingPitches()->withstates(PitchStage::Draft, PitchStage::ReturnForRevision)->with($with);

        $pitchesCollection = $pitchesQuery->get()
            ->when($includeDrafts, fn(Collection $collection) => $collection->merge($userDraftsQuery->get()))
            ->when($includeCollaboratingPitches, fn(Collection $collection) => $collection->merge($collaboratingPitchesQuery->get()))
            ->when($groupPitches, fn(Collection $collection) => $collection->groupBy('currentStateId', true)->map);

        return PitchResource::collection($pitchesCollection);
    }

    public function show(Pitch $pitch): PitchResource
    {
        return PitchResource::make($pitch->loadMissing($this->with(['primaryForm'])));
    }

    public function store(StorePitchRequest $request, CreatePitch $createPitch): JsonResponse
    {
        $data = $request->except('topic', 'form', 'language_code', 'working_headline', 'notes', 'collaborators');

        $topic = $request->topic ?? null;

        $pitchData = new CreatePitchData(
            data: $data,
            topic: $topic,
        );

        if ($request->has('form') && $request->has('language_code')) {
            $pitchForm = new PitchForm();

            $pitchForm->language_code = $request->string('language_code');
            $pitchForm->form = $request->form;
            $pitchData->setForm($pitchForm);
        }

        if ($collaborators = $request->collaborators()) {
            $pitchData->setCollaborators($collaborators);
        }

        $pitch = $createPitch->execute($request->user(), $pitchData);

        return response()->json(
            PitchResource::make($pitch->fresh()->loadMissing($this->with()))->additional(['created' => true]),
            Response::HTTP_CREATED,
        );
    }

    public function update(UpdatePitchRequest $request, Pitch $pitch, SyncCollaborators $syncCollaborators): JsonResponse
    {
        // Handle Collaborators
        $syncCollaborators->execute($pitch, $request->collaborators());

        $data = $request->payload();

        $pitch->update($data);

        PitchUpdated::dispatch($pitch, $request->user(), $data);

        return response()->json(['pitch' => PitchResource::make($pitch->refresh()->loadMissing($this->with()))]);
    }

    public function destroy(Request $request, Pitch $pitch): JsonResponse
    {
        throw_unless(
            Gate::allows('delete', $pitch),
            UnauthorizedException::forPermissions(['delete pitch']),
        );

        try {
            $this->db->beginTransaction();
            $pitch->delete();
            $this->db->commit();
        } catch (\Exception $exception) {
            $this->db->rollBack();
        }

        return response()->json(['deleted' => $pitch->getPublicKey()], Response::HTTP_OK);
    }
}
