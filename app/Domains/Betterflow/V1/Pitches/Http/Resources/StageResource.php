<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->resource->loadMissing('stateModel');

        return [
            'active' => $this->stateModel?->active,
            'attributes' => $this->attributes,
            'color' => $this->stateModel?->color,
            'description' => $this->stateModel?->description,
            'name' => $this->state->niceName(),
            'slug' => $this->stateModel?->slug,
        ];
    }
}
