<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use App\Domains\Betterflow\V1\Admin\Crud\Http\Resources\CrudResource;
use Illuminate\Http\Resources\Json\JsonResource;

class SimplePitchResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->getPublicKey(),
            'short_name' => $this->short_name,
            'slug' => $this->slug,
            'angle_statement' => $this->angle_statement,
            'type' => CrudResource::make($this->whenLoaded('type')),
            'stage' => StageResource::make($this->whenLoaded('state')),
        ];
    }
}
