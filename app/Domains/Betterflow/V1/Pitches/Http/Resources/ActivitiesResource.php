<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Shared\Contracts\HasState;
use App\Domains\Users\Models\System;
use App\Domains\Users\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ActivitiesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        /**
         * "id": 1,
         * "log_name": "default",
         * "description": "created",
         * "subject_type": "pitch",
         * "event": "pitch.created",
         * "subject_id": "1",
         * "causer_type": "App\\Models\\Roles\\Reporter",
         * "causer_id": 1,
         */
        $activity = $this->resource->loadMissing('subject', 'causer');

        // Handles basic data on activity
        $data = [
            'id' => $activity->getKey(),
            'caused_by' => SimpleUserResource::make($activity->causer),
            'what_happened' => [
                'activity' => $activity->description,
                'subject' => [
                    'id' => $activity->subject->getPublicKey(),
                    'type' => $activity->subject::getSubjectName(),
                    'changes' => data_get($activity, 'properties.changes', null),

                ],
            ],
            'event' => $this->getEventName($activity),
            'created_at' => $activity->created_at,
        ];

        if ($old = data_get($activity, 'properties.old')) {
            data_set($data, 'what_happened.subject.old', $old);
        }

        if (is_null($activity->causer)) {
            $data['caused_by'] = SimpleUserResource::make(System::first());
        }

        // Handles collaborators on activity
        if ($activity->event === RecordableEvents::CollaboratorsUpdated->value) {

            $added = data_get($activity, 'properties.changes.added', []);
            $removed = data_get($activity, 'properties.changes.removed', []);
            $updated = data_get($activity, 'properties.changes.updated', []);

            $all = collect([$added, $removed, $updated])->flatten()->unique();

            $users = User::find($all);

            $addedUsers = $users->whereIn('id', $added);

            $removedUsers = $users->whereIn('id', $removed);

            $updatedUsers = $users->whereIn('id', $updated);

            data_set($data, 'what_happened.subject.changes', [
                'added' => SimpleUserResource::collection($addedUsers),
                'removed' => SimpleUserResource::collection($removedUsers),
                'updated' => SimpleUserResource::collection($updatedUsers),
            ]);
        }

        // Handles attachments on activity
        if ($activity->subject instanceof HasMedia && $attachmentIds = data_get($activity, 'properties.attachments', null)) {
            $attachmentModels = Media::query()->whereIn('id', $attachmentIds)->get();

            if ($attachmentModels->isNotEmpty()) {
                $data['attachments'] = AttachmentsResource::collection($attachmentModels);
                $data['attachment_ids'] = $attachmentIds;
            }
        }

        // Handles comment on activity
        // if ($activity->event === RecordableEvents::CommentAdded->value || $activity->event === RecordableEvents::ReplyAdded->value) {
        //     // Add likes
        //     // if ($comment = data_get($activity, 'properties.changes.comment', null)) {
        //     //     $commentModel = Comment::findOr(data_get($comment, 'id', null));

        //     //     if($commentId = data_get($comment, 'id', null)) {
        //     //         if($commentId === 2) {
        //     //         }
        //     //     }

        //     //     $data['like_count'] = $commentModel?->likes()->count() ?? 0;
        //     // }
        // }

        // Handles state on activity
        if ($activity->subject instanceof HasState && $state = data_get($activity, 'properties.state', null)) {
            $data['state'] = $state;
        }

        return $data;
    }

    protected function getEventName($activity): string
    {
        $eventName = $activity->event;
        if (preg_match('/^[A-Z]/', $eventName)) {
            return $eventName;
        }

        $subject = $activity->subject_type;

        return str($subject)->append(' ' . $eventName)->studly()->toString();
    }
}
