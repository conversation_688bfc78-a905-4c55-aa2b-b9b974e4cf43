<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationPitchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->getResourceKey(),
            'short_name' => $this->resource->short_name,
            ...$this->additional,
        ];
    }
}
