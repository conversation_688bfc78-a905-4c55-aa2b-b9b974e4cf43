<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Shared\Http\Resources\CollaboratorResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Shared\Helpers\LanguageHelper;
use App\Domains\Topics\Http\Resources\TopicResource;
use App\Domains\Verticals\Http\Resources\VerticalsResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PitchResource extends JsonResource
{
    public static $wrap;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $pitch = $this->resource;

        $isPitch = (bool) $request->route('pitch', data_get($this->additional, 'created', false));

        if ($isPitch) {
            $pitch->loadMissing('editorial');
        }

        $country = LanguageHelper::country($this->country_code);

        $primaryForm = $this->loadMissing('primaryForm')->primaryForm;

        $requiresTravel = data_get($primaryForm, 'form.requires_travel', false);
        return [
            'id' => $this->getResourceKey(),
            'assigned_to' => SimpleUserResource::make($this->whenLoaded('assigned')),
            'attachments_count' => $this->whenLoaded('attachmentsCount', $this->attachments_count),
            'collaborators' => CollaboratorResource::collection($this->whenLoaded('collaborators')),
            'comments_count' => $pitch->comments_count,
            'country' => [
                'code' => $country->code,
                'name' => $country->name,
            ],
            'created_by' => SimpleUserResource::make($this->whenLoaded('author')),
            'created_at' => $pitch->created_at,
            'form_languages' => $pitch->forms()->pluck('language_code'),
            'form_schema_version' => $pitch->form_schema_version,
            'primary_language' => $pitch->primary_language,
            'revisions_count' => $pitch->revisions_count ?? 0,
            'requires_travel' => $requiresTravel,
            'editorial_id' => $this->whenLoaded('editorial', $pitch->editorial?->getResourceKey()),
            'short_name' => $pitch->short_name,
            'scheduled_for_meeting' => $this->when($isPitch && $pitch->pitchEvent, optional($pitch->pitchEvent)->calendarEvent()),
            'slug' => $pitch->slug,
            'stage' => StageResource::make($this->whenLoaded('state')),
            'topic' => TopicResource::make($this->whenLoaded('topic')),
            'type' => $this->whenLoaded('type'),
            'vertical' => VerticalsResource::make($this->whenLoaded('vertical')),
            'watchers_count' => $pitch->watchers_count,
            'updated_at' => $pitch->updated_at,
            'watchers_ids' => $pitch->watchers()->pluck('user_id'),
            $this->mergeWhen(
                $isPitch,
                [
                    'activities' => ActivitiesResource::collection($this->whenLoaded('activities')),
                    'attachments' => AttachmentsResource::collection($this->whenLoaded('attachments')),
                    'comments' => CommentsResource::collection($this->whenLoaded('comments')),
                    'tasks' => TaskResource::collection($this->whenLoaded('tasks')),
                    'watchers' => SimpleUserResource::collection(collect($pitch->collectWatchers())),
                ]
            ),
        ];
    }
}
