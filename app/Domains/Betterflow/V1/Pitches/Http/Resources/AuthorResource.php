<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuthorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
            'photo_url' => $this->resource->getPhotoUrl(),
            'role' => $this->resource->role,
            'has_session' => $this->resource->session ?? false,
        ];
    }
}
