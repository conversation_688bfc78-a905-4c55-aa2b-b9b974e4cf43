<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AttachmentsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Media $media */
        $media = $this->resource;

        $conversions = $this->getGeneratedConversions()->map(function ($cond, $conversion) use ($media) {
            if ($cond) {
                return [
                    'key' => $conversion,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'path' => $media->getPathRelativeToRoot($conversion),
                    'url' => $media->getUrl($conversion),
                ];
            }
        });

        return [
            'id' => $media->getKey(),
            'name' => $media->name,
            'model_id' => $media->model_id,
            'model_type' => $media->model_type,
            'mime_type' => $media->mime_type,
            'size' => $media->size,
            'original_path' => $media->getPath(),
            'conversions' => $conversions,
            'full_url' => $media->getFullUrl(),
            'original_url' => $media->original_url,
            'preview_url' => $media->preview_url,
            // 'created_at' => $media->created_at,
            // 'updated_at' => $media->updated_at,
        ];
    }
}
