<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Requests;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Helpers\LanguageHelper;
use App\Rules\LanguageCodeRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePitchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', Pitch::class);
    }

    public function prepareForValidation(): void
    {
        if ($this->input('vertical')) {
            $this->merge([
                'vertical_id' => $this->input('vertical'),
            ]);
        }

        $this->offsetUnset('vertical');

        if ($this->input('language_code')) {
            $this->merge([
                'primary_language' => $this->input('language_code'),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'working_headline' => ['sometimes', 'string', 'max:255'],
            'short_name' => ['sometimes', 'string', 'max:255'],
            'slug' => ['required', 'string', 'max:255', 'unique:pitches,slug'],
            'country_code' => ['sometimes', 'string', Rule::in(LanguageHelper::countries()->pluck('code'))],
            'pitch_type' => ['sometimes', 'numeric', 'exists:pitch_types,id'],
            'assigned_to' => ['sometimes', 'numeric', 'exists:users,id'],
            'topic' => ['required', 'numeric', 'exists:topics,id'],
            'vertical_id' => ['required_with:topic', 'numeric', 'exists:verticals,id'],
            'collaborators' => ['sometimes', 'array', 'min:1', 'exists:users,id'],
            // Pitch form
            'primary_language' => ['sometimes', 'string', new LanguageCodeRule],
            'form' => ['sometimes', 'array', 'required_with:language_code'],

        ];
    }

    public function messages(): array
    {
        return [
            'topic.exists' => "This topic doesn't exist.",
        ];
    }

    public function payload(): array
    {
        return $this->except('collaborators', 'language_code', 'form');
    }

    public function collaborators(): ?array
    {
        return $this->collect('collaborators')->toArray() ?? [];
    }
}
