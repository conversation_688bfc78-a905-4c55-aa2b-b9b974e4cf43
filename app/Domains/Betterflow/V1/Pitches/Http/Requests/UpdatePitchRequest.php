<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\Requests;

use App\Domains\Topics\Models\Topic;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;

class UpdatePitchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(#[CurrentUser] $user): bool
    {
        return $user->can('update', $this->pitch);
    }

    protected function prepareForValidation(): void
    {
        if($this->has('topic')) {
            $this->merge([
                'topic_id' => $this->input('topic'),
            ]);

            $this->offsetUnset('topic');
        }

        if($this->has('vertical')) {
            $this->merge([
                'vertical_id' => $this->input('vertical'),
            ]);

            $this->offsetUnset('vertical');
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'slug' => ['sometimes', Rule::unique('pitches', 'slug')->ignore($this->pitch->getPublicKey(), 'public_id')],
            'topic_id' => ['nullable', 'numeric', 'exists:topics,id'],
            'vertical_id' => ['required_with:topic_id', 'numeric', 'exists:verticals,id', Rule::in(Topic::find($this->input('topic_id'))?->loadMissing('verticals')->verticals->pluck('id'))],
            'collaborators' => ['sometimes', 'array'],
            'collaborators.*' => ['exists:users,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'topic_id.exists' => "This topic doesn't exist.",
            'vertical_id.in' => 'This vertical does not belong to this topic.',
        ];
    }

    public function payload(): array
    {
        $data = $this->except('collaborators', 'form');

        return [
            ...$data,
        ];
    }

    public function collaborators(): ?Collection
    {
        return $this->collect('collaborators');
    }
}
