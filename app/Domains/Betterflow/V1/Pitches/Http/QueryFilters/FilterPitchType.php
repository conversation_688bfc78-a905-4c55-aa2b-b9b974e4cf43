<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\QueryFilters;

use App\Domains\Shared\Helpers\CrudHelper;
use App\Domains\Shared\Http\QueryFilters\FilterRequest;
use Closure;
use Illuminate\Contracts\Database\Eloquent\Builder;

class FilterPitchType extends FilterRequest
{
    public function handle(Builder $query, Closure $next): Builder
    {
        $query->when($this->request->pitch_type, function ($query, $pitch_type): void {
            $pitch_type = str($pitch_type)->explode(',');
            $pitch_types = CrudHelper::pitch_types()->whereIn('slug', $pitch_type)->pluck('id');
            $query->whereIn('pitch_type', $pitch_types);
        });

        return $next($query);
    }
}
