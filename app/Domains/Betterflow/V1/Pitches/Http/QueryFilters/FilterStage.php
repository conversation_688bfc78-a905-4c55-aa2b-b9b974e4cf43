<?php

namespace App\Domains\Betterflow\V1\Pitches\Http\QueryFilters;

use App\Domains\Shared\Helpers\CrudHelper;
use App\Domains\Shared\Http\QueryFilters\FilterRequest;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class FilterStage extends FilterRequest
{
    public function handle(Builder $query, Closure $next): Builder
    {
        $query->when($this->request->stage, function ($query, $pitch_stage): void {
            $pitch_stage = str($pitch_stage)->explode(',');
            $pitch_stages = CrudHelper::pitch_stages()->whereIn('slug', $pitch_stage)->pluck('slug');

            $query->whereHas('state', function ($query) use ($pitch_stages): void {
                $query->whereIn('state', $pitch_stages);
            });
        });

        return $next($query);
    }
}
