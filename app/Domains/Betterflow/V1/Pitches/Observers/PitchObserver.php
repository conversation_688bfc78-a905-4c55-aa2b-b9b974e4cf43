<?php

namespace App\Domains\Betterflow\V1\Pitches\Observers;

use App\Domains\Betterflow\V1\Pitches\Enums\PitchStage;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;

class PitchObserver
{
    public function approving(Pitch $pitch): void
    {
    }

    public function approved(Pitch $pitch): void
    {
    }

    public function declining(Pitch $pitch): void
    {
    }

    public function declined(Pitch $pitch): void
    {
    }

    public function creating(Pitch $pitch): void
    {
    }

    public function created(Pitch $pitch): void
    {
        $pitch->states()->create([
            'state' => PitchStage::Draft,
            'attributes' => [
                'created_by' => $pitch->author->getKey(),
            ],
        ]);
    }

    public function topicUpdated(Pitch $pitch, ?int $topic = null): void
    {
    }
}
