<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners;

use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\PitchAssigned;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchAssigned implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

    public $queue = 'compass';


    public function handle(PitchAssigned $event): void
    {
        $assignedTo = User::find($event->assignedId);
        $pitch = Pitch::find($event->pitchId);

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            event: RecordableEvents::AssignedTo,
            description: 'Assigned this pitch to',
            properties: ['changes' => $assignedTo],
        );

        RecordActivity::dispatch($activity);
    }
}
