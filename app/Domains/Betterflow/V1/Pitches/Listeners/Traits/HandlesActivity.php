<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Traits;

use App\Domains\Shared\Data\ActivityObject;

trait HandlesActivity
{
    public function recordActivity(ActivityObject $activity): void
    {
        $newActivity = activity($activity->logName);

        $newActivity
            ->on($activity->on)
            ->event($activity->event->value)
            ->withProperties($activity->properties);
        if ($by = data_get($activity, 'by')) {
            $newActivity->by($by);
        } else {
            $newActivity->byAnonymous();
        }

        $newActivity->log($activity->description);
    }
}
