<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners;

use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\AttachmentsAdded;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandleAttachmentsAdded implements ShouldQueue
{
    use InteractsWithQueue;

      

    /**
     * Handle the event.
     */
    public function handle(AttachmentsAdded $event): void
    {
        $pitch = Pitch::find($event->pitchId);
        $byUser = $event->byUser;

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $byUser->getKey(),
            event: RecordableEvents::AttachmentsDeleted,
            description: 'uploaded new file/s',
            properties: ['attachments' => $event->attachmentIds],
        );

        RecordActivity::dispatch($activity);
    }
}
