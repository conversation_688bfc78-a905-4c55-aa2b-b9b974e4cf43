<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners;

use App\Domains\Betterflow\V1\Pitches\Events\CommentAdded;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Comments\Http\Resources\ActivityCommentResource;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandleCommentAdded implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

      

    /**
     * Handle the event.
     */
    public function handle(CommentAdded $event): void
    {
        // $pitch = Pitch::find($event->pitchId);

        // $activity = new ActivityObject(
        //     logName: 'pitches',
        //     on: $pitch,
        //     by: $event->user->getKey(),
        //     event: 'CommentAdded',
        //     description: 'Posted a comment',
        //     properties: [
        //         'changes' => [
        //             'comment' => ActivityCommentResource::make($event->comment),
        //         ],
        //     ],
        // );

        // $this->recordActivity($activity);
    }
}
