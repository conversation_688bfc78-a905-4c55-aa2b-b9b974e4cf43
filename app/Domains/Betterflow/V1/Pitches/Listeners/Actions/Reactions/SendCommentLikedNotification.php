<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\Reactions;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Pitches\Events\Reactions\LikedComment;
use App\Domains\Comments\Http\Resources\ActivityCommentResource;
use App\Domains\Comments\Models\Comment;
use App\Domains\Users\Models\User;

class SendCommentLikedNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LikedComment $event): void
    {
        $user = User::find($event->userId);
        $comment = Comment::find($event->commentId);

        activity('comments')->event('LikedComment')
            ->by($user)
            ->on($comment)
            ->withProperties(['user' => SimpleUserResource::make($user), 'comment' => ActivityCommentResource::make($comment)])
            ->log('Liked comment');
        // TODO: send notification
    }
}
