<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\Reactions;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Pitches\Events\Reactions\UnlikedComment;
use App\Domains\Comments\Http\Resources\ActivityCommentResource;
use App\Domains\Comments\Models\Comment;
use App\Domains\Users\Models\User;

class SendCommentUnlikedNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UnlikedComment $event): void
    {
        $user = User::find($event->userId);
        $comment = Comment::find($event->commentId);
        activity('comments')->event('UnlikedComment')
            ->by($user)
            ->on($comment)
            ->withProperties(['user' => SimpleUserResource::make($user), 'comment' => ActivityCommentResource::make($comment)])
            ->log('Unliked comment');
        // TODO: send notification
    }
}
