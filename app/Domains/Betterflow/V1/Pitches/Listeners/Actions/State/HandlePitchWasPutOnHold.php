<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchPutOnHold;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchWasPutOnHold implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

      

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PitchPutOnHold $event): void {}
}
