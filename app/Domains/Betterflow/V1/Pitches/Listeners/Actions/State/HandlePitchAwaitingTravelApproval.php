<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchAwaitingTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Notifications\ReviewTravelPitch;
use App\Domains\Users\Models\EditorialAdmin;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchAwaitingTravelApproval implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

      

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PitchAwaitingTravelApproval $event): void
    {
        $pitch = $event->pitch;

        $user = $pitch->author;
        $collaborators = $pitch->collaborators;

        // Create pitch awaiting travel approval notifications.
        // $user->notify(new ReviewTravelPitch($pitch->getKey()));
        // $collaborators->each->notify(new ReviewTravelPitch($pitch->getKey()));

        // Send to other editors and admins.
        // $managingEditors = ManagingEditor::get();

        // $managingEditors->each->notify(new ReviewTravelPitch($pitch->getKey()));

        $editorialAdmin = EditorialAdmin::get();

        $editorialAdmin->each->notify(new ReviewTravelPitch($pitch->getKey(), $event->user));
    }
}
