<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchTravelApproved;
use App\Domains\Betterflow\V1\Pitches\Events\PitchAssigned;
use App\Domains\Betterflow\V1\Pitches\Jobs\CreatePitchTask;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandleTravelApproved implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

      

    public function handle(PitchTravelApproved $event): void
    {

        // $pitch = Pitch::find($event->pitchId);
        // $user = User::find($event->userId);

        // $this->recordActivity(new ActivityObject(
        //     logName: 'pitches',
        //     on: $pitch,
        //     by: $user->getKey(),
        //     event: 'pitch:approved_for_travel',
        //     description: "Pitch was approved by {$user->name}",
        //     properties: ['pitch' => $pitch, 'attributes' => $pitch->state],
        // ));

        // if ($editor = Editor::first()) {
        //     $pitch->assignTo($editor);

        //     PitchAssigned::dispatch($pitch->getKey(), $editor->getKey(), $user->getKey());

        //     // Create a task
        //     $task =new Task(
        //         userId: $editor->getKey(),
        //         title: 'Approve Travel Pitch',
        //         description: 'You need to review this pitch.',
        //         type: PitchTaskType::ReviewPitch,
        //         dueAt: now()->addDays(2),
        //         data: new TaskData(
        //             pitch: $pitch,
        //             label: 'Approve pitch',
        //             url: $pitch->frontendUrl()
        //         )
        //     );

        //     CreatePitchTask::dispatch($pitch->getKey(), $task);
        // }

        // $pitch->persist();
    }
}
