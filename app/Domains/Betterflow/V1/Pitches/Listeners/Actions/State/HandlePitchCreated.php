<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\PitchCreated;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchCreated implements ShouldQueueAfterCommit
{
    use HandlesActivity;
    use HasActivityProperties;
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    /**
     * Handle the event.
     */
    public function handle(PitchCreated $event): void
    {
        $pitch = $event->pitch;

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $pitch->created_by,
            event: RecordableEvents::DraftCreated,
            description: 'Drafted a pitch',
            properties: $this->properties($pitch),
        );

        $this->recordActivity($activity);
    }
}
