<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\PitchUpdated;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchUpdated implements ShouldQueue
{
    use HasActivityProperties;
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(PitchUpdated $event): void
    {
        // $pitch = $event->pitch;

        // $activity = new ActivityObject(
        //     logName: 'pitches',
        //     on: $pitch,
        //     by: $event->user->getKey(),
        //     event: 'PitchUpdated',
        //     description: 'Updated this pitch',
        //     properties: $this->properties($pitch),
        // );

        // RecordActivity::dispatch($activity);
    }
}
