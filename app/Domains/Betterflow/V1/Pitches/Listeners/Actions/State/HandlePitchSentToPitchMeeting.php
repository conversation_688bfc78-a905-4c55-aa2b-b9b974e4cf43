<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchScheduledForPitchMeeting;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Notifications\PitchScheduledForMeeting;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchSentToPitchMeeting implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

      

    public $tries = 3;

    public $backoff = [2, 5, 10]; // Exponential backoff in seconds

    /**
     * Handle the event.
     */
    public function handle(PitchScheduledForPitchMeeting $event): void
    {
        try {
            $pitch = $event->pitch;
            $userWhoDidIt = $event->user;
            $author = $pitch->author;
            $collaborators = $pitch->collaborators;

            // Create pitch sent to pitch meeting notifications.
            $author->notify(new PitchScheduledForMeeting($pitch->getKey(), $userWhoDidIt));
            $collaborators->each->notify(new PitchScheduledForMeeting($pitch->getKey(), $userWhoDidIt));
        } catch (\Throwable $exception) {
            // Log the error for debugging
            \Log::error('Failed to send pitch meeting notifications', [
                'pitch_id' => $event->pitch->getKey(),
                'exception' => $exception->getMessage(),
            ]);

            // Rethrow to trigger retry mechanism
            throw $exception;
        }
    }

    /**
     * Handle a job failure after all retry attempts.
     */
    public function failed(PitchScheduledForPitchMeeting $event, \Throwable $exception): void
    {
        \Log::critical('All retry attempts failed for pitch meeting notifications', [
            'pitch_id' => $event->pitch->getKey(),
            'exception' => $exception->getMessage(),
        ]);

        // Optionally, you could add additional error handling here
        // For example, sending an alert to an admin or support team
    }
}
