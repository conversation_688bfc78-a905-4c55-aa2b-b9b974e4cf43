<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchDeclined;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Notifications\PitchDeclined as NotificationsPitchDeclined;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchDeclined implements ShouldQueueAfterCommit
{
    use HandlesActivity;
    use InteractsWithQueue;

      

    /**
     * Handle the event.
     */
    public function handle(PitchDeclined $event): void
    {
        $pitch = $event->pitch;

        $user = $pitch->author;
        $collaborators = $pitch->collaborators;

        // Create pitch declined notifications.
        $user->notify(new NotificationsPitchDeclined($pitch->getKey(), $event->user));
        $collaborators->each->notify(new NotificationsPitchDeclined($pitch->getKey(), $event->user));
    }
}
