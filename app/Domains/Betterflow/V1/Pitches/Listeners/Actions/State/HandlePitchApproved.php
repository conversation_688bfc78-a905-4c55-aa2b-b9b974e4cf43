<?php

namespace App\Domains\Betterflow\V1\Pitches\Listeners\Actions\State;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchApproved;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Pitches\Notifications\EditorialCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandlePitchApproved implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    /**
     * Handle the event.
     */
    public function handle(PitchApproved $event): void
    {
        $pitch = $event->pitch;

        $user = $pitch->author;
        $collaborators = $pitch->collaborators;

        // Create pitch approved notifications.
        $user->notify(new EditorialCreated($pitch->getKey(), $event->user));
        $collaborators->each->notify(new EditorialCreated($pitch->getKey(), $event->user));
    }
}
