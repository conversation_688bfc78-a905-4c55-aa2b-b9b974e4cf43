<?php

namespace App\Domains\Betterflow\V1\Pitches\Data;

use App\Domains\Betterflow\V1\Pitches\Models\PitchForm;
use <PERSON>tie\LaravelData\Data;

final class CreatePitchData extends Data
{
    public function __construct(
        public readonly array $data,
        public readonly ?int $topic,
        protected ?array $collaborators = null,
        protected ?PitchForm $form = null,
    ) {}

    public function setForm(PitchForm $form): self
    {
        $this->form = $form;

        return $this;
    }

    public function getForm(): ?PitchForm
    {
        return $this->form;
    }

    public function setCollaborators(array $collaborators = []): self
    {
        $this->collaborators = $collaborators;

        return $this;
    }

    public function getCollaborators(): ?array
    {
        return $this->collaborators;
    }
}
