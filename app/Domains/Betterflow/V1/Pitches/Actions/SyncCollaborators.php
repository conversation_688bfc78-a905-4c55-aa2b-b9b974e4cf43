<?php

namespace App\Domains\Betterflow\V1\Pitches\Actions;

use App\Domains\Betterflow\V1\Pitches\Events\Actions\CollaboratorsAdded;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SyncCollaborators
{
    public function execute(Pitch $pitch, Collection $collaborators): void
    {
        DB::transaction(function () use ($pitch, $collaborators) {
            if ($collaborators->isEmpty()) {
                $pitchCollaborators = $pitch->collaborators->pluck('id');
                $pitchCollaborators->each(fn($collaborator) => $pitch->unwatch($collaborator));
            } else {
                $collaborators->each(fn($user) => $pitch->watch($user));
            }

            $pitch->collaborators()->sync($collaborators);

            CollaboratorsAdded::dispatch($pitch->getKey(), $collaborators, auth()->user());

            return $pitch->fresh();
        });
    }
}
