<?php

namespace App\Domains\Betterflow\V1\Pitches\Actions;

use App\Domains\Comments\Models\Comment;
use Illuminate\Http\Request;

class AddReply
{
    public function execute(Comment $comment, Request $request): Comment
    {
        $reply = new Comment();

        // Set the comment and parent ID.
        $reply->comment = $request->get('comment');
        $reply->parent_id = $comment->getKey();

        // Associate the comment with the authenticated user.
        $reply->user()->associate($request->user());

        // Associate the comment with the commentable model.
        $reply->commentable()->associate($comment);

        // Associate the comment with the commentable model.
        $comment->replies()->save($reply);

        // Save the comment to the database.
        $reply->save();

        return $reply;
    }
}
