<?php

namespace App\Domains\Betterflow\V1\Pitches\Actions;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Comments\Models\Comment;
use Illuminate\Http\Request;

class AddComment
{
    public function execute(Pitch $pitch, Request $request): Comment
    {
        $comment = new Comment();

        // Set the comment content.
        $comment->comment = $request->get('comment');

        // Set the parent comment ID to null.
        $comment->parent_id = null;

        // Associate the comment with the current user.
        $comment->user()->associate($request->user());

        // Save the comment to the pitch.
        $pitch->comments()->save($comment);

        return $comment;
    }
}
