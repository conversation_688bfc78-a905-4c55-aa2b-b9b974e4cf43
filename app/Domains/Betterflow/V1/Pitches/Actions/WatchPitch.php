<?php

namespace App\Domains\Betterflow\V1\Pitches\Actions;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\PitchResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use Illuminate\Container\Attributes\CurrentUser;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class WatchPitch
{
    use AsAction;

    public function authorize(#[CurrentUser] $user, Pitch $pitch): bool
    {
        return true;
    }

    public function handle(ActionRequest $request, Pitch $pitch): Pitch
    {
        $pitch->toggleWatch($request->user()->getKey());

        return $pitch->refresh();
    }

    public function asController(ActionRequest $request, Pitch $pitch): Pitch
    {
        return $this->handle($request, $pitch);
    }

    public function jsonResponse(Pitch $pitch): PitchResource
    {
        return PitchResource::make($pitch);
    }
}
