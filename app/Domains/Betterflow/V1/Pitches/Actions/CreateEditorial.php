<?php

namespace App\Domains\Betterflow\V1\Pitches\Actions;

use App\Domains\Betterflow\V1\Editorials\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialCreated;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;

class CreateEditorial
{
    public function execute(Pitch $pitch, User $assignee): Editorial
    {
        $editorial = new Editorial();
        $editorial->reporter()->associate($pitch->author);
        $editorial->assignee()->associate($assignee);

        $editorial->name = str($pitch->short_name)->headline();

        /* @phpstan-ignore-next-line */
        $pitch->editorial()->save(
            $editorial,
        )->createInitialStage();

        $activity = new ActivityObject(
            logName: 'editorials',
            description: 'Created an editorial',
            on: $editorial,
            event: RecordableEvents::EditorialCreated,
        );

        RecordActivity::dispatch($activity);

        EditorialCreated::dispatch($editorial->getKey());

        return $editorial;
    }
}
