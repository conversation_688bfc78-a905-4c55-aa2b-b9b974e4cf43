<?php

namespace App\Domains\Betterflow\V1\Pitches\Actions;

use App\Domains\Betterflow\V1\Pitches\Data\CreatePitchData;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\CollaboratorsAdded;
use App\Domains\Betterflow\V1\Pitches\Events\PitchCreated;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Users\Models\User;
use Illuminate\Database\DatabaseManager;

class CreatePitch
{
    public function __construct(
        protected DatabaseManager $db,
    ) {}

    /**
     * @throws \Throwable
     */
    public function execute(User $user, CreatePitchData $data): Pitch
    {
        $pitch = $this->db->transaction(callback: function () use ($data, $user) {

            /* @var Pitch $pitch */
            $pitch = $user->ownPitches()->create([
                ...$data->data,
            ]);

            if ($data->topic) {
                $pitch->topic()->associate($data->topic)->save();
                $pitch->vertical()->associate($data->data['vertical_id'])->save();
                $pitch->triggerEvent('topicUpdated');
            }

            if ($data->getForm() !== null) {
                $pitch->forms()->save($data->getForm());
            }

            if ($collaborators = $data->getCollaborators()) {
                $pitch->collaborators()->sync($collaborators);
                $pitch->recordEvent(new CollaboratorsAdded($pitch->getKey(), collect($collaborators)));
            }

             /* @phpstan-ignore-next-line */
            $pitch->recordEvent(new PitchCreated($pitch));

            $pitch->persist();

            return $pitch;
        }, attempts: 3);      

        /* @phpstan-ignore-next-line */
        return $pitch;
    }
}
