<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Actions\CreateEditorial;
use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchApproved;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchApproved as EventsPitchApproved;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Pitches\Notifications\AssignEditors;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;

class PitchApprovedHandler
{
    use HasActivityProperties;

    public function __construct(
        private CreateEditorial $createEditorial,
    ) {}

    public function handle(PitchApproved $command): void
    {
        $pitch = Pitch::query()->findOrFail($command->pitchId);
        $user = User::query()->findOrFail($command->userId);
        $assignee = User::query()->findOrFail(
            id: $command->assigneeId
        );

        $task = $command->task;

        $pitch->approve(approvedBy: $user);

        $task?->markComplete();

        if(!app()->runningInConsole()) {
            $pitch->persist();
        }
        
        /* @phpstan-ignore-next-line */
        // TODO: Assign assignee to the editorial
        $editorial = $this->createEditorial->execute($pitch, $assignee);
        
        if ($assignee) {
            $task = new Task(
                userId: $assignee->getKey(),
                title: 'Assign Editors',
                description: 'You need to assign initial team and assets.',
                type: EditorialTaskType::AssignEditors,
                data: new TaskData(
                    model: $editorial,
                    label: 'Assign team and assets',
                ),
            );

            $newTask = $editorial->createTask($task, false);

            // Vertical Editor eg. (Florencia)
            $assignee->notify(new AssignEditors(
                editorial: $editorial,
                task: $newTask,
                approvedBy: $user,
            ));
        }

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $user->getKey(),
            event: RecordableEvents::PitchStageChanged,
            description: 'Approved this pitch',
            properties: [...$this->properties($pitch), 'state' => $this->state($pitch)],
        );

        RecordActivity::dispatch($activity);

        EventsPitchApproved::dispatch($pitch->getKey(), $user->getKey());
    }
}
