<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchAwaitingTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchAwaitingTravelApproval as EventPitchAwaitingTravelApproval;
use App\Domains\Betterflow\V1\Pitches\Events\PitchAssigned;
use App\Domains\Betterflow\V1\Pitches\Jobs\CreatePitchTask;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\EditorialAdmin;
use App\Domains\Users\Models\User;

class PitchAwaitingTravelApprovalHandler
{
    use HasActivityProperties;

    public function handle(PitchAwaitingTravelApproval $command): void
    {
        $pitch = Pitch::find($command->pitchId);
        $user = User::find($command->userId);
        $holdUntil = data_get($command->data, 'hold_until');

        $task = $command->task;

        // $collaborators = $pitch->collaborators;
        // Notification::send($collaborators->push($pitch->author), new PitchStateChanged($pitch->getKey(), $user->getKey(), $pitch->state));
        $pitch->travelApproval(requestedBy: $user, holdUntil: $holdUntil);

        $task?->markComplete();

        // TODO: Assign EditorialAdmin to the pitch
        if ($editorialAdmin = EditorialAdmin::first()) {
            $pitch->assignTo($editorialAdmin);

            PitchAssigned::dispatch($pitch->getKey(), $editorialAdmin->getKey(), $user->getKey());

            $instructions = data_get($command->data, 'instructions');
            // TODO: This creates a task for EditorialAdmin to review travel approval.
            // Create a task
            $task = new Task(
                userId: $editorialAdmin->getKey(),
                title: 'Review Travel Pitch',
                description: 'You need to review this pitch',
                type: PitchTaskType::TravelApproval,
                dueAt: now()->addDays(2),
                data: new TaskData(
                    model: $pitch,
                    label: 'Review pitch',
                    properties: compact('instructions'),
                ),
            );

            CreatePitchTask::dispatch($pitch->getKey(), $task);
        }

        $pitch->persist();

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $user->id,
            event: RecordableEvents::PitchStageChanged,
            description: 'Sent this pitch for travel approval',
            properties: [...$this->properties($pitch), 'state' => $this->state($pitch)],
        );

        RecordActivity::dispatch($activity);

        EventPitchAwaitingTravelApproval::dispatch($pitch->getKey(), $user->getKey());
    }
}
