<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchSubmitted;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State;
use App\Domains\Betterflow\V1\Pitches\Events\PitchAssigned;
use App\Domains\Betterflow\V1\Pitches\Jobs\CreatePitchTask;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;
use Throwable;

class PitchSubmittedHandler
{
    use HasActivityProperties;

    public function handle(PitchSubmitted $command): void
    {
        $pitch = Pitch::find($command->pitchId);
        $user = User::find($command->userId);

        $task = $command->task;

        rescue(
            function () use ($pitch, $user, $task): void {
                $pitch->submit(submittedBy: $user);

                $task?->markComplete();

                /* @phpstan-ignore-next-line */
                $this->handleSubmission($pitch, $user);                
        
                $activity = new ActivityObject(
                    logName: 'pitches',
                    on: $pitch,
                    by: $user->getKey(),
                    event: RecordableEvents::PitchStageChanged,
                    description: 'Submitted this pitch',
                    properties: [
                        ...$this->properties($pitch),
                        'state' => $this->state($pitch),
                    ],
                );

                unset($pitch->collaborators);

                // if(!app()->runningInConsole()) {
                    $pitch->persist();
                // }

                RecordActivity::dispatch($activity);
            },
            report: function (Throwable $throwable): void {
                report($throwable);
                throw $throwable;
            },
        );

        State\PitchSubmitted::dispatch($pitch->getKey(), $user->getKey());
    }

    private function handleSubmission(Pitch $pitch, User $user): void
    {
        if ($verticalEditor = $pitch->vertical->editor) {
            $pitch->assignTo($verticalEditor);

            PitchAssigned::dispatch($pitch->getKey(), $verticalEditor->getKey(), $user->getKey());

            $task = new Task(
                userId: $verticalEditor->getKey(),
                title: 'Review Pitch',
                description: 'You need to review this pitch',
                type: PitchTaskType::ReviewPitch,
                dueAt: null,
                data: new TaskData(
                    model: $pitch,
                    label: 'Review pitch',
                ),
            );

            CreatePitchTask::dispatch($pitch->getKey(), $task);
        }
    }
}
