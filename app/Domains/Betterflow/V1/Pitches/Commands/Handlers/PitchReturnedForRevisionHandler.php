<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchReturnedForRevision;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchReturnedForRevision as EventPitchReturnedForRevision;
use App\Domains\Betterflow\V1\Pitches\Jobs\CreatePitchTask;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;

class PitchReturnedForRevisionHandler
{
    use HasActivityProperties;

    public function handle(PitchReturnedForRevision $command): void
    {

        $reason = data_get($command->data, 'reason');

        $pitch = Pitch::findOrFail($command->pitchId);
        $user = User::findOrFail($command->userId);

        $task = $command->task;

        $pitch->returnForRevision($user, $reason);

        $task?->markComplete();

        $pitch->assignToAuthor();

        $instructions = data_get($command->data, 'instructions');

        $task = new Task(
            userId: $pitch->created_by,
            title: 'Your pitch needs to be revised.',
            description: 'Intructions provided to review this Pitch. Make your changes and resubmit this Pitch',
            type: PitchTaskType::RevisePitch,
            dueAt: null,
            data: new TaskData(
                model: $pitch,
                label: 'Update Pitch',
                properties: compact('instructions'),
            ),
        );

        CreatePitchTask::dispatch($pitch->getKey(), $task);

        if(!app()->runningInConsole()) {
            $pitch->persist();
        }

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $user->getKey(),
            event: RecordableEvents::PitchStageChanged,
            description: 'Returned this pitch for revision',
            properties: [...$this->properties($pitch), 'state' => $this->state($pitch)],
        );

        RecordActivity::dispatch($activity);

        EventPitchReturnedForRevision::dispatch($pitch->getKey(), $user->getKey());
    }
}
