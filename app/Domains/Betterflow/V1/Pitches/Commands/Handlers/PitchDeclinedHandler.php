<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchDeclined;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchDeclined as EventPitchDeclined;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;

class PitchDeclinedHandler
{
    use HasActivityProperties;

    public function handle(PitchDeclined $command): void
    {
        $pitch = Pitch::findOrFail($command->pitchId);
        $user = User::findOrFail($command->userId);

        $task = $command->task;
        $reason = data_get($command->data, 'reason');

        $pitch->decline($user, $reason);

        $task?->markComplete();

        $pitch->persist();

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $user->getKey(),
            event: RecordableEvents::PitchStageChanged,
            description: 'Declined this pitch',
            properties: [...$this->properties($pitch), 'state' => $this->state($pitch)],
        );

        RecordActivity::dispatch($activity);

        EventPitchDeclined::dispatch($pitch->getKey(), $user->getKey());
    }
}
