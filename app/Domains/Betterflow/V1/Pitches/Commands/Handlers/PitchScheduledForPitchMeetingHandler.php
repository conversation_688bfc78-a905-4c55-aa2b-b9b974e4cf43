<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchScheduledForPitchMeeting;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\State\PitchScheduledForPitchMeeting as EventPitchScheduledForPitchMeeting;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;

class PitchScheduledForPitchMeetingHandler
{
    use HasActivityProperties;

    public function handle(PitchScheduledForPitchMeeting $command): void
    {
        $eventId = data_get($command->data, 'event_id');

        $pitch = Pitch::findOrFail($command->pitchId);
        $user = User::findOrFail($command->userId);

        $task = $command->task;

        $pitch->scheduleToPitchMeeting($eventId, scheduledBy: $user);

        $task?->markComplete();

        $pitch->persist();

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $user->getKey(),
            event: RecordableEvents::PitchStageChanged,
            description: 'Scheduled this pitch for a meeting',
            properties: [
                ...$this->properties($pitch),
                'state' => $this->state($pitch),
            ],
        );

        RecordActivity::dispatch($activity);

        EventPitchScheduledForPitchMeeting::dispatch($pitch->getKey(), $user->getKey());
    }
}
