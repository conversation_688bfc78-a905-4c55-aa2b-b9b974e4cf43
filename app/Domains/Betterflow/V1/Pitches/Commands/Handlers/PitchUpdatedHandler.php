<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Handlers;

use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Commands\PitchUpdated;
use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Pitches\Events\Actions\PitchUpdated as EventsPitchUpdated;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Shared\Data\ActivityObject;

class PitchUpdatedHandler
{
    use HasActivityProperties;

    public function handle(PitchUpdated $command): void
    {
        $pitch = $command->pitch;
        $user = $command->user;
        $data = $command->data;

        $rawOriginal = $pitch->getRawOriginal();

        $pitch->fill($data);

        $activity = new ActivityObject(
            logName: 'pitches',
            on: $pitch,
            by: $user->getKey(),
            event: RecordableEvents::PitchUpdated,
            description: 'Updated this pitch',
            properties: [
                'changes' => collect($pitch->getDirty())->diffAssoc($rawOriginal)->except(['id', 'public_id', 'created_at', 'updated_at']),
                'old' => $rawOriginal,
            ],
        );

        RecordActivity::dispatchSync($activity);

        unset($pitch->form);
        unset($pitch->collaborators);

        $pitch->recordEvent(new EventsPitchUpdated($pitch, $user));
        $pitch->persist();
    }
}
