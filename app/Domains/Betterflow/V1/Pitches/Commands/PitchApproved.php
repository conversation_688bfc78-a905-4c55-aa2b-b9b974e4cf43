<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands;

use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Contracts\Command;
use Illuminate\Foundation\Bus\Dispatchable;

class PitchApproved implements Command 
{
    use Dispatchable;

    public ?Task $task = null;

    public function __construct(
        public readonly string $pitchId,
        public readonly string $userId,
        public readonly ?int $assigneeId = null,
        public ?array $data = null,
        public ?string $taskId = null,
    ) {
        if ($taskId) {
            $this->task = Task::find($taskId);
        }
    }
}
