<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands;

use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Foundation\Bus\Dispatchable;

abstract class PitchStateCommand
{
    use Dispatchable;

    public ?Task $task = null;

    public function __construct(
        public readonly string $pitchId,
        public readonly string $userId,
        public readonly ?array $data = null,
        public readonly ?string $taskId = null,
    ) {
        if ($taskId) {
            $this->task = Task::find($taskId);
        }
    }
}
