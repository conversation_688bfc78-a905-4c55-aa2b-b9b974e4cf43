<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands;

use App\Domains\Betterflow\V1\Pitches\Data\PitchWithCommand;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Contracts\Command;
use Closure;

class CommandsPipeHandler
{

    public function __construct(
        protected Command $command,
    ) {}

    public function handle(Pitch $pitch, Closure $next)
    {
        dispatch($this->command);

        return $next($pitch->refresh());
    }
}
