<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Contracts\Command;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Bus\Dispatchable;

class PitchUpdated implements Command
{
    use Dispatchable;

    public function __construct(
        public Pitch $pitch,
        public ?User $user = null,
        public ?array $data = null,
    ) {}
}
