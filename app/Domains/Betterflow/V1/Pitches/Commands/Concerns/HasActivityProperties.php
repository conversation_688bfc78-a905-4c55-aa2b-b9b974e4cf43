<?php

namespace App\Domains\Betterflow\V1\Pitches\Commands\Concerns;

use App\Domains\Betterflow\V1\Pitches\Enums\RecordableEvents;
use Illuminate\Database\Eloquent\Model;

trait HasActivityProperties
{
    public function changes(Model $model): mixed
    {
        $attributes = $model->fresh()->getAttributes();

        $lastStateModel = $model->fresh()->activities()->latest()->forEvents([RecordableEvents::DraftCreated, RecordableEvents::PitchUpdated])->first();

        $lastState = data_get($lastStateModel, 'properties.old', []);

        $diff = collect($attributes)->diffAssoc($lastState);

        return [
            'changes' => $diff->except(['id', 'public_id', 'created_at', 'updated_at']),
            'old' => $attributes,
        ];
    }

    public function properties(Model $model): array
    {
        return $this->changes($model);
    }

    public function state(Model $model): array
    {

        $model = $model->fresh();

        return [
            'current' => $model->currentState,
            'previous' => $model->previousState,
        ];
    }
}
