<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Models;

use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Abstract CRUD model providing standard create, read, update, and delete operations.
 */
abstract class CrudModel extends BaseDomainModel
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'color',
        'description',
        'active',
    ];

    protected function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    public function triggerEvent(string $event): void
    {
        $this->fireModelEvent($event);
    }

    public function delete(): bool
    {
        $this->active = false;
        $this->save();

        parent::delete();

        return true;
    }
}
