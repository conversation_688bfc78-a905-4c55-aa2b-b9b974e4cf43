<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Models;

class PitchType extends CrudModel
{

    protected static function booted()
    {
        static::creating(function ($model) {
            if(!$model->isDirty('id')){
                $model->id = $model->query()->max('id') + 1 ?? 1;
            }
        });
    }

    public function getFillable(): array
    {
        $fillable = parent::getFillable();

        return [
            ...$fillable,
            'icon',
        ];
    }
}
