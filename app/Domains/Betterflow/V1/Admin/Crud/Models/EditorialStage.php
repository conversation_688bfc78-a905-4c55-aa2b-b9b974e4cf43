<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Models;

use App\Domains\Betterflow\V1\Admin\Crud\Models\Concerns\HasStages;
use Database\Factories\Admin\Crud\EditorialStageFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * EditorialStage model representing stages in the editorial workflow.
 */
class EditorialStage extends CrudModel
{
    use HasStages;

    protected $table = 'stages';

    protected static function newFactory(): Factory
    {
        return EditorialStageFactory::new();
    }
}
