<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Models\Concerns;

use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

trait HasStages
{
    protected static function getType(): string
    {
        return str(class_basename(static::class))->before('Stage')->lower();
    }

    public static function bootHasStages(): void
    {
        static::addGlobalScope(static::getType() . '_stage', function (Builder $builder): void {
            $builder->where('type', static::getType());
        });

        static::creating(function (self $stage): void {
            $stage->slug = $stage->slug ?? str($stage->name)->slug()->toString();
            abort_if(static::query()->whereSlug($stage->slug)->exists(), 409, 'Stage already exists');

            $stage->type = $stage->type ?? static::getType();

        });

        static::created(function (self $stage): void {
            Cache::forget(static::getType() . '_stages');
        });

        static::deleted(function (self $stage): void {
            Cache::forget(static::getType() . '_stages');
        });

        static::updated(function (self $stage): void {
            Cache::forget(static::getType() . '_stages');
        });

        static::softDeleted(function (self $stage): void {
            Cache::forget(static::getType() . '_stages');
        });
    }
}
