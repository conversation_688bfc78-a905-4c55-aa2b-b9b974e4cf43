<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Database\Factories;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use Illuminate\Database\Eloquent\Factories\Factory;

class PitchTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    protected $model = PitchType::class;

    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'description' => fake()->sentence(),
            'slug' => fake()->slug(),
            'active' => true,
            'color' => fake()->hexColor(),
        ];
    }
}
