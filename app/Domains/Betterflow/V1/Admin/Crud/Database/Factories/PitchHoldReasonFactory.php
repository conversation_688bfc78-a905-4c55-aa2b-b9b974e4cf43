<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Database\Factories;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchHoldReason;
use Illuminate\Database\Eloquent\Factories\Factory;

class PitchHoldReasonFactory extends Factory
{
    protected $model = PitchHoldReason::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'description' => fake()->sentence(),
            'active' => fake()->boolean(),
            'color' => fake()->hexColor(),
            'slug' => fake()->slug(),
        ];
    }
}
