<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Database\Factories;

use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchStage;
use Illuminate\Database\Eloquent\Factories\Factory;

class PitchStageFactory extends Factory
{
    protected $model = PitchStage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => 'pitch',
            'active' => true, // fake()->boolean(),
        ];
    }
}
