<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CrudResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var int */
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            /** @var bool */
            'active' => $this->active,
            'color' => $this->color,
            'icon' => $this->whenHas('icon'),
        ];
    }
}
