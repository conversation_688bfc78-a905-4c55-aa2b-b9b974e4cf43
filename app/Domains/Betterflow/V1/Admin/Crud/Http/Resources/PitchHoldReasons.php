<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PitchHoldReasons extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var int */
            'id' => $this->id,
            'name' => $this->name,
            /** @var bool */
            'active' => $this->active,
            'slug' => $this->slug,
            'description' => $this->description,
            'color' => $this->color,
        ];

    }
}
