<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Http\Controllers;

use App\Domains\Betterflow\V1\Admin\Crud\Http\Resources\CrudResource;
use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchStage;
use App\Domains\Shared\Helpers\CrudHelper;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Admin / Pitch Stages
 */
class PitchStagesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return CrudResource::collection(CrudHelper::pitch_stages());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $new_stage = PitchStage::create($request->all());
        Cache::forget('pitch_stages');

        return CrudResource::make($new_stage);
    }

    /**
     * Display the specified resource.
     */
    public function show(PitchStage $stage)
    {
        return CrudResource::make($stage);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PitchStage $stage)
    {
        $stage->update($request->all());
        Cache::forget('pitch_stages');

        return CrudResource::make($stage->fresh());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PitchStage $stage): void
    {
        $stage->delete();
        Cache::forget('pitch_stages');
    }
}
