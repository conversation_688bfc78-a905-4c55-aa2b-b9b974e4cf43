<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Http\Controllers;

use App\Domains\Betterflow\V1\Admin\Crud\Http\Requests\CreateCountryRequest;
use App\Domains\Betterflow\V1\Admin\Crud\Http\Requests\UpdateCountryRequest;
use App\Domains\Betterflow\V1\Admin\Crud\Http\Resources\CountryResource;
use App\Domains\Betterflow\V1\Admin\Crud\Models\Country;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CountriesController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        return CountryResource::collection(Country::query()->get());
    }

    public function show(Country $country): CountryResource
    {
        return CountryResource::make($country);
    }

    public function store(CreateCountryRequest $request): CountryResource
    {
        $country = Country::create($request->validated());

        return CountryResource::make($country);
    }

    public function update(UpdateCountryRequest $request, Country $country): CountryResource
    {
        $country->update($request->validated());

        return CountryResource::make($country);
    }

    public function destroy(Country $country)
    {
        $country->delete();

        return response()->noContent();
    }
}
