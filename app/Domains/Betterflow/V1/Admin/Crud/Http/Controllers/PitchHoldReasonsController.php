<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Http\Controllers;

use App\Domains\Betterflow\V1\Admin\Crud\Http\Resources\CrudResource;
use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchHoldReason;
use App\Domains\Shared\Helpers\CrudHelper;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Admin / Pitch Hold Reasons
 */
class PitchHoldReasonsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return CrudResource::collection(CrudHelper::pitch_hold_reasons());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $new_reason = PitchHoldReason::create($request->all());
        Cache::forget('pitch_hold_reasons');

        return CrudResource::make($new_reason);
    }

    /**
     * Display the specified resource.
     */
    public function show(PitchHoldReason $reason)
    {
        return CrudResource::make($reason);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PitchHoldReason $reason)
    {
        $reason->update($request->all());
        Cache::forget('pitch_hold_reasons');

        return CrudResource::make($reason);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PitchHoldReason $reason): void
    {
        $reason->delete();
        Cache::forget('pitch_hold_reasons');
    }
}
