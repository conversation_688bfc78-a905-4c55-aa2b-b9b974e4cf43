<?php

namespace App\Domains\Betterflow\V1\Admin\Crud\Http\Controllers;

use App\Domains\Betterflow\V1\Admin\Crud\Http\Resources\CrudResource;
use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Shared\Helpers\CrudHelper;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Admin / Pitch Types
 */
class PitchTypesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return CrudResource::collection(CrudHelper::pitch_types());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $data = $request->validate([
            'name' => ['required', 'string', 'unique:pitch_types'],
            'slug' => ['required', 'string', 'unique:pitch_types'],
            'description' => ['required', 'string'],
            'color' => ['required', 'string', 'regex:/^#([A-Fa-f0-9]{6})$/'],
            'icon' => ['sometimes', 'string'],

        ]);

        Cache::forget('pitch_types');
        $new_type = PitchType::create($data);

        return CrudResource::make($new_type);
    }

    /**
     * Display the specified resource.
     */
    public function show(PitchType $type)
    {
        return CrudResource::make($type);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PitchType $type)
    {
        $type->update($request->all());
        Cache::forget('pitch_types');

        return CrudResource::make($type->fresh());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PitchType $type): void
    {
        $type->delete();
        Cache::forget('pitch_types');
    }
}
