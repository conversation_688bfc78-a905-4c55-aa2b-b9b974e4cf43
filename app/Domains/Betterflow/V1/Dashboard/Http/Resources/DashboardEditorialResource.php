<?php

namespace App\Domains\Betterflow\V1\Dashboard\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Shared\Helpers\LanguageHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DashboardEditorialResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $country = LanguageHelper::country($this->pitch->country_code);

        return [
            'id' => $this->getResourceKey(),
            'name' => $this->name,
            'pitch_type' => $this->pitch?->type,
            'phase' => $this->phase->value,
            'phase_formatted' => $this->phase->headline(),
            'sub_phase' => $this->when($this->sub_phase, fn() => $this->sub_phase?->value, null),
            'sub_phase_formatted' => $this->when($this->sub_phase, fn() => $this->sub_phase?->headline(), ''),
            'topic' => $this->pitch->topic,
            'short_name' => $this->pitch->short_name,
            'vertical' => $this->pitch->vertical,
            'country' => $country,
            'default_language' => $this->default_language,
            'created_at' => $this->created_at,
            'pitched_at' => $this->pitch->created_at,

            // User roles information
            'reporter' => SimpleUserResource::make($this->whenLoaded('reporter')),
            'assignee' => SimpleUserResource::make($this->whenLoaded('assignee')),
            'topline_editor' => SimpleUserResource::make($this->whenLoaded('toplineEditor')),
            'copy_editor' => SimpleUserResource::make($this->whenLoaded('copyEditor')),
            'fact_checker' => SimpleUserResource::make($this->whenLoaded('factChecker')),
            'translator' => SimpleUserResource::make($this->whenLoaded('translator')),

            // User role flags - showing which roles the current user has in this editorial
            'user_roles' => [
                // Standard editorial roles
                'is_reporter' => $this->reporter_id === $request->user()->getKey(),
                'is_assignee' => $this->assigned_to_id === $request->user()->getKey(),
                'is_topline_editor' => $this->topline_editor_id === $request->user()->getKey(),
                'is_copy_editor' => $this->copy_editor_id === $request->user()->getKey(),
                'is_fact_checker' => $this->fact_checker_id === $request->user()->getKey(),
                'is_translator' => $this->translator_id === $request->user()->getKey(),

                // Task-based roles
                'has_tasks' => isset($this->userTasks) && $this->userTasks->isNotEmpty(),
                'has_asset_tasks' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset';
                }),
                'has_editorial_tasks' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'editorial';
                }),

                // Asset-specific roles based on task types
                'is_reporting_asset_owner' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset' &&
                           str_contains($task->type, 'prepare_reporting_topline_edit');
                }),
                'is_photo_asset_owner' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset' &&
                           str_contains($task->type, 'prepare_photo_topline_edit');
                }),
                'is_graphic_asset_owner' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset' &&
                           str_contains($task->type, 'prepare_graphic_topline_edit');
                }),
                'is_illustration_asset_owner' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset' &&
                           str_contains($task->type, 'prepare_illustration_topline_edit');
                }),
                'is_headline_summary_asset_owner' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset' &&
                           str_contains($task->type, 'prepare_headline_summary_topline_edit');
                }),
                'is_fact_asset_owner' => isset($this->userTasks) && $this->userTasks->contains(function($task) {
                    return $task->taskable_type === 'asset' &&
                           str_contains($task->type, 'prepare_fact_topline_edit');
                }),
            ],

            // Tasks assigned to the current user for this editorial
            'tasks' => TaskResource::collection($this->userTasks ?? collect([])),
            'pending_tasks_count' => $this->userTasks ? $this->userTasks->count() : 0,
        ];
    }
}
