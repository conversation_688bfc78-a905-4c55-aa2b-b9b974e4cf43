<?php

namespace App\Domains\Betterflow\V1\Dashboard\Http\Controllers;

use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use Illuminate\Http\Request;

class CompassTasksDashboardController
{
    public function __invoke(Request $request)
    {
        return TaskResource::collection($request->user()->pendingTasks()->get()->groupBy(fn($task) => str($task->taskable_type)->append('_tasks'))->map);
    }
}
