<?php

namespace App\Domains\Betterflow\V1\Dashboard\Http\Controllers;

use App\Domains\Users\Models\User;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Dashboard\Http\Resources\DashboardEditorialResource;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CompassEditorialsDashboardController
{
    public function __invoke(#[CurrentUser] User $user): AnonymousResourceCollection
    {
        // Get all tasks for the current user so we can filter out task types
        $userTasks = $user->pendingTasks()->with('taskable')->get();
        
        // Filter out all asset tasks
        $assetTasks = $userTasks->where('taskable_type', 'asset');

        // Filter Editorial tasks
        $editorialTasks = $userTasks->where('taskable_type', 'editorial');
        
        // Get editorial IDs from editorial tasks
        $editorialIdsFromTasks = $editorialTasks->pluck('taskable_id')->unique()->values();
        
        // Get editorial IDs from asset tasks
        $editorialIdsFromAssetTasks = $assetTasks->map(function ($task) {
            if ($task->taskable && $task->taskable->editorial_id) {
                return $task->taskable->editorial_id;
            }
        })->unique()->values();
        
        $allTasks = collect($editorialIdsFromAssetTasks, $editorialIdsFromTasks)->unique()->values();
        
        // Get all editorials where the user is involved in any role or has tasks
        // This is important since users involvement in editorials is based on many types of assignments
        // Needs to include direct assignment like assignes as fact checker but also assigned tasks and assists
        $editorials = Editorial::with([
            'pitch' => fn($query) => $query->with(['type', 'topic', 'vertical'])
        ])->forUserAndTasks($user, $allTasks);
        
        // Group editorial tasks by editorial ID
        $editorialTasksGrouped = $editorialTasks->groupBy('taskable_id');
        // Attach tasks to their respective editorials
        $editorials->afterQuery(function (Collection $editorials) use ($editorialTasksGrouped, $assetTasks) {
            // Initialize with editorial tasks
            
            // Add direct editorial tasks if they exist
            return $editorials->map(function(Editorial $editorial) use ($editorialTasksGrouped, $assetTasks) {
                $tasks = collect();
                if ($editorialTasksGrouped->has($editorial->id)) {
                    $tasks = $tasks->merge($editorialTasksGrouped->get($editorial->id));
                }
                
                // Add asset tasks that belong to this editorial
                foreach ($assetTasks as $task) {
                    // Check if the asset task is related to this editorial
                    // First check if there's an editorial_id in the task data
                    if (isset($task->data['editorial_id']) && $task->data['editorial_id'] == $editorial->id) {
                        $tasks->push($task);
                    }
                    // If there's an asset_id in the task data, try to find the editorial through the asset
                    elseif (isset($task->data['asset_id']) && $task->taskable && $task->taskable->editorial_id == $editorial->id) {
                        $tasks->push($task);
                    }
                    // If the task has a taskable that is an asset and that asset belongs to this editorial
                    elseif ($task->taskable && $task->taskable->editorial_id == $editorial->id) {
                        $tasks->push($task);
                    }
                }
                
                $editorial->userTasks = $tasks;
                return $editorial;
            });
        });

        return DashboardEditorialResource::collection($editorials->get());
    }
}
