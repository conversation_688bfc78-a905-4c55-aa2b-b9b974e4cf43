<?php

namespace App\Domains\Betterflow\V1\Dashboard\Http\Controllers;

use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Collection as SupportCollection;

class CompassDashboardController
{
    public function __invoke(Request $request)
    {
        $pitches = $this->pitches($request->user());
        $editorials = $this->editorials($request->user());
        $events = $this->events($request->user());
        return response()->json([
            'editorials' => $editorials,
            'pitches' => $pitches,
            'events' => $events
        ]);
    }

    protected function editorials(User $user): Collection|SupportCollection|null
    {
        return $user->editorials;
    }

    protected function pitches(User $user): Collection|SupportCollection|null
    {
        $pitches = collect();

        $with = ['collaborators', 'attachments', 'activities', 'comments', 'pitchEvent'];

        $userPitchesBuilder = $user->ownPitches()->with($with);

        $userPitches = $userPitchesBuilder->clone()->notDrafts()->get();

        $userDraftPitches = $userPitchesBuilder->clone()->drafts()->get();

        $userCollaboratingPitches = $user->collaboratingPitches()->with($with)->get();

        // Followed pitches.
        $watchedPitches = $user->watchedPitches()->with($with)->get();

        $pitches->put('pitches', $userPitches);
        $pitches->put('drafts', $userDraftPitches);
        $pitches->put('collaborating', $userCollaboratingPitches);
        $pitches->put('watched', $watchedPitches);

        return $pitches;
    }

    protected function events(User $user): Collection|SupportCollection|null
    {
        return $user->pitchEvents();
    }
}
