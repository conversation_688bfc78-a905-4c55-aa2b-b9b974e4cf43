<?php

namespace App\Domains\Betterflow\V1\Dashboard\Http\Controllers;

use Illuminate\Http\Request;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\PitchResource;

class CompassPitchesDashboardController
{
    public function __invoke(Request $request)
    {
        $pitches = collect();

        $user = $request->user();

        $with = ['collaborators', 'attachments', 'tasks', 'activities', 'comments', 'pitchEvent'];

        $userPitchesBuilder = $user->ownPitches()->with($with);

        $userPitches = $userPitchesBuilder->clone()->notDrafts()->get();

        $userDraftPitches = $userPitchesBuilder->clone()->drafts()->get();

        $userCollaboratingPitches = $user->collaboratingPitches()->with($with)->get();

        // Followed pitches.
        $watchedPitches = $user->watchedPitches()->with($with)->get();

        $pitches->put('pitches', $userPitches);
        $pitches->put('drafts', $userDraftPitches);
        $pitches->put('collaborating', $userCollaboratingPitches);
        $pitches->put('watched', $watchedPitches);

        return PitchResource::collection($pitches->map);
    }
}
