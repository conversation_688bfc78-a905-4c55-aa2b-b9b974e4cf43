<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Enums\Assets\RecordableEvents;
use App\Domains\Betterflow\V1\Editorials\Events\AssistCreated;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Notifications\AssistRequestedNotification;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Queue\InteractsWithQueue;
use Throwable;

class HandleAssistCreated implements ShouldQueueAfterCommit
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public function handle(AssistCreated $event): void
    {
        /** @var Task */
        $task = $event->task;

        $task->user->notify(new AssistRequestedNotification(
            task: $task,
            editorial: $task->taskable instanceof Editorial ? $task->taskable : $task->taskable->editorial,
            asset: $task->taskable instanceof Asset ? $task->taskable : null,
        ));

        $activity = new ActivityObject(
            logName: str($task->taskable_type)->plural()->toString(),
            by: $task->requestedBy?->getKey(),
            on: $task->taskable,
            event: RecordableEvents::AssistRequested,
            description: 'Requested an assist',
        );

        RecordActivity::dispatch($activity);
    }

    public function failed(AssistCreated $event, Throwable $exception): void
    {
        logger('HandleAssistCreated listener failed', [
            'exception' => $exception->getMessage(),
            // 'task_id' => $this->event->assist->id ?? null,
            // 'task_type' => get_class($this->event->assist) ?? null
        ]);
    }
}
