<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Editorials\Jobs\CheckForCompletedAssets;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Notifications\AssetAssistCompletedNotification;
use App\Domains\Betterflow\V1\Editorials\Notifications\EditorialAssistCompletedNotification;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted as SharedEventsTaskCompleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class RecordActivityAndNotifyRequester implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    public function handle(TaskCompleted|SharedEventsTaskCompleted $event): void
    {
        $task = $event->task;

        if ($task->taskable instanceof Asset && $task->isOwnerTask()) {
            $asset = $task->taskable;

            $asset->markComplete();

            CheckForCompletedAssets::dispatch($asset, $task);
        }

        if ($task->hasRequester() && $task->taskable instanceof Asset) {
            $task->requestedBy->notify(new AssetAssistCompletedNotification(
                asset: $task->taskable,
                title: $task->title,
                message: 'An assist you requested has been marked complete',
                assist: $task,
            ));
        } else if ($task->hasRequester() && $task->taskable instanceof Editorial) {
            $task->requestedBy->notify(new EditorialAssistCompletedNotification(
                editorial: $task->taskable,
                title: $task->title,
                message: 'An assist you requested has been marked complete',
                assist: $task,
            ));
        }
    }
}
