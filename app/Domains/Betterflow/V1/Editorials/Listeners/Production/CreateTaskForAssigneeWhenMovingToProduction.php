<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production;

use App\Domains\Betterflow\V1\Editorials\DTO\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase as EnumsEditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedToProduction;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateTaskForAssigneeWhenMovingToProduction implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    public function handle(EditorialMovedToProduction $event): void
    {
        /** @var Editorial $editorial */
        $editorial = Editorial::findOrFail($event->editorialId);

        /** @var EditorialPhase $previous */
        $previous = $event->previousPhase;

        // Check if the editorial has moved to the correct phase & sub phase
        if (
            $previous->phase === EnumsEditorialPhase::Editing &&
            $editorial->isInProduction() &&
            $editorial->sub_phase === EditorialSubPhase::FactCheckAssignment
        ) {
            $task = new Task(
                userId: $editorial->assigned_to_id,
                title: "Assign Fact Checker and Copy Editor",
                description: "Assign fact checker and copy editor to the editorial",
                type: EditorialTaskType::AssignFactCheckerAndCopyEditor,
                dueAt: now()->addDays(2),
                data: new TaskData(
                    model: $editorial,
                    label: "Assign Editors",
                    properties: []
                )
            );

            $editorial->createTask($task);
        }
    }
}
