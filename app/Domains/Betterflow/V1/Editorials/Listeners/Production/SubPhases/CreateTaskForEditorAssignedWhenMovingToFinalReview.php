<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production\SubPhases;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase as ValueObjectsEditorialPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateTaskForEditorAssignedWhenMovingToFinalReview implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    public function handle(EditorialMovedSubPhase $event): void
    {
        /** @var Editorial $editorial */
        $editorial = Editorial::findOrFail($event->editorialId);

        /** @var ValueObjectsEditorialPhase $previous */
        $previous = $event->previousPhase;
        $assignee = $editorial->assignee;

        if ($assignee) {
            if (
                $editorial->phase === EditorialPhase::Production &&
                $editorial->sub_phase === EditorialSubPhase::FinalReview &&
                $previous->phase === EditorialPhase::Production &&
                ($previous->subPhase === EditorialSubPhase::Translation || $previous->subPhase === EditorialSubPhase::CopyEdit)
            ) {
                //  Create task for copy editor
                $task = new Task(
                    userId: $assignee->getKey(),
                    title: "Final Review",
                    description: "final review",
                    type: EditorialTaskType::FinalReview,
                    dueAt: now()->addDays(2),
                    data: new TaskData(
                        model: $editorial,
                        label: "Final Review",
                        properties: []
                    )
                );

                $editorial->createTask($task);
            }
        }
    }
}
