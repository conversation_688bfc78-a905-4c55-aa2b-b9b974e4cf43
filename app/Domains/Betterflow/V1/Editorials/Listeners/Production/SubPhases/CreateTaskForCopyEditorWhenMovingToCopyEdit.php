<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production\SubPhases;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase as ValueObjectsEditorialPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateTaskForCopyEditorWhenMovingToCopyEdit implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    public function handle(EditorialMovedSubPhase $event): void
    {
        /** @var Editorial $editorial */
        $editorial = Editorial::findOrFail($event->editorialId);

        /** @var ValueObjectsEditorialPhase $previous */
        $previous = $event->previousPhase;
        $copyEditor = $editorial->copyEditor;

        if ($copyEditor) {
            if (
                $editorial->phase === EditorialPhase::Production &&
                $editorial->sub_phase === EditorialSubPhase::CopyEdit &&
                $previous->phase === EditorialPhase::Production &&
                $previous->subPhase === EditorialSubPhase::FactCheck

            ) {
                //  Create task for copy editor
                $task = new Task(
                    userId: $copyEditor->getKey(),
                    title: "Copy Edit",
                    description: "Copy Edit",
                    type: EditorialTaskType::CopyEdit,
                    dueAt: now()->addDays(2),
                    data: new TaskData(
                        model: $editorial,
                        label: "Copy Edit",
                        properties: []
                    )
                );

                $editorial->createTask($task);
            }
        }
    }
}
