<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production\SubPhases;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class MovePhaseToFinalReview implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(TaskCompleted $event): void
    {
        /** @var Task $task */
        $task = $event->task;

        /** @var Editorial $taskable */
        $taskable = $task->taskable;
        $isRequested = $task->hasRequester();
        if (
            $taskable instanceof Editorial
            && $taskable->isInProduction()
            && !$isRequested
        ) {
            $needsTranslation = $taskable->needsTranslation();
            $previous = EditorialPhase::tryFromEditorial($taskable);
            
            if ($taskable->sub_phase === EditorialSubPhase::CopyEdit) {

                if ($needsTranslation) {
                    $taskable->moveSubPhase(EditorialSubPhase::Translation);
                } else {
                    $taskable->moveSubPhase(EditorialSubPhase::FinalReview);
                }
            }

            $taskable->recordEvent(new EditorialMovedSubPhase($taskable->getKey(), $previous));

            $taskable->persist();
        }
    }
}
