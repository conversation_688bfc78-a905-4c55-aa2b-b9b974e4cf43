<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production\SubPhases;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;

class CreateTaskForAssigneeToAssignTranslatorWhenMovingToTranslation implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    /**
     * Handle the event.
     */
    public function handle(EditorialMovedSubPhase $event): void
    {
        $editorial = Editorial::findOrFail($event->editorialId);
        $assignee = $editorial->assignee;
        $previousPhase = $event->previousPhase;

        if ($assignee) {
            if (
                $previousPhase->phase === EditorialPhase::Production &&
                $previousPhase->subPhase !== EditorialSubPhase::TranslationAssignment &&
                $editorial->isInProduction() &&
                $editorial->sub_phase === EditorialSubPhase::TranslationAssignment

            ) {
                // Send task + notification to assignee
                $task = new Task(
                    userId: $assignee->getKey(),
                    title: "Assign Translator",
                    description: "Assign translator to the editorial",
                    type: EditorialTaskType::AssignTranslator,
                    dueAt: now()->addDays(2),
                    data: new TaskData(
                        model: $editorial,
                        label: "Assign Translator",
                        properties: []
                    )
                );

                $editorial->createTask($task);
            }
        }
    }
}
