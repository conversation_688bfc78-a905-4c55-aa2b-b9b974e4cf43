<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\Production\SubPhases;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase as EnumsEditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedToProduction;
use App\Domains\Betterflow\V1\Editorials\Events\TaskCompleted as EditorialsEventsTaskCompleted;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted as SharedEventsTaskCompleted;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class MaybeMoveSubPhaseAutomatically implements ShouldQueue
{
    use InteractsWithQueue;

    public $queue = 'compass';

    public function handle(EditorialsEventsTaskCompleted|SharedEventsTaskCompleted $event): void
    {
        /** @var Task $task */
        $task = $event->task;

        /** @var Editorial $taskable */
        $taskable = $task->taskable;
        $isRequested = $task->hasRequester();

        if ($taskable instanceof Editorial && $taskable->isInEditing() && !$isRequested) {

            /** @var EditorialPhase $previous */
            $previous = EditorialPhase::tryFromEditorial($taskable);

            // Editing assignment completion
            if ($taskable->sub_phase === EditorialSubPhase::EditingAssignment && $task->type === EditorialTaskType::AssignEditors->value) {
                $taskable->moveSubPhase(EditorialSubPhase::Editing);
            }
            if (
                $taskable->sub_phase === EditorialSubPhase::ToplineEdit
                && $task->type === EditorialTaskType::ToplineEdit->value
            ) {
                if (!$taskable->allAssetsComplete()) {
                    $taskable->moveSubPhase(EditorialSubPhase::Editing);
                } else {
                    $taskable->movePhase(EnumsEditorialPhase::Production);
                    $taskable->moveSubPhase(EditorialSubPhase::FactCheckAssignment);
                    $taskable->recordEvent(new EditorialMovedToProduction($taskable->getKey(), $previous));
                }
            }

            $taskable->recordEvent(new EditorialMovedSubPhase($taskable->getKey(), $previous));

            $taskable->persist();

            return;
        }

        if (
            $taskable instanceof Editorial
            && $taskable->isInProduction()
            && !$isRequested
        ) {
            $needsTranslation = $taskable->needsTranslation();
            $previous = EditorialPhase::tryFromEditorial($taskable);

            // Fact check assignment completion
            if ($taskable->sub_phase === EditorialSubPhase::FactCheckAssignment && $task->type === EditorialTaskType::AssignFactCheckerAndCopyEditor->value) {
                $taskable->moveSubPhase(EditorialSubPhase::FactCheck);
            }

            if ($taskable->sub_phase === EditorialSubPhase::FactCheck && $task->type === EditorialTaskType::FactCheck->value) {
                $taskable->moveSubPhase(EditorialSubPhase::CopyEdit);
            }
            
             // Fact check complete
            if ($taskable->sub_phase === EditorialSubPhase::CopyEdit && $task->type === EditorialTaskType::CopyEdit->value) {
                if ($taskable->needsTranslation()) {
                    $taskable->moveSubPhase(EditorialSubPhase::TranslationAssignment);
                } else {
                    $taskable->moveSubPhase(EditorialSubPhase::FinalReview);
                }
            }

            // Translation assignment completion
            if ($taskable->sub_phase === EditorialSubPhase::TranslationAssignment && $task->type === EditorialTaskType::AssignTranslator->value) {
                $taskable->moveSubPhase(EditorialSubPhase::Translation);
            }

            // Handle Translation phase completion
            if ($taskable->sub_phase === EditorialSubPhase::Translation && $task->type === EditorialTaskType::Translation->value) {
                $taskable->moveSubPhase(EditorialSubPhase::FinalReview);
            }

            // Handle FinalReview phase completion
            if ($taskable->sub_phase === EditorialSubPhase::FinalReview && $task->type === EditorialTaskType::FinalReview->value) {
                $taskable->movePhase(EnumsEditorialPhase::Publishing);
                $taskable->moveSubPhase(EditorialSubPhase::PublishingAssignment);
            }

            $taskable->recordEvent(new EditorialMovedSubPhase($taskable->getKey(), $previous));

            $taskable->persist();

            return;
        }

        // Publishing assignment completion
        if ($taskable instanceof Editorial && $taskable->isInPublishing() && !$isRequested) {
            return;
        }

        // Published
        if ($taskable instanceof Editorial && $taskable->isInPublished() && !$isRequested) {
            return;
        }
    }
}
