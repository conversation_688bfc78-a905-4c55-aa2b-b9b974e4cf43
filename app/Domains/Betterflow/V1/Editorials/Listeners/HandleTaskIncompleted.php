<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Events\TaskIncompleted;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Throwable;

class HandleTaskIncompleted implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public function handle(TaskIncompleted $event): void
    {
        $task = $event->task;
        $instructions = $event->instructions;

        if($task->taskable instanceof Asset) {
            $task->taskable->markPending();
        }

        // Notify the user their task was marked as incomplete
        $task->user->notify(new DefaultNotification(
            forWhat: $task->taskable,
            title: 'Asset marked for review',
            message: $instructions
                ? 'Your asset has been marked as incomplete with the following instructions:
' . $instructions
                : 'Your asset has been marked as incomplete and needs attention',
            type: $task->type,
        ));
    }

    public function failed(Throwable $exception): void
    {
        \Log::error('HandleTaskIncompleted listener failed', [
            'exception' => $exception->getMessage(),
            // 'task_id' => $this->event->task->id ?? null,
            // 'task_type' => get_class($this->event->task) ?? null
        ]);
    }
}
