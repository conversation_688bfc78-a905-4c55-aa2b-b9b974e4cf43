<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Events\EditorialCreated;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Contracts\Queue\ShouldQueue;

class MakeDefaultLanguagePackage implements ShouldQueue
{
    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    public function handle(EditorialCreated $event) {}
}
