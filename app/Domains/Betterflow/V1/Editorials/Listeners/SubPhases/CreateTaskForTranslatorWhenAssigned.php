<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners\SubPhases;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData as DataTaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;

class CreateTaskForTranslatorWhenAssigned implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    /**
     * Handle the event.
     */
    public function handle(EditorialMovedSubPhase $event): void
    {
        /** @var Editorial $editorial */
        $editorial = Editorial::findOrFail($event->editorialId);

        if ($editorial->isInProduction() && $editorial->sub_phase === EditorialSubPhase::Translation) {
            $task = new Task(
                userId: $editorial->translator->getKey(),
                title: "Handle Translation",
                description: "Handle translation for the editorial",
                type: EditorialTaskType::Translation,
                dueAt: now()->addDays(2),
                data: new DataTaskData(
                    model: $editorial,
                    label: "Handle Translation",
                    properties: []
                )
            );

            $editorial->createTask($task);
        }
    }
}
