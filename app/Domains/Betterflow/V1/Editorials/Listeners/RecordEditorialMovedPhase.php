<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\DTO\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\Assets\RecordableEvents;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedToProduction;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class RecordEditorialMovedPhase implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public $queue = 'compass';

    public function handle(EditorialMovedToProduction $event): void
    {
        /** @var Editorial $editorial */
        $editorial = Editorial::findOrFail($event->editorialId);

        /** @var EditorialPhase $previousPhase */
        $previousPhase = $event->previousPhase;

        $activity = new ActivityObject(
            logName: 'editorials',
            on: $editorial,
            event: RecordableEvents::PhaseChanged,
            description: "Moved from phase {$previousPhase->phase->value} to {$editorial->phase->value}",
            properties: [
                'phase_changed' => [
                    'old' => $previousPhase->phase,
                    'new' => $editorial->phase,
                ],
                'sub_phase_changed' => [
                    'old' => $previousPhase->subPhase,
                    'new' => $editorial->sub_phase,
                ],
            ],
        );

        RecordActivity::dispatch($activity);
    }
}
