<?php

namespace App\Domains\Betterflow\V1\Editorials\Listeners;

use App\Domains\Betterflow\V1\Editorials\Events\EditorialReassigned;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandleReassignTasksForEditorial implements ShouldQueue
{   
    use InteractsWithQueue;


    public function handle(EditorialReassigned $event): void
    {
        $editorial = Editorial::findOrFail($event->editorialId);
        $newAssignee = User::findOrFail($event->userId);
        $previousAssignee = User::findOrFail($event->previousAssignee);
        $editorial->reassignTasks($newAssignee, $previousAssignee);
    }
}
