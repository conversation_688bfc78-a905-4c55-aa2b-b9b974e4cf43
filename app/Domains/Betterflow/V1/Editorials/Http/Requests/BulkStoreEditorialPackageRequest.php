<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Requests;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Helpers\LanguageHelper;
use Illuminate\Auth\Access\Response;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class BulkStoreEditorialPackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(#[RouteParameter('editorial')] Editorial $editorial): Response
    {
        return Gate::authorize('create_packages', $editorial);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(#[RouteParameter('editorial')] Editorial $editorial)
    {
        return [
            'packages' => ['required', 'array'],
            'packages.*' => ['required', 'array'],
            'packages.*.language_code' => ['required', 'string', 
                Rule::in((LanguageHelper::languageCodes()->pluck('code'))),
                Rule::unique('editorial_packages', 'language_code')->where('editorial_id', $editorial->getKey())
            ],
            'packages.*.data' => ['required', 'array'],
        ];
    }
}