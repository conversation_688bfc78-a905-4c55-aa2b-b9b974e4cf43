<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCommentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'comment' => ['required'],
            'comment_id' => ['sometimes', 'numeric', 'exists:comments,id'],
        ];
    }
}
