<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Requests;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Helpers\LanguageHelper;
use Illuminate\Auth\Access\Response;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class StoreEditorialPackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(#[CurrentUser] $user, #[RouteParameter('editorial')] Editorial $editorial): Response
    {
        return Gate::authorize('create_packages', $editorial);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(#[RouteParameter('editorial')] Editorial $editorial): array
    {
        return [
            'language_code' => [
                'required',
                'string',
                Rule::in(LanguageHelper::languageCodes()->pluck('code')),
                Rule::unique('editorial_packages')
                    ->where('editorial_id', $editorial->getKey())
                    ->whereNull('deleted_at')
            ],
            'data' => ['required', 'array'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'language_code.unique' => 'A package with this language already exists for this editorial.',
            'language_code.in' => 'The selected language code is invalid.',
        ];
    }

    /**
     * Get the validated data from the request.
     */
    public function payload(): array
    {
        return $this->validated();
    }
}
