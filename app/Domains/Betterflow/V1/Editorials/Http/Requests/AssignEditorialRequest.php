<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Requests;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use App\Rules\LanguageCodeRule;
use Illuminate\Auth\Access\Response;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class AssignEditorialRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(#[CurrentUser] User $user, #[RouteParameter('editorial')] Editorial $editorial): Response
    {
        return Gate::authorize('assign', $editorial);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'languages' => ['sometimes', 'array'],
            'languages.*' => ['string', new LanguageCodeRule()],
            'users' => ['required', 'array'],
            'users.*.id' => ['required', 'exists:users,id'],
            'users.*.role' => ['sometimes', 'string', Rule::in(Role::query()->get()->pluck('name')->push('topline_editor'))],
            'users.*.asset' => ['nullable', 'string', Rule::in(AssetType::cases())],
        ];
    }

    public function messages(): array
    {
        return [
            'languages.*.string' => 'The language code must be a string.',
            'users.required' => 'The users field is required.',
            'users.array' => 'The users field must be an array.',
            'users.*.id.required' => 'The user id field is required.',
            'users.*.id.exists' => 'The selected user is not a valid user.',
        ];
    }
}
