<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection as SupportCollection;

class AssetResource extends JsonResource
{
    public function toArray($request): array
    {
        $latestContent = $this->content()->where('language_code', $request->language ?? 'en-EN')->first();

        $data = null;

        if ($latestContent && $latestContent->segments()->count() > 0) {
            $data = $latestContent->segments;
        } else {
            $latestContent = $latestContent?->currentVersion();
            $data = unserialize(data_get($latestContent, 'model_data', null));
        }

        $responsibleUser = null;

        if ($latestContent && $latestContent->hasAttribute('user_id')) {
            $responsibleUser = SimpleUserResource::make(User::find($latestContent->getAttribute('user_id')));
        }

        $allTasks = $this->resource->loadMissing('tasks.taskable.assignee', 'tasks.user', 'assignee')->tasks;

        $assetTasks = collect();
        $ownerTask = null;

        if ($allTasks?->isNotEmpty()) {
            $assetTasks = $allTasks->filter(function ($task) {
                return ! $task->isOwnerTask();
            });

            $ownerTask = $allTasks->filter(function ($task) {
                return $task->isOwnerTask();
            })->first();
        }

        if ($data instanceof Collection) {
            $data = $data->mapWithKeys(function ($segment) {
                return [
                    $segment->segment => $segment->data,
                ];
            });
        }

        $content = $data instanceof SupportCollection
            ? $data->prepend($latestContent->getResourceKey(), 'id')
            ->prepend($latestContent->language_code, 'language')
            ->toArray()
            : $this->when($data, [
                'id' => data_get($data, 'id'),
                'language' => data_get($data, 'language_code'),
                ...Arr::wrap(data_get($data, 'data')),
            ], null);

        return [
            'id' => $this->getResourceKey(),
            'editorial_id' => $this->whenLoaded('editorial', fn() => $this->editorial->getResourceKey()),
            'name' => $this->name,
            'slug' => $this->slug,
            'url' => $this->url,
            'required' => $this->required,
            'type' => $this->resource->type->value,
            'description' => $this->description,
            'completed' => $this->isComplete(),
            'schema_version' => $this->schema_version,
            'assignee' => SimpleUserResource::make($this->whenLoaded('assignee')),
            'updated_by' => $this->when($responsibleUser, fn() => $responsibleUser),
            'content' => $content,
            $this->mergeWhen($allTasks, [
                'tasks' => $this->when($assetTasks->isNotEmpty(), TaskResource::collection($assetTasks->groupBy('status')->map)->additional(['asset_id' => $this->getResourceKey()])),
                'owner_task' => $this->whenNotNull(TaskResource::make($ownerTask)->additional(['asset_id' => $this->getResourceKey()])),
            ]),
            'comments' => CommentsResource::collection($this->whenLoaded('comments')),
        ];
    }
}
