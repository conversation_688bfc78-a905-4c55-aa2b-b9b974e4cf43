<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AttachmentsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Media $media */
        $media = $this->resource;

        $conversions = $this->getGeneratedConversions()->map(function ($cond, $conversion) use ($media) {
            if ($cond) {
                return [
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    ...$media->getResponsiveImageUrls($conversion),
                    'url' => $media->getAvailableFullUrl([$conversion]),
                ];
            }
        });

        return [
            'id' => $media->getKey(),
            'name' => $media->name,
            'model_id' => $media->model_id,
            'model_type' => $media->model_type,
            'mime_type' => $media->mime_type,
            'size' => $media->size,
            'url' => $media->getFullUrl(),
            'conversions' => $conversions,
            'uploaded_by' => SimpleUserResource::make(User::find($media->getCustomProperty('uploaded_by'))),
        ];
    }
}
