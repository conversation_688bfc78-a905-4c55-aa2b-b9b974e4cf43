<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\AttachmentsResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Domains\Shared\Helpers\LanguageHelper;

class EditorialPackageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $language = null;

        $this->resource->loadMissing('attachments');

        try {
            $language = LanguageHelper::language($this->language_code);
        } catch (\Exception $e) {
            // Language not found, continue without it
        }

        return [
            'id' => $this->getResourceKey(),
            'editorial_id' => $this->editorial->getResourceKey(),
            'language' => $language,
            'data' => $this->data,
            'attachments' => $this->hasAttachments() ? AttachmentsResource::collection($this->resource->getMedia('package-attachments')) : null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
