<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Shared\Helpers\LanguageHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EditorialResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $isEditorial = $request->route()->parameter('editorial', false);
        $country = LanguageHelper::country($this->pitch->country_code);

        $tasks =  TaskResource::collection($this->whenLoaded('tasks')->groupBy('status')->map) ?? [];
        $assists = TaskResource::collection($this->whenLoaded('assists')->groupBy('status')->map)  ?? [];

        $additional = $this->additional;

        if ($this->isInPublishing()) {
            $packages = $this->loadMissing('packages')->packages;

            $additional['packages'] = $packages;
        }

        if ($isEditorial) {
            $additional = [
                ...$additional,
                'comments' => CommentsResource::collection($this->whenLoaded('comments')),
                'pitch_id' => $this->pitch->getPublicKey(),
                'comments_count' =>  $this->whenLoaded('commentsCount', fn() => $this->comments_count),
                'watcher_ids' => $this->whenLoaded('watchers', fn() => $this->watchers?->pluck('user_id')),
                'assigned_editor' => SimpleUserResource::make($this->whenLoaded('assignee')),
                'topline_editor' => SimpleUserResource::make($this->whenLoaded('toplineEditor')),
                'copy_editor' => SimpleUserResource::make($this->whenLoaded('copyEditor')),
                'fact_checker' => SimpleUserResource::make($this->whenLoaded('factChecker')),
                'translator' => SimpleUserResource::make($this->whenLoaded('translator')),
                'assets' => AssetResource::collection($this->whenLoaded('assets')),
                'tasks' => $tasks->additional(['editorial_id' => $this->getResourceKey()]),
                'assists' =>  $assists->additional(['editorial_id' => $this->getResourceKey()]),
            ];
        }

        $data = [
            'id' => $this->getResourceKey(),
            'name' => $this->name,
            'pitch_type' => $this->pitch?->type,
            'current_owner' => SimpleUserResource::make($this->currentOwner()),
            'assignee' => SimpleUserResource::make($this->whenLoaded('assignee')),
            'phase' => $this->phase->value,
            'phase_formatted' => $this->phase->headline(),
            'sub_phase' => $this->when($this->sub_phase, fn() => $this->sub_phase?->value, null),
            'sub_phase_formatted' => $this->when($this->sub_phase, fn() => $this->sub_phase?->headline(), ''),
            'topic' => $this->pitch->topic,
            'short_name' => $this->pitch->short_name,
            'vertical' => $this->pitch->vertical,
            'current_owner' => SimpleUserResource::make($this->resource->currentOwner()),
            'current_task' => $this->when($this->currentTask(), fn() => TaskResource::make($this->currentTask())),
            'needs_translation' => $this->resource->needsTranslation(),
            'reporter' => SimpleUserResource::make($this->whenLoaded('reporter')),
            'country' => $country,
            'default_language' => $this->default_language,
            'active_tasks_count' => ($this->pending_tasks_count ?? 0) + $this->assists->count(),
            'created_at' => $this->created_at,
            'pitched_at' => $this->pitch->created_at,
            ...$additional,
        ];

        return $data;
    }
}
