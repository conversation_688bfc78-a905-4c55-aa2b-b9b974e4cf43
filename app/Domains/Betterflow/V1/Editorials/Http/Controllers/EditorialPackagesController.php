<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Http\Requests\BulkStoreEditorialPackageRequest;
use App\Domains\Betterflow\V1\Editorials\Http\Requests\PackageAttachmentsRequest;
use App\Domains\Betterflow\V1\Editorials\Http\Requests\StoreEditorialPackageRequest;
use App\Domains\Betterflow\V1\Editorials\Http\Requests\UpdateEditorialPackageRequest;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialPackageResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Exceptions\DuplicateLanguageCodeException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class EditorialPackagesController extends Controller
{
    /**
     * Store multiple attachments for a package
     */
    public function storeAttachments(PackageAttachmentsRequest $request, Editorial $editorial, EditorialPackage $package): JsonResponse
    {
        $package->addAllAttachmentsFromRequest($request, 'package-attachments');
        return response()->json(status: Response::HTTP_CREATED);
    }

    /**
     * Bulk store editorial packages by language.
     *
     * @param Request $request
     * @param Editorial $editorial
     * @return AnonymousResourceCollection
     */
    public function bulkStore(BulkStoreEditorialPackageRequest $request, Editorial $editorial): AnonymousResourceCollection
    {
        $validated = $request->validated();

        // Check for duplicates in request
        $requestLanguages = collect(data_get($validated, 'packages', []))
            ->pluck('language_code')
            ->toArray();
            
        if (count($requestLanguages) !== count(array_unique($requestLanguages))) {
            report(new DuplicateLanguageCodeException('Duplicate language codes in request'));
            abort(Response::HTTP_CONFLICT, 'Duplicate language codes in request');
        }

        // Check against existing packages
        
        $packages = DB::transaction(function () use ($validated, $editorial) {
            return collect(data_get($validated, 'packages', []))->map(function ($packageData) use ($editorial) {
                return $editorial->packages()->create(array_merge($packageData, [
                    'language_code' => data_get($packageData, 'language_code'),
                    'data' => data_get($packageData, 'data', []),
                ]));
            });
        });

        return EditorialPackageResource::collection($packages);
    }
    /**
     * Display a listing of the editorial packages.
     *
     * @param Request $request
     * @param Editorial $editorial
     * @return AnonymousResourceCollection
     */
    public function index(Request $request, Editorial $editorial): AnonymousResourceCollection
    {
        $query = $editorial->packages()->afterQuery(function (Collection $packages) use ($editorial) {
            return $packages->each->setRelation('editorial', $editorial);
        });

        // Filter by language if provided
        if ($request->has('language')) {
            $query->where('language_code', $request->language);
        }

        $packages = $query->get();

        return EditorialPackageResource::collection($packages);
    }

    /**
     * Display the specified editorial package.
     *
     * @param Editorial $editorial
     * @param EditorialPackage $package
     * @return EditorialPackageResource
     */
    public function show(Editorial $editorial, EditorialPackage $package): EditorialPackageResource
    {
        // Ensure the package belongs to the editorial
        abort_unless($package->editorial->is($editorial), Response::HTTP_NOT_FOUND);

        return EditorialPackageResource::make($package);
    }

    /**
     * Store a newly created editorial package.
     *
     * @param StoreEditorialPackageRequest $request
     * @param Editorial $editorial
     * @return EditorialPackageResource
     */
    public function store(StoreEditorialPackageRequest $request, Editorial $editorial): EditorialPackageResource
    {
        $data = $request->payload();

        $package = $editorial->packages()->create([
            'language_code' => $data['language_code'],
            'data' => $data['data'],
        ]);

        return EditorialPackageResource::make($package);
    }

    /**
     * Update the specified editorial package.
     *
     * @param UpdateEditorialPackageRequest $request
     * @param Editorial $editorial
     * @param EditorialPackage $package
     * @return EditorialPackageResource
     */
    public function update(UpdateEditorialPackageRequest $request, Editorial $editorial, EditorialPackage $package): EditorialPackageResource
    {
        // Ensure the package belongs to the editorial
        abort_unless($package->editorial->is($editorial), Response::HTTP_NOT_FOUND);

        $validated = $request->payload();

        $data = data_get($validated, 'data', []);

        $package->update([
            'data' => $data
        ]);

        return EditorialPackageResource::make($package->fresh());
    }

    /**
     * Remove the specified editorial package.
     *
     * @param Editorial $editorial
     * @param EditorialPackage $package
     * @return \Illuminate\Http\Response
     */
    public function destroy(Editorial $editorial, EditorialPackage $package): Response
    {
        // Ensure the package belongs to the editorial
        abort_unless($package->editorial->is($editorial), Response::HTTP_NOT_FOUND);

        $package->delete();

        return response()->noContent();
    }
}
