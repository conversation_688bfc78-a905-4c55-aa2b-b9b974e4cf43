<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Actions\CreateAsset;
use App\Domains\Betterflow\V1\Editorials\Events\UserAssigned;
use App\Domains\Betterflow\V1\Editorials\Events\AssetCreated;
use App\Domains\Betterflow\V1\Editorials\Http\Requests\AssignEditorialRequest;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Users\Models\User;
use App\Exceptions\AssetExistsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class EditorialAssignemntsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(AssignEditorialRequest $request, Editorial $editorial, CreateAsset $createAsset): EditorialResource|JsonResponse
    {
        $data = $request->validated();
        $newAssets = collect();

        DB::beginTransaction();
        try {
            collect(data_get($data, 'users', []))->each(function ($user) use ($editorial, $createAsset, $request, $newAssets): void {
                $userId = data_get($user, 'id');
                $role = data_get($user, 'role');
                $assetType = data_get($user, 'asset');

                if ($assetType && $editorial->hasAssetsOfType($assetType)) {
                    throw new AssetExistsException(sprintf('Editorial already has 1 %s asset.', $assetType));
                }

                if ($assetType) {
                    $asset = $createAsset->handle($assetType, $userId, [
                        'language' => data_get($request, 'language', $editorial->default_language),
                    ]);

                    $editorial->addAsset($asset);
                    $newAssets->push($asset);

                    $editorial->recordEvent(new AssetCreated($asset, $request->user()));
                }

                $editorial->assignUser($userId, $role);

                if($role && $userId) {
                    $editorial->recordEvent(new UserAssigned($editorial->getKey(), $userId, $role));
                }
            });

            // Create packages in editing
            if ($editorial->isInEditing()) {
                $editorial->addPackage($editorial->default_language);
                
                $request->whenHas('languages', function ($languages) use ($editorial): void {
                    foreach ($languages as $language) {
                        $editorial->addPackage($language);
                    }
                });
            }

            $editorial->persist();

            $this->maybeCompleteTasks($editorial->refresh(), $editorial->assignee);

            DB::commit();
        } catch (AssetExistsException $th) {
            abort(Response::HTTP_CONFLICT, $th->getMessage());
        } catch (\Throwable $th) {
            DB::rollBack();
            abort(Response::HTTP_INTERNAL_SERVER_ERROR, $th->getMessage());
        }

        return EditorialResource::make($editorial->fresh('tasks', 'assists'));
    }

    private function maybeCompleteTasks(Editorial $editorial, User $user): void
    {
        // Auto-complete relevant tasks with more explicit handling
        $pendingTasks = $editorial->pendingTasks()->where('user_id', $user->getKey())
            ->afterQuery(function ($tasks) use ($user) {
                $tasks->each->setRelation('user', $user);
            })
            ->get();

        $pendingTasks->each(function (Task $task) {
            try {
                // Attempt to auto-complete the task
                $task->autoCompleteIfNeeded();
            } catch (\Throwable $e) {
                logger()->error('Task Auto-Complete Failed', [
                    'task_id' => $task->id,
                    'error' => $e->getMessage(),
                ]);
            }
        });
    }
}
