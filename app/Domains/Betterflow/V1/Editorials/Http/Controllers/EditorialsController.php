<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\ResourceCollection;

class EditorialsController extends Controller
{
    public function index()
    {
        return EditorialResource::collection(
            Editorial::query()->with([
                'tasks' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->notAssists()->latest(),
                'assists' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
                'watchers' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session')]),
                'assignee' => fn($query) => $query->with('roles', 'session'),
                'reporter' => fn($query) => $query->with('roles', 'session'),
                'translator' => fn($query) => $query->with('roles', 'session'),
                'toplineEditor' => fn($query) => $query->with('roles', 'session'),
                'copyEditor' => fn($query) => $query->with('roles', 'session'),
                'factChecker' => fn($query) => $query->with('roles', 'session'),
                'packages',
                // 'currentTask',
                'pitch' => fn($query) => $query->with('topic', 'vertical', 'type'),
            ])->withCount('comments', 'pendingTasks')->get()
        );
    }

    public function show(Editorial $editorial): EditorialResource
    {
        return EditorialResource::make($editorial->loadMissing([
            'comments.replies',
            'assets.assignee',
            'assets.tasks.user',
            'tasks' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->notAssists()->latest(),
            'assists' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session'), 'taskable'])->latest(),
            'watchers' => fn($query) => $query->with(['user' => fn($query) => $query->with('roles', 'session')]),
            'assignee' => fn($query) => $query->with('roles', 'session'),
            'reporter' => fn($query) => $query->with('roles', 'session'),
            'translator' => fn($query) => $query->with('roles', 'session'),
            'toplineEditor' => fn($query) => $query->with('roles', 'session'),
            'copyEditor' => fn($query) => $query->with('roles', 'session'),
            'factChecker' => fn($query) => $query->with('roles', 'session'),
            'packages',
            'pitch' => fn($query) => $query->with('topic', 'vertical', 'type'),
        ])->loadCount('comments'));
    }
}
