<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers;

use App\Domains\Betterflow\V1\Editorials\Http\Requests\StoreCommentRequest;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Concerns\IdentifiesModel;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class EditorialCommentsController
{
    use IdentifiesModel;

    public function index(Request $request, Editorial $editorial): ResourceCollection
    {
        // Retrieve all comments for the editorial and return them as a resource collection.
        return CommentsResource::collection($editorial->comments);
    }

    public function store(StoreCommentRequest $request, Editorial $editorial): JsonResponse
    {
        $parentComment = null;

        // Create a new comment object.
        $request->whenFilled('comment_id', function ($commentId) use ($editorial, &$parentComment) {
            $parentComment = Comment::findOrFail($commentId);
        });

        $comment = $editorial->comments()->create([
            'user_id' => $request->user()->getKey(),
            'comment' => $request->comment,
            'parent_id' => $parentComment?->getKey() ?? null,
        ]);

        // Return the newly created comment resource.
        return CommentsResource::make($comment->fresh())->response()->setStatusCode(201);
    }
}
