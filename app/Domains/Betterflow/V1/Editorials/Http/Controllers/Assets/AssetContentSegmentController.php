<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets;

use App\Domains\Betterflow\V1\Editorials\Assets\Actions\CreateSegment;
use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Assets\Resources\SegmentResource;
use Illuminate\Http\Request;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;

class AssetContentSegmentController extends Controller
{
    public function show(Request $request, Editorial $editorial, Asset $asset, string $tab): SegmentResource
    {
        $languageCode = $request->query('language', 'en-EN');

        return SegmentResource::make($asset->segment($tab, $languageCode));
    }

    public function update(Request $request, Editorial $editorial, Asset $asset, string $tab): SegmentResource
    {
        $languageCode = $request->query('language', 'en-EN');

        $segment = $asset->segment($tab, $languageCode);

        $segment->update([
            'data' => $request->json('data'),
        ]);

        return SegmentResource::make($segment->fresh());
    }
}
