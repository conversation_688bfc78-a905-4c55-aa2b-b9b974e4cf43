<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class AssetsController extends Controller
{
    public function index(Request $request, Editorial $editorial): AnonymousResourceCollection
    {
        return AssetResource::collection($editorial->assets()->with('assignee', 'content.segments', 'activities', 'tasks.taskable', 'tasks.user', 'editorial')->get());
    }

    public function show(Request $request, Editorial $editorial, Asset $asset): AssetResource
    {
        return AssetResource::make($asset->loadMissing('assignee', 'content.segments', 'activities', 'tasks.taskable', 'tasks.user', 'editorial', 'comments.user', 'comments.likes'));
    }
}
