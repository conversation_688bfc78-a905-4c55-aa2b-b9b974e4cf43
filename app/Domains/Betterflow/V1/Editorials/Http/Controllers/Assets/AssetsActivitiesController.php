<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets;

use Illuminate\Http\Request;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\ActivitiesResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class AssetsActivitiesController extends Controller
{
    public function __invoke(Request $request, Editorial $editorial, Asset $asset): AnonymousResourceCollection
    {
        return ActivitiesResource::collection($asset->activities()->with('causer')->get());
    }
}
