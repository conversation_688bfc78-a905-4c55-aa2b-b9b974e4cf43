<?php

namespace App\Domains\Betterflow\V1\Editorials\Http\Controllers\Assets;

use App\Domains\Betterflow\V1\Editorials\Http\Requests\StoreCommentRequest;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Concerns\IdentifiesModel;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AssetsCommentsController extends Controller
{
    use IdentifiesModel;

    public function index(Request $request, Editorial $editorial, Asset $asset): ResourceCollection
    {
        // Retrieve all comments for the asset and return them as a resource collection.
        return CommentsResource::collection($asset->comments);
    }

    public function store(StoreCommentRequest $request, Editorial $editorial, Asset $asset): CommentsResource
    {
        $parentComment = null;

        // Create a new comment object.
        $request->whenFilled('comment_id', function ($commentId) use ($editorial, &$parentComment) {
            $parentComment = Comment::findOrFail($commentId);
        });

        $comment = $asset->comments()->create([
            'user_id' => $request->user()->getKey(),
            'parent_id' => $parentComment?->getKey() ?? null,
            'comment' => $request->comment,
        ]);

        // Return the newly created comment resource.
        return new CommentsResource($comment->fresh());
    }

    public function reply(StoreCommentRequest $request, Editorial $editorial, Asset $asset, Comment $comment): CommentsResource
    {
       $comment = $asset->comments()->create([
            'user_id' => $request->user()->getKey(),
            'parent_id' => $comment->getKey(),
            'comment' => $request->comment,
        ]);

        return CommentsResource::make($comment->fresh());
    }
}
