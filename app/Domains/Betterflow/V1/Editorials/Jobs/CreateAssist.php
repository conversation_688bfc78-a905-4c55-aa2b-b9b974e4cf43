<?php

namespace App\Domains\Betterflow\V1\Editorials\Jobs;

use App\Domains\Betterflow\V1\Editorials\Events\AssistCreated;
use App\Domains\Betterflow\V1\Pitches\Commands\Concerns\HasActivityProperties;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Throwable;

class CreateAssist implements ShouldQueueAfterCommit
{
    use HandlesActivity;
    use HasActivityProperties;
    use Queueable;

    public $deleteWhenMissingModels = true;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public function __construct(
        #[WithoutRelations]
        protected Model $model,
        protected Task $task,
    ) {

        $this->onQueue('compass');
    }

    public function handle(): void
    {
        $model = $this->model;

        // Update editors tasks...
        $editorTask = $model->ownerTask();

        if ($editorTask && $editorTask->isPending()) {
            $editorTask->placeOnHold();
        }

        $newtask = $model->addAssist($this->task);

        if(method_exists($model, 'persist')) {
            $model->recordEvent(new AssistCreated($newtask));
            $model->persist();
        } else {
            AssistCreated::dispatch($newtask);
            $model->save();
        }
    }

    public function failed(Throwable $exception): void
    {
        logger('CreateAssist job failed', [
            'exception' => $exception->getMessage(),
            'model_id' => $this->model->id ?? null,
            'model_type' => get_class($this->model),
            'task' => $this->task,
        ]);
    }
}
