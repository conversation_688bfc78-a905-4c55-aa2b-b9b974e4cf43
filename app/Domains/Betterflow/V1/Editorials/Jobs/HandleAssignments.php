<?php

namespace App\Domains\Betterflow\V1\Editorials\Jobs;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class HandleAssignments implements ShouldQueueAfterCommit
{
    use Dispatchable;
    use HandlesActivity;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 3;

    public $backoff = [2, 5, 10];

    public function __construct(
        protected string $editorialId,
        protected Task $task,
    ) {

        $this->onQueue('compass');
    }

    public function handle(): void
    {
        // $editorial = Editorial::query()->findOrFail($this->editorialId);
        // Add any additional processing logic here
    }

    public function failed(Throwable $exception): void
    {
        \Log::error('HandleAssignments job failed', [
            'exception' => $exception->getMessage(),
            'editorial_id' => $this->editorialId,
            'task' => $this->task,
        ]);
    }
}
