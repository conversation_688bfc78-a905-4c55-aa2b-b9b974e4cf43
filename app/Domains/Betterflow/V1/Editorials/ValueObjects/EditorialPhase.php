<?php

namespace App\Domains\Betterflow\V1\Editorials\ValueObjects;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase as EnumsEditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;

class EditorialPhase
{
    public function __construct(public EnumsEditorialPhase $phase, public ?EditorialSubPhase $subPhase = null) {}

    public static function tryFromEditorial(Editorial $editorial): self
    {
        return new self($editorial->phase, $editorial->sub_phase ?? null);
    }
}
