<?php

namespace App\Domains\Betterflow\V1\Editorials;

use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Relations\Relation;
use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Fact;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Photo;
use App\Domains\Betterflow\V1\Editorials\Commands\UserAssigned;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Graphic;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Reporting;
use App\Domains\Betterflow\V1\Editorials\Policies\EditorialPolicy;
use App\Domains\Betterflow\V1\Editorials\Commands\EditorialAssigned;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Illustration;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\HeadlineSummary;
use App\Domains\Betterflow\V1\Editorials\Commands\Handlers\UserAssignedHandler;
use App\Domains\Betterflow\V1\Editorials\Commands\Handlers\EditorialAssignedHandler;
use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use App\Domains\Betterflow\V1\Editorials\Policies\EditorialPackagePolicy;
use App\Domains\Betterflow\V1\Editorials\Policies\AssetPolicy;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Foundation\Http\Middleware\TrimStrings;
use Illuminate\Http\Request;


class EditorialsServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Gate::policy(Editorial::class, EditorialPolicy::class);
        Gate::policy(EditorialPackage::class, EditorialPackagePolicy::class);
        Gate::policy(Asset::class, AssetPolicy::class);
      
        Relation::morphMap([
            'editorial' => Editorial::class,

            // Assets
            'asset' => Asset::class,
            'asset_content' => AssetContent::class,

            AssetType::Reporting->value => Reporting::class,
            AssetType::Photo->value => Photo::class,
            AssetType::Illustration->value => Illustration::class,
            AssetType::Headline_Summary->value => HeadlineSummary::class,
            AssetType::Graphic->value => Graphic::class,
            AssetType::Fact->value => Fact::class,
        ]);

        Bus::map([
            EditorialAssigned::class => EditorialAssignedHandler::class,
        ]);

        TrimStrings::skipWhen(function (Request $request) {
            return $request->is('**/assets/*') && $request->filled('data');
        });

        ConvertEmptyStringsToNull::skipWhen(function (Request $request) {
            return $request->is('**/assets/*') && $request->filled('data');
        });
    }
}
