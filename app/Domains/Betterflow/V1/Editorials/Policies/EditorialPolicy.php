<?php

namespace App\Domains\Betterflow\V1\Editorials\Policies;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Enums\Permission;
use App\Domains\Users\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;
use Illuminate\Http\Response as HttpResponse;

class EditorialPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     */
    public function assign(User $user, Editorial $editorial): Response
    {
        if ($user->hasAnyRole('super_admin', 'admin')) {
            return Response::allow();
        }

        if ($user->can(Permission::ASSIGN_USERS)) {
            return Response::allow();
        }

        if ($user->getKey() === $editorial->reporter_id) {
            return Response::allow();
        }

        return Response::denyWithStatus(HttpResponse::HTTP_FORBIDDEN, 'You do not have permission to assign users to this editorial');
    }

    public function request_assist(User $user, Editorial $editorial): Response
    {
        if ($editorial->hasAssignedUser($user)) {
            return Response::allow();
        }

        if ($user->can(Permission::REQUEST_EDITORIAL_ASSIST)) {
            return Response::allow();
        }

        return Response::denyWithStatus(HttpResponse::HTTP_FORBIDDEN, 'You do not have permission to request editorial assist');
    }

    public function create_packages(User $user, Editorial $editorial): Response
    {
        // if (!$editorial->isInPublishing()) {
        //     return Response::denyWithStatus(HttpResponse::HTTP_CONFLICT, 'Editorial is not in publishing phase');
        // }

        if ($editorial->hasAssignedUser($user) && $user->can(Permission::EDIT_EDITORIAL)) {
            return Response::allow();
        }

        if ($user->can(Permission::EDIT_EDITORIAL)) {
            return Response::allow();
        }

        return Response::denyWithStatus(HttpResponse::HTTP_FORBIDDEN, 'You do not have permission to create packages for this editorial');
    }

    public function update_packages(User $user, Editorial $editorial): Response
    {
        if ($editorial->hasAssignedUser($user)) {
            return Response::allow();
        }

        if ($user->can(Permission::EDIT_EDITORIAL)) {
            return Response::allow();
        }

        return Response::denyWithStatus(HttpResponse::HTTP_FORBIDDEN, 'You do not have permission to update packages for this editorial');
    }

    public function fact_check(User $user, Editorial $editorial): Response
    {
        if ($editorial->hasAssignedUser($user, 'fact_checker')) {
            return Response::allow();
        }

        if ($user->can(Permission::EDIT_EDITORIAL)) {
            return Response::allow();
        }

        return Response::denyWithStatus(HttpResponse::HTTP_FORBIDDEN, 'You do not have permission to fact check this editorial');
    }
}
