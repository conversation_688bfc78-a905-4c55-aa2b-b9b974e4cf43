<?php

namespace App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Database\Eloquent\Factories\Factory;

class AssetFactory extends Factory
{
    protected $model = Asset::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(4, true),
            'editorial_id' => Editorial::factory(),
            'public_id' => fake()->uuid(),
            'url' => fake()->url(),
            'type' => fake()->randomElement(['graphic', 'photo', 'illustration', 'headline_summary', 'reporting']),
            'attributes' => '{}',
        ];
    }
}
