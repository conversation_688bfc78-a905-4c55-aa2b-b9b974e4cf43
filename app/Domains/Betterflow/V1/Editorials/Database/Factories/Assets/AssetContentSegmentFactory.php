<?php

namespace App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets;

use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use Illuminate\Database\Eloquent\Factories\Factory;

class AssetContentSegmentFactory extends Factory
{
    protected $model = AssetContentSegment::class;

    public function definition(): array
    {
        return [
            'asset_content_id' => AssetContent::factory(),
            'segment' => $this->faker->word,
            'data' => [
                'title' => $this->faker->sentence,
                'content' => $this->faker->paragraphs(2, true),
                'metadata' => [
                    'author' => $this->faker->name,
                    'updated_at' => $this->faker->dateTime->format('Y-m-d H:i:s'),
                ],
            ],
        ];
    }

    public function forAssetContent(AssetContent $assetContent): static
    {
        return $this->state([
            'asset_content_id' => $assetContent->id,
        ]);
    }

    public function withSegment(string $segment): static
    {
        return $this->state([
            'segment' => $segment,
        ]);
    }

    public function withData(array $data): static
    {
        return $this->state([
            'data' => $data,
        ]);
    }

    public function empty(): static
    {
        return $this->state([
            'data' => [],
        ]);
    }
}