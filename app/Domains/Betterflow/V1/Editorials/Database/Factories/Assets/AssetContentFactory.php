<?php

declare(strict_types=1);

namespace App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use Illuminate\Database\Eloquent\Factories\Factory;

final class AssetContentFactory extends Factory
{
    protected $model = AssetContent::class;

    public function definition(): array
    {
        return [
            'asset_id' => Asset::factory(),
            'language_code' => 'en-EN',
            'data' => [
                'title' => fake()->sentence(),
                'content' => fake()->paragraphs(3, true),
                'metadata' => [
                    'author' => fake()->name(),
                    'created_at' => fake()->dateTime()->format('Y-m-d H:i:s'),
                ],
            ],
        ];
    }

    public function forAsset(Asset $asset): static
    {
        return $this->state([
            'asset_id' => $asset->id,
        ]);
    }

    public function withLanguage(string $languageCode): static
    {
        return $this->state([
            'language_code' => $languageCode,
        ]);
    }

    public function withData(array $data): static
    {
        return $this->state([
            'data' => $data,
        ]);
    }

    public function empty(): static
    {
        return $this->state([
            'data' => [],
        ]);
    }
}
