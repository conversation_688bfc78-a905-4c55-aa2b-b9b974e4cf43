<?php

namespace App\Domains\Betterflow\V1\Editorials\Database\Factories;

use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class EditorialFactory extends Factory
{
    protected $model = Editorial::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $pitch = Pitch::factory()->create();

        return [
            'pitch_id' => $pitch->getKey(),
            'public_id' => Str::orderedUuid()->toString(),
            'reporter_id' => User::factory(),
            'name' => str($pitch->short_name)->headline(),
            'phase' => EditorialPhase::Editing,
            'sub_phase' => EditorialSubPhase::EditingAssignment,
        ];
    }
}
