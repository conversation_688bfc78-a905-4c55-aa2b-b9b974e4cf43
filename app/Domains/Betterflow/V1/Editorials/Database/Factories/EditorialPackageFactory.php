<?php

namespace App\Domains\Betterflow\V1\Editorials\Database\Factories;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Editorials\Models\EditorialPackage;
use Illuminate\Database\Eloquent\Factories\Factory;

class EditorialPackageFactory extends Factory
{
    protected $model = EditorialPackage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'editorial_id' => Editorial::factory(),
            'language_code' => 'en-EN',
            'data' => [
                'title' => fake()->sentence(),
                'content' => fake()->paragraphs(3, true),
            ],
        ];
    }

    /**
     * Set a specific language code for the package.
     *
     * @param string $languageCode
     * @return Factory
     */
    public function withLanguage(string $languageCode): Factory
    {
        return $this->state(function (array $attributes) use ($languageCode) {
            return [
                'language_code' => $languageCode,
            ];
        });
    }
}
