<?php

namespace App\Domains\Betterflow\V1\Editorials\Models;

use App\Domains\Shared\Models\BasePivot;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Lunarstorm\LaravelDDD\Factories\HasDomainFactory;
use Spatie\Permission\Models\Role;

class Assignment extends BasePivot
{
    use HasDomainFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected $casts = [];

    public $table = 'assignments';

    public function assignable(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role', 'name');
    }

    public static function assign(User $user, Editorial $editorial): self
    {
        $assignment = new self();
        $assignment->user()->associate($user);
        $assignment->assignable()->associate($editorial);
        $assignment->role = $user->type;
        $assignment->save();

        return $assignment;
    }
}
