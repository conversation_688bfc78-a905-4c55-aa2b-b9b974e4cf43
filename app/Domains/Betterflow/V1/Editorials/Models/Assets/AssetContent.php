<?php

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets;

use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetContentFactory;
use App\Domains\Shared\Models\Activity;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\Concerns\HasResourceKey;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Mpociot\Versionable\VersionableTrait;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * AssetContent model representing detailed content for editorial assets.
 */
class AssetContent extends BaseDomainModel
{
    use LogsActivity;
    use HasResourceKey;
    use SoftDeletes;
    use VersionableTrait;

    protected $table = 'asset_contents';

    protected $guarded = [];

    protected $casts = [
        'data' => 'json',
        'deleted_at' => 'datetime',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('assets')
            ->logUnguarded();
    }

    public function tapActivity(Activity $activity, string $eventName): void
    {
        if (in_array($eventName, ['created', 'updated'])) {
            $activity->event = str($eventName)->headline()->prepend('AssetContent')->toString();
        }

        $activity->subject_id = $this->asset_id;
        $activity->subject_type = 'asset';
    }

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'asset_id');
    }

    public function segments(): HasMany
    {
        return $this->hasMany(AssetContentSegment::class, 'asset_content_id');
    }

    public function segment(string $segment): AssetContentSegment
    {
        return $this->segments()->where('segment', $segment)->firstOrFail();
    }

    protected static function newFactory(): AssetContentFactory
    {
        return AssetContentFactory::new();
    }
}
