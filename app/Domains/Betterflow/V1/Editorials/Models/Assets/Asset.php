<?php

namespace App\Domains\Betterflow\V1\Editorials\Models\Assets;

use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Database\Factories\Assets\AssetFactory;
use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Concerns\HasComments;
use App\Domains\Shared\Concerns\HasTasks;
use App\Domains\Shared\Contracts\HasName;
use App\Domains\Shared\Contracts\HasPublicKey as HasPublicKeyContract;
use App\Domains\Shared\Contracts\HasSlug;
use App\Domains\Shared\Models\Activity;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\Concerns\HasPublicKey;
use App\Domains\Users\Models\User;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Mpociot\Versionable\VersionableTrait;
use Parental\HasChildren;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Asset extends BaseDomainModel implements HasPublicKeyContract, HasSlug, HasName
{
    use CascadeSoftDeletes;
    use HasChildren;
    use HasComments;
    use HasPublicKey;
    use HasTasks;
    use LogsActivity;
    use SoftDeletes;
    use VersionableTrait;

    protected $casts = [
        'type' => AssetType::class,
        'deleted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    protected $cascadeDeletes = ['content'];

    protected $guarded = [];

    protected $with = [];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function (Asset $asset): void {
            if (! $asset->isDirty('slug')) {
                $asset->slug = str($asset->type->value)->slug();
            }
        });
    }

    public function getName(): string
    {
        return $this->getAttribute('name') ?? '';
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('assets')
            ->logUnguarded()
            ->dontSubmitEmptyLogs();
    }

    public function getRouteKeyName(): string
    {
        if (request()->has('channel_name')) {
            return 'public_id';
        }

        $asset = request()->route()?->parameter('asset');
        if ($asset && Str::isUuid($asset)) {
            return 'public_id';
        }

        return 'id';
    }

    public function tapActivity(Activity $activity, string $eventName): void
    {
        if (in_array($eventName, ['created', 'updated'])) {
            $activity->event = str($eventName)->headline()->prepend('Asset')->toString();
        }
    }

    public function getRelationForKey(string $key): ?Model
    {
        return match ($key) {
            'assigned_to_id' => $this->assignee,
            'editorial_id' => $this->editorial,
            default => null
        };
    }

    public function editorial(): BelongsTo
    {
        return $this->belongsTo(Editorial::class);
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_id');
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_id');
    }

    public function content(): HasMany
    {
        return $this->hasMany(AssetContent::class, 'asset_id');
    }

    public function contentForLanguage(string $languageCode): ?AssetContent
    {
        return $this->content()->where('language_code', $languageCode)->firstOrFail();
    }

    public function segment(string $segment, string $languageCode = 'en-EN'): AssetContentSegment
    {
        return AssetContentSegment::query()->lockForUpdate()->where('segment', $segment)->where('asset_content_id', $this->content()->where('language_code', $languageCode)->first()?->getKey())->firstOrFail();
    }

    public function isComplete(): bool
    {
        return $this->completed_at !== null;
    }

    public function complete(): void
    {
        $this->completed_at = now();
        $this->save();
    }

    public function markComplete(): void
    {
        $this->completed_at = now();
        $this->save();
    }

    public function markPending(): void
    {
        $this->completed_at = null;
        $this->save();
    }

    public function isPending(): bool
    {
        return $this->completed_at === null;
    }

    public function scopeComplete(Builder $query): Builder
    {
        return $query->whereNotNull('completed_at')->where('progress', 100);
    }

    public function defaultLanguage(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->editorial->default_language,
            set: fn(string $language): string => $this->editorial->default_language = $language,
        );
    }

    public function slug(): string
    {
        return $this->slug ?? $this->type->slug();
    }

    protected function childTypes(): array
    {
        return [
            AssetType::Reporting->value => Reporting::class,
            AssetType::Photo->value => Photo::class,
            AssetType::Illustration->value => Illustration::class,
            AssetType::Headline_Summary->value => HeadlineSummary::class,
            AssetType::Graphic->value => Graphic::class,
            AssetType::Fact->value => Fact::class,
        ];
    }

    protected static function newFactory(): AssetFactory
    {
        return AssetFactory::new();
    }
}
