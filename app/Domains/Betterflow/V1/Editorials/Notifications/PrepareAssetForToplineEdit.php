<?php

namespace App\Domains\Betterflow\V1\Editorials\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use App\Domains\Notifications\Http\Resources\NotificationUserResource;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\Task;
use App\Domains\Users\Models\User;

class PrepareAssetForToplineEdit extends Notification
{
    use Queueable;

    public function __construct(
        public readonly ?Asset $asset,
        public readonly ?Task $task,
        public readonly ?User $byUser,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        return [
            'who_did_it' => NotificationUserResource::make($notifiable),
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'asset' => NotificationSubjectResource::make($this->asset),
                'task' => NotificationSubjectResource::make($this->task),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        $assetType = $this->asset->type->plural() ?? 'asset';
        return "prepare_{$assetType}_for_topline_edit";
    }
}
