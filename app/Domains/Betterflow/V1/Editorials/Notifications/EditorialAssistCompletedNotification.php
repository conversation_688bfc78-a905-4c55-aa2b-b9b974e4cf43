<?php

namespace App\Domains\Betterflow\V1\Editorials\Notifications;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\Task as ContractsTask;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class EditorialAssistCompletedNotification extends Notification
{
    use Queueable;

    public function __construct(
        public readonly ?Editorial $editorial,
        public readonly ?string $title,
        public readonly ?string $message,
        public readonly ?ContractsTask $assist = null,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {

        $editorial = $this->editorial;
        $assist = $this->assist;
        return [
            'who_did_it' => SimpleUserResource::make($assist->user),
            'from_where' => config('lighthouse.betterflow.namespace'),
            'for_what' => NotificationSubjectResource::make($editorial),
            'resources' => [
                'editorial' => NotificationSubjectResource::make($editorial),
                'task' => NotificationSubjectResource::make($assist),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'assist_completed';
    }
}
