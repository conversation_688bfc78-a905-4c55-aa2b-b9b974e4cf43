<?php

namespace App\Domains\Betterflow\V1\Editorials\Notifications;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\Task as ContractsTask;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class AssetAssistCompletedNotification extends Notification
{
    use Queueable;

    public function __construct(
        public readonly ?Asset $asset,
        public readonly ?string $title,
        public readonly ?string $message,
        public readonly ?ContractsTask $assist = null,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        $asset = $this->asset;
        $editorial = $asset->loadMissing('editorial')->editorial;
        $assist = $this->assist;
        
        return [
            'who_did_it' => SimpleUserResource::make($assist->user),
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'asset' => NotificationSubjectResource::make($asset),
                'task' => NotificationSubjectResource::make($assist),
                'editorial' => NotificationSubjectResource::make($editorial),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'assist_completed';
    }
}
