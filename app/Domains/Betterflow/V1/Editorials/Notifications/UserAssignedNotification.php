<?php

namespace App\Domains\Betterflow\V1\Editorials\Notifications;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class UserAssignedNotification extends Notification
{
    use Queueable;

    public function __construct(
        public ?Asset $asset,
        public ?Task $task,
        public ?User $byUser,
    ) {}

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toArray($notifiable)
    {

        $editorial = $this->asset->loadMissing('editorial')->editorial;
        
        return [
            'who_did_it' => SimpleUserResource::make($this->byUser),
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'editorial' => NotificationSubjectResource::make($editorial),
                'user' => NotificationSubjectResource::make($this->asset),
                'task' => NotificationSubjectResource::make($this->task),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return 'user_assigned';
    }
}
