<?php

namespace App\Domains\Betterflow\V1\Editorials\Notifications;

use App\Domains\Shared\Data\NotificationDTO;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;

class FrontendNotification extends Notification implements ShouldQueue, ShouldBroadcast
{
    use Queueable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly ?string $title = null,
        public readonly ?string $message = null,
        public readonly ?string $type = null,
    ) {
        $this->onQueue('default');
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage())
            ->line($this->title)
            ->line($this->message)
            ->action('Notification Action', url('/'))
            ->greeting('Lighthouse');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return (new NotificationDTO(
            user: $notifiable,
            forWhat: $notifiable,
            type: $this->type ?? null,
            title: $this->title ?? 'Default Notification',
            message: $this->message ?? 'Default Notification',
            origin: 'lighthouse',
        ))->toArray();
    }

    public function broadcastOn(): Channel|array
    {
        return [
            new PrivateChannel('User.Notifications.2'),
        ];
    }

    // public function broadcastAs(): string
    // {
    //     return 'frontend.notification';
    // }

    // public function broadcastType(): string
    // {
    //     return 'frontend.notification';
    // }

    public function broadcastWith(): array
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'type' => $this->type,
        ];
    }

    public function toBroadcast(): BroadcastMessage
    {
        return new BroadcastMessage([
            'title' => $this->title,
            'message' => $this->message,
            'type' => $this->type,
        ]);
    }

    public function databaseType(object $notifiable): string
    {
        return $this->type ?? 'default';
    }
}
