<?php

namespace App\Domains\Betterflow\V1\Editorials\Events;

use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TaskCompleted implements ShouldQueue
{
    use Dispatchable;
    use SerializesModels;

    public function __construct(
        public Task $task,
    ) {}
}
