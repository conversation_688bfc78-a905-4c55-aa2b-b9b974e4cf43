<?php

namespace App\Domains\Betterflow\V1\Editorials\Events;

use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;

class EditorialMovedSubPhase implements ShouldQueue
{
    use Dispatchable;

    public function __construct(
        public readonly string $editorialId,
        public readonly EditorialPhase $previousPhase,
    )
    {}
}
