<?php

namespace App\Domains\Betterflow\V1\Editorials\Events;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;

class EditorialReassigned implements ShouldQueue
{
    use Dispatchable;

    public function __construct(
        public readonly string $editorialId,
        public readonly string $userId,
        public readonly ?string $previousAssignee = null,
    ) {}
}
