<?php

namespace App\Domains\Betterflow\V1\Editorials\Events;

use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EditorialMovedToProduction implements ShouldQueue
{
    use Dispatchable;
    use SerializesModels;

    public function __construct(public string $editorialId, public EditorialPhase $previousPhase) {}
}
