<?php

namespace App\Domains\Betterflow\v1\Editorials\Assets\Models;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Shared\Models\BaseDomainModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class AssetContentSegment extends BaseDomainModel
{
    protected $table = 'asset_content_segments';

    protected $guarded = [];

    protected $casts = [
        'data' => 'json',
    ];

    public function assetContent(): BelongsTo
    {
        return $this->belongsTo(AssetContent::class, 'asset_content_id');
    }

    public function asset(): HasOneThrough
    {
        return $this->hasOneThrough(Asset::class, AssetContent::class, 'id', 'id', 'asset_content_id', 'asset_id');
    }
}
