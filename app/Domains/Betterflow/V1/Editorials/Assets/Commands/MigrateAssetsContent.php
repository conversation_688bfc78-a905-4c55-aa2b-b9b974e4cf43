<?php

namespace App\Domains\Betterflow\V1\Editorials\Assets\Commands;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use Illuminate\Console\Command;

class MigrateAssetsContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'compass:migrate-assets-content';

    protected $segmentKeys = [
        'story-content',
        'photo',
        'workshop',
        'plan',
        'final-illustrations',
        'graphics',
        'research',
        'sketches',
        'fact-content',
        'uploads',
        'research-links',
        'sources',
        'captions'
    ];

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate assets content to new segment system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $assetContent = AssetContent::get();

        foreach ($assetContent as $assetContent) {
            foreach ($this->segmentKeys as $segmentKey) {
                $data = data_get($assetContent->data, $segmentKey);

                if ($data) {
                    $assetContent->segments()->create([
                        'segment' => $segmentKey,
                        'data' => $data,
                    ]);
                }
            }
        }
    }
}
