<?php

namespace App\Domains\Betterflow\V1\Editorials\Assets\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class SegmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data = [
            'asset_id' => $this->asset->getResourceKey(),
            'asset' => $this->asset->slug,
            ...Arr::except($data, ['id', 'asset_content_id', 'created_at', 'updated_at', 'deleted_at']),
        ];

        return $data;
    }
}
