<?php

use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_content_segments', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(AssetContent::class, 'asset_content_id')->constrained()->cascadeOnDelete();
            $table->string('segment');
            $table->json('data')->nullable();

            $table->unique(['asset_content_id', 'segment']);

            $table->index('asset_content_id');
            $table->index('segment');
            
            $table->timestamps();
        });
    }
};
