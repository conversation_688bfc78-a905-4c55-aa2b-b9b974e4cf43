<?php

namespace App\Domains\Betterflow\V1\Editorials\Assets\Database\Factories;

use App\Domains\Betterflow\V1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Database\Factories\AssetContentFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

class AssetContentSegmentFactory extends Factory
{
    protected $model = AssetContentSegment::class;

    public function definition(): array
    {
        return [
            'asset_content_id' => AssetContentFactory::new(),
            'segment' => fake()->randomElement([
                'story-content',
                'photo',
                'workshop',
                'plan',
                'final-illustrations',
                'graphics',
                'research',
                'sketches',
                'fact-content',
                'uploads',
                'research-links',
                'sources',
                'captions',
            ]),
            'data' => [
                'content' => fake()->paragraph(),
                'metadata' => [
                    'created_by' => fake()->name(),
                    'version' => fake()->numberBetween(1, 5),
                ],
            ],
        ];
    }

    public function storyContent(): static
    {
        return $this->state(fn (array $attributes) => [
            'segment' => 'story-content',
            'data' => [
                'content' => fake()->paragraphs(3, true),
                'word_count' => fake()->numberBetween(100, 1000),
                'metadata' => [
                    'author' => fake()->name(),
                    'last_edited' => fake()->dateTime()->format('Y-m-d H:i:s'),
                ],
            ],
        ]);
    }

    public function photo(): static
    {
        return $this->state(fn (array $attributes) => [
            'segment' => 'photo',
            'data' => [
                'images' => [
                    [
                        'url' => fake()->imageUrl(),
                        'caption' => fake()->sentence(),
                        'alt_text' => fake()->sentence(),
                    ],
                ],
                'metadata' => [
                    'photographer' => fake()->name(),
                    'shoot_date' => fake()->date(),
                ],
            ],
        ]);
    }

    public function research(): static
    {
        return $this->state(fn (array $attributes) => [
            'segment' => 'research',
            'data' => [
                'notes' => fake()->paragraphs(2, true),
                'sources' => [
                    fake()->url(),
                    fake()->url(),
                ],
                'metadata' => [
                    'researcher' => fake()->name(),
                    'research_date' => fake()->date(),
                ],
            ],
        ]);
    }

    public function withEmptyData(): static
    {
        return $this->state(fn (array $attributes) => [
            'data' => null,
        ]);
    }

    public function withMinimalData(): static
    {
        return $this->state(fn (array $attributes) => [
            'data' => [
                'content' => fake()->sentence(),
            ],
        ]);
    }
}