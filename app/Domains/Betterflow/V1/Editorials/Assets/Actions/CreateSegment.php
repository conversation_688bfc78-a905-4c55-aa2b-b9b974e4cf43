<?php

namespace App\Domains\Betterflow\V1\Editorials\Assets\Actions;

use App\Domains\Betterflow\v1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Assets\Resources\SegmentResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Database\UniqueConstraintViolationException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateSegment
{
    use AsAction;

    public function handle(Asset $asset, AssetContent $content, string $segment, array $data)
    {
       try {
        $segment = $content->segments()->create([
            'segment' => $segment,
            'data' => $data,
        ]);
       } catch (UniqueConstraintViolationException $th) {
         abort(422, 'Segment already exists');
       }

        return $segment;
    }

    public function authorize(): bool
    {
        return true;
    }

    public function asController(ActionRequest $request, Editorial $editorial, Asset $asset)
    {
        $languageCode = $request->input('language', 'en-EN');
        $segment = $request->input('segment');
        $data = $request->input('data');

        $segment = $this->handle($asset, $asset->contentForLanguage($languageCode), $segment, $data);

        return $segment;
    }

    public function jsonResponse(AssetContentSegment $segment): SegmentResource
    {
        return SegmentResource::make($segment);
    }
}
