<?php

namespace App\Domains\Betterflow\V1\Editorials\Enums;

use App\Domains\Shared\Concerns\EnumHelpers;

enum AssetType: string
{
    use EnumHelpers;

    case Graphic = 'graphic';
    case Illustration = 'illustration';
    case Photo = 'photo';
    case Headline_Summary = 'headline_summary';
    case Reporting = 'reporting';
    case Fact = 'fact';

    public function plural(): string
    {
        return match ($this) {
            self::Graphic => 'graphics',
            self::Illustration => 'illustrations',
            self::Photo => 'photos',
            self::Headline_Summary => 'headline_summary',
            self::Reporting => 'reporting',
            self::Fact => 'fact',
        };
    }

    public function slug(): string
    {
        return str($this->value)->slug()->toString();
    }
}
