<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Enums\Assets\RecordableEvents;
use App\Domains\Betterflow\V1\Editorials\Events\AssetReassigned;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Users\Models\User;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ReassignAsset
{
    use AsAction;

    public function authorize(#[CurrentUser] User $user, Asset $asset)
    {
        return true;
    }

    public function handle(Asset $asset, int $newAssignee, int $actorId)
    {
        $oldAssignee = $asset->assigned_to_id;

        throw_if($oldAssignee === $newAssignee, HttpException::class, 400, 'Asset is already assigned to this user');

        DB::beginTransaction();

        try {
            $asset->update([
                'assigned_to_id' => $newAssignee,
            ]);

            $asset->tasks()?->update([
                'user_id' => $newAssignee,
            ]);

            RecordActivity::dispatch(new ActivityObject(
                'assets',
                $asset,
                RecordableEvents::Reassigned,
                '',
                [
                    'asset' => $asset,
                    'from_assignee' => $oldAssignee,
                    'to_assignee' => $newAssignee,
                ],
                $actorId
            ));

            AssetReassigned::dispatch($asset, $oldAssignee, $newAssignee, $actorId);

            DB::commit();

            return $asset;
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function rules(): array
    {
        return [
            'assign_to' => ['required', 'integer', 'exists:users,id'],
        ];
    }

    public function asController(ActionRequest $request, Editorial $editorial, Asset $asset)
    {
        return $this->handle($asset, $request->validated('assign_to'), $request->user()->id);
    }

    public function jsonResponse(Asset $asset): AssetResource
    {
        return AssetResource::make($asset->fresh());
    }
}
