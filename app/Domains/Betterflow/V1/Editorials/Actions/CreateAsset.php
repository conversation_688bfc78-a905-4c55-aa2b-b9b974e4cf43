<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Enums\AssetType;
use App\Domains\Betterflow\V1\Editorials\Events\AssetCreated;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Fact;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Graphic;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\HeadlineSummary;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Illustration;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Photo;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Reporting;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Shared\Helpers\LanguageHelper;
use App\Exceptions\AssetExistsException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

// TODO: See https://www.laravelactions.com/2.x/dispatch-jobs.html#unique-jobs
class CreateAsset
{
    use AsAction;

    public function handle(string $type, $userId, array $data = []): Asset
    {
        $asset = $this->makeAssetFor($type);
        $asset->assigned_to_id = $userId;

        if ($assetData = data_get($data, 'asset')) {
            $asset->fill($assetData);
        }

        if ($content = data_get($data, 'data', false)) {
            $asset->content()->create([
                'language_code' => data_get($data, 'language', 'en-EN'),
                'data' => $content,
            ]);
        }

        return $asset;
    }

    public function authorize(Request $request, Editorial $editorial): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'language' => ['sometimes', 'string', Rule::in(LanguageHelper::languageCodes()->pluck('code'))],
            'assets' => ['required', 'array', 'min:1'],
            'assets.*.type' => ['required', 'string', Rule::in(AssetType::cases())],
            'assets.*.name' => ['required', 'string', 'max:255'],
            'assets.*.slug' => ['sometimes', 'string', 'max:255'],
            'assets.*.description' => ['sometimes', 'string', 'max:255'],
            'assets.*.url' => ['sometimes', 'url', 'max:255'],
            'assets.*.schema_version' => ['sometimes', 'string', 'max:255'],
            'assets.*.data' => ['sometimes', 'json'],
            'assets.*.assignee' => ['required', 'integer', 'exists:users,id'],
        ];
    }

    public function prepareForValidation(ActionRequest $request): void
    {
        $assets = $request->assets;

        foreach ($assets as $key => $asset) {
            if ($data = data_get($asset, 'data')) {
                $asset = [
                    ...$asset,
                    'data' => json_encode($data, 1),
                ];
                $assets[$key] = $asset;
            }
        }

        $request->merge([
            'assets' => $assets,
        ]);
    }

    public function asController(ActionRequest $request, Editorial $editorial): Collection
    {
        $data = $request->validated();

        $user = $request->user();
        $assets = collect(data_get($data, 'assets'));
        $newAssets = collect();

        try {
            DB::beginTransaction();
            $assets->each(function (array $asset) use ($editorial, $data, $newAssets): void {
                if ($editorial->hasAssetsOfType($asset['type'])) {
                    throw new AssetExistsException(sprintf('The editorial already has 1 %s asset.', $asset['type']));
                }

                $newAsset = $this->handle($asset['type'], $asset['assignee'], [
                    'language' => data_get($data, 'language', 'en-EN'),
                    'data' => data_get($asset, 'data', null),
                    'asset' => collect($asset)->only('name', 'slug', 'description', 'url', 'schema_version')->toArray(),
                ]);

                $editorial->addAsset($newAsset);
                $newAssets->push($newAsset);
            });

            $editorial->persist();

            $newAssets->each(function ($asset) use ($user): void {
                AssetCreated::dispatch($asset, $user);
            });

            DB::commit();
        } catch (AssetExistsException $th) {
            abort(500, $th->getMessage());
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

        return $newAssets;
    }

    public function jsonResponse(Collection $assets): AnonymousResourceCollection|AssetResource
    {
        if ($assets->count() == 1) {
            return AssetResource::make($assets->first()->refresh());
        }

        return AssetResource::collection($assets->map->refresh());
    }

    private function makeAssetFor(string $roleOrType): Asset
    {
        return match ($roleOrType) {
            'story_editor', AssetType::Reporting->value => new Reporting(),
            'photo_editor', AssetType::Photo->value => new Photo(),
            'graphics_editor', AssetType::Graphic->value => new Graphic(),
            'illustrator', AssetType::Illustration->value => new Illustration(),
            'headline_summary', AssetType::Headline_Summary->value => new HeadlineSummary(),
            'fact_checker', AssetType::Fact->value => new Fact(),

            default => throw new \InvalidArgumentException('Cannot create asset for: ' . $roleOrType),
        };
    }
}
