<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class FoldEditorials
{
    use AsAction;

    public function handle(array $editorials, int|string $foldedBy)
    {
        $editorials = Editorial::query()->whereIn('id', $editorials)->get();

        DB::transaction(function () use ($editorials, $foldedBy) {
            collect($editorials)->each(fn($editorial) => FoldEditorial::run($editorial, $foldedBy));
        });
    }

    public function rules()
    {
        return [
            'editorials' => ['required', 'array'],
            'editorials.*' => ['exists:editorials,id'],
            'folded_by' => ['required', 'exists:users,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function asController(ActionRequest $request)
    {
        return $this->handle($request->validated('editorials'), $request->validated('folded_by') ?? $request->user()->getKey());
    }
}
