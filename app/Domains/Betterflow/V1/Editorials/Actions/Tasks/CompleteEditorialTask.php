<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions\Tasks;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CompleteEditorialTask
{
    use AsAction;

    public function asController(ActionRequest $request, Editorial $editorial, Task $task, CompleteTask $completeTask)
    {
        return rescue(function () use ($request, $task, $completeTask, $editorial): TaskResource {
            $notes = '';

            if ($request->has('notes')) {
                $notes = $request->input('notes');
            }

            $completeTask->handle($task, $notes);

            $editorial->persist();

            return TaskResource::make($task)->additional(['editorial_id' => $editorial->getResourceKey()]);
        });
    }
}
