<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions\Tasks;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Container\Attributes\RouteParameter;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CompleteTask
{
    use AsAction;

    public function handle(Task $task, mixed $notes = ''): void
    {
        if ($task->completed()) {
            throw new InvalidArgumentException('task is already completed');
        }

        $task->notes = $notes;

        $task->markComplete();

        if (! $task->taskable->hasPendingAssists() && $task->isAssist()) {
            $tasksToUpdate = $task->taskable->tasksOnHold();

            $tasksToUpdate->when(
                $task->taskable instanceof Editorial,
                fn($query) => $query->where('user_id', $task->taskable->currentOwner()->getKey())
            );

            $tasksToUpdate->get()->each->removeHold();
        }

        $task->taskable->persist();
        $task->persist();
    }

    public function rules(): array
    {
        return [
            'notes' => ['nullable', 'string'],
        ];
    }

    public function getValidationMessages(): array
    {
        return [
            'notes.string' => 'Notes must be a string',
        ];
    }

    public function authorize(ActionRequest $request, #[CurrentUser] $user, #[RouteParameter('task')] Task $task): bool
    {
        /** @var User $user */
        return $user->is($task->user);
    }

    public function asController(ActionRequest $request, ?Editorial $editorial, ?Asset $asset, Task $task)
    {
        return rescue(function () use ($request, $task) {
            $notes = '';

            if ($request->has('notes')) {
                $notes = $request->input('notes');
            }

            $this->handle($task, $notes);

            return TaskResource::make($task);
        }, function (Throwable $e) {
            return response()->json([
                'message' => $e->getMessage(),
            ], 500);
        });
    }
}
