<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions\Tasks;

use App\Domains\Betterflow\V1\Editorials\Events\TaskIncompleted;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class IncompleteTask
{
    use AsAction;

    public function handle(Task $task, ?string $notes = null, string $instructions = ''): void
    {
        if (! $task->completed()) {
            throw new InvalidArgumentException('task is not completed');
        }

        $task->notes = '';

        // Create new TaskData with updated properties
        $currentData = $task->data;
        $properties = isset($currentData['properties']) ? $currentData['properties'] : [];
        $properties['instructions'] = $instructions;

        $taskData = TaskData::make([
            'model' => $task->taskable,
            'label' => $currentData['action']['label'] ?? $task->title,
            'properties' => $properties,
        ]);

        $task->data = $taskData->toArray();
        $task->markIncomplete();

        if ($task->isAssist()) {
            $task->taskable->ownerTask()?->placeOnHold();
        }

        $task->persist();

        TaskIncompleted::dispatch($task, $instructions);
    }

    public function asController(ActionRequest $request, Task $task)
    {
        return rescue(function () use ($request, $task) {
            $notes = '';
            $instructions = '';

            if ($request->filled('notes')) {
                $notes = $request->input('notes');
            }

            if ($request->filled('instructions')) {
                $instructions = $request->string('instructions');
            }

            $this->handle($task, $notes, $instructions);

            return TaskResource::make($task);
        }, function (Throwable $e) {
            return response()->json([
                'message' => $e->getMessage(),
            ], 500);
        });
    }
}
