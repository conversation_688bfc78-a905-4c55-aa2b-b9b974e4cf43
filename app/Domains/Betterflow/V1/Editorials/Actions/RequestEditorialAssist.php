<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Jobs\CreateAssist;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Data\TaskData;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Date;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RequestEditorialAssist
{
    use AsAction;

    public function authorize(
        #[CurrentUser] $user,
        #[RouteParameter('editorial')] Editorial $editorial
    ): bool {
        return $user->can('request_assist', $editorial);
    }

    public function rules(ActionRequest $request): array
    {
        return [
            'type' => ['sometimes', 'string'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['sometimes', 'string', 'max:3000'],
            'due_at' => ['sometimes', 'date', 'after:today'],
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'instructions' => ['sometimes', 'string', 'max:3000'],
        ];
    }

    public function handle(Editorial $editorial, Task $task): void
    {
        CreateAssist::dispatch($editorial, $task);
    }

    public function asController(ActionRequest $request, Editorial $editorial): JsonResponse
    {
        $this->handle($editorial, $this->taskFromRequest($editorial, $request));

        return response()->json([
            'success' => true,
            'message' => 'Assistance requested.',
        ]);
    }

    public function taskFromRequest(Editorial $editorial, ActionRequest $request): Task
    {
        $data = $request->validated();

        $type = data_get($data, 'type');
        $type = $type ? $type : sprintf('%s_review_assist', $editorial->sub_phase->value);

        return new Task(
            userId: data_get($data, 'user_id'),
            title: data_get($data, 'title'),
            description: data_get($data, 'description'),
            type: $type,
            requestedById: $request->user()->getKey(),
            dueAt: Date::createFromDate(data_get($data, 'due_at', null)),
            data: new TaskData(
                model: $editorial,
                label: data_get($data, 'title'),
                properties: [
                    'instructions' => data_get($data, 'instructions'),
                ],
            ),
        );
    }
}
