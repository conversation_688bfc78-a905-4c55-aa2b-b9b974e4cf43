<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Events\EditorialReassigned;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ReassignEditorial
{
    use AsAction;

    public function handle(Editorial $editorial, $assignee)
    {
        $previousAssignee = $editorial->assigned_to_id;

        $editorial->assignee()->associate($assignee);

        $editorial->recordEvent(new EditorialReassigned($editorial->getKey(), $assignee, $previousAssignee));

        $editorial->persist();

        return $editorial->refresh();
    }

    public function rules()
    {
        return [
            'assignee' => ['required', 'integer', 'exists:users,id']
        ];
    }

    public function asController(ActionRequest $request, Editorial $editorial)
    {
        return rescue(function () use ($request, $editorial) {
            return $this->handle($editorial, $request->assignee);
        });
    }

    public function authorize(ActionRequest $request)
    {
        return $request->user()->hasRole('admin');
    }

    public function jsonResponse(Editorial $editorial)
    {
        return response()->json([
            'editorial' => EditorialResource::make($editorial),
            'success' => true,
            'message' => 'Editorial reassigned.',
        ]);
    }
}
