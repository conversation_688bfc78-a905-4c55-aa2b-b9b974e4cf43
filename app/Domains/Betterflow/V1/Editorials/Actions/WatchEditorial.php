<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Illuminate\Http\JsonResponse;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class WatchEditorial
{
    use AsAction;

    public function authorize(ActionRequest $request, Editorial $editorial): bool
    {
        return true;
    }

    public function handle(ActionRequest $request, Editorial $editorial): bool
    {
        $editorial->toggleWatch($request->user()->getKey());

        $editorial->refresh();

        return $editorial->isWatched($request->user()->getKey());
    }

    public function asController(ActionRequest $request, Editorial $editorial)
    {
        return $this->handle($request, $editorial);
    }

    public function jsonResponse(bool $isWatching): JsonResponse
    {
        return response()->json([
            'watching' => $isWatching
        ]);
    }
}
