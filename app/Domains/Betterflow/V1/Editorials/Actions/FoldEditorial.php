<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\ActionRequest;

class FoldEditorial
{
    use AsAction;

    public function handle(Editorial $editorial, int|string $foldedBy)
    {
        if ($editorial->trashed()) {
            throw new \Exception('Editorial is already folded');
        }

        $editorial->fold($foldedBy);
    }

    public function rules(ActionRequest $request)
    {
        return [
            'folded_by' => ['sometimes', 'exists:users,id'],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }       

    public function asController(ActionRequest $request, Editorial $editorial)
    {
        return $this->handle($editorial, $request->validated('folded_by') ?? $request->user()->getKey());
    }
}
