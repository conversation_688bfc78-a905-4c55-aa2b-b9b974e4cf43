<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\ValueObjects\EditorialPhase as VOEditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialPhase;
use App\Domains\Betterflow\V1\Editorials\Enums\EditorialSubPhase;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedToProduction;
use App\Domains\Betterflow\V1\Editorials\Events\EditorialMovedSubPhase;
use App\Domains\Betterflow\V1\Editorials\Http\Resources\EditorialResource;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Illuminate\Validation\Rule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class MoveEditorialPhase
{
    use AsAction;

    public function authorize(ActionRequest $request, Editorial $editorial): bool
    {
        return true;
    }

    public function rules(ActionRequest $request): array
    {
        return [
            'phase' => ['nullable', 'string', Rule::in(EditorialPhase::cases())],
            'sub_phase' => ['nullable', 'string', function ($attribute, $value, $fail) use ($request) {

                $phase = EditorialPhase::tryFrom($request->input('phase'));

                if (!EditorialSubPhase::stagesForPhase($phase)->contains(EditorialSubPhase::tryFrom($value))) {
                    $fail('Invalid subphase');
                };
            }],
        ];
    }

    public function handle(Editorial $editorial, ActionRequest $request): Editorial
    {

        $phase = EditorialPhase::tryFrom($request->phase) ?? $editorial->phase;
        $subPhase = EditorialSubPhase::tryFrom($request->sub_phase) ??  $editorial->initialSubphaseFor(EditorialPhase::tryFrom($request->phase));

        $previousPhase = VOEditorialPhase::tryFromEditorial($editorial);

        $editorial->fill([
            'phase' => $phase,
            'sub_phase' => $subPhase,
        ]);

        if ($phase !== $previousPhase->phase) {
            $editorial->recordEvent(new EditorialMovedToProduction($editorial->getKey(), $previousPhase));
        }

        if ($subPhase !== $previousPhase->subPhase) {
            $editorial->recordEvent(new EditorialMovedSubPhase($editorial->getKey(), $previousPhase));
        }

        $this->maybeCompleteTasks($editorial, $editorial->assignee);

        $editorial->persist();

        return $editorial->refresh();
    }

    public function asController(ActionRequest $request, Editorial $editorial): Editorial
    {
        return $this->handle($editorial, $request);
    }

    public function jsonResponse(Editorial $editorial, ActionRequest $request): EditorialResource
    {
        return EditorialResource::make($editorial->fresh('tasks', 'assists'));
    }

    private function maybeCompleteTasks(Editorial $editorial, User $user): void
    {
        // Auto-complete relevant tasks with more explicit handling
        $pendingTasks = $editorial->fresh()->pendingTasksForUser($user->getKey());

        $pendingTasks->each(function ($task) {
            try {
                // Attempt to auto-complete the task
                $task->autoCompleteIfNeeded();
            } catch (\Throwable $e) {
                logger()->info('Task Auto-Complete Failed', [
                    'task_id' => $task->id,
                    'error' => $e->getMessage(),
                ]);
            }
        });
    }
}
