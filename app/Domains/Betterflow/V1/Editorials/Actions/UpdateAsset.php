<?php

namespace App\Domains\Betterflow\V1\Editorials\Actions;

use App\Domains\Betterflow\V1\Editorials\Http\Resources\AssetResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Shared\Helpers\JsonHelper;
use App\Domains\Shared\Helpers\LanguageHelper;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateAsset
{
    use AsAction;
    use HandlesActivity;

    public function authorize(Request $request, Asset $asset): bool
    {
        return true;
    }

    public function handle(Asset $asset, Request $request, array $data): Asset
    {
        // Fix: Marking an asset as complete was deleting the data
        // Need to preserve the data - Only update asset data if it's provided in the request
        //

        $request->whenFilled('data', function (mixed $assetData) use ($asset, $request): void {
            $language = $request->input('language', $asset->default_language);

            // FIXME: Laravel middleware strips empty strings to null, revert this before storing in the db
            $assetData = JsonHelper::convertNullValues($assetData);

            if ($assetContent = $asset->content()->where('language_code', $language)->first()) {
                $assetContent->update([
                    'data' => $assetData,
                ]);
            } else {
                $asset->content()->create([
                    'language_code' => $language,
                    'data' => $assetData,
                ]);
            }
        });

        // Handle completion of an asset
        if ($request->filled('completed')) {
            if ($request->boolean('completed') == true) {
                if ($asset->isComplete()) {
                    abort(500, 'Asset is already completed');
                }

                $asset->markComplete();

                // TODO: Dispatch event check all assets completed
            }

            if ($request->boolean('completed') == false) {
                $asset->markPending();
            }
        }

        return $asset;
    }

    public function rules(): array
    {
        return [
            'language' => ['sometimes', 'string', Rule::in(LanguageHelper::languageCodes()->pluck('code'))],
            'data' => ['sometimes', 'array'],
            'completed' => ['sometimes', 'boolean'],
        ];
    }

    public function asController(ActionRequest $request, Editorial $editorial, Asset $asset): Asset
    {
        return $this->handle($asset, $request, $request->except('language'));
    }

    public function jsonResponse(Asset $asset): AssetResource
    {
        return AssetResource::make($asset->fresh()->loadMissing('assignee', 'content'));
    }
}
