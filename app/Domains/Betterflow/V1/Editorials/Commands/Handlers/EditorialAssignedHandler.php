<?php

namespace App\Domains\Betterflow\V1\Editorials\Commands\Handlers;

use App\Domains\Betterflow\V1\Editorials\Commands\EditorialAssigned;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;

class EditorialAssignedHandler
{
    public function handle(EditorialAssigned $command): void
    {
        $editorial = $command->editorial->refresh();

        // Handle creating tasks and notifications based on assets created.
        // if ($assets->isNotEmpty()) {
        //     $assets->each(function (Asset $asset) use ($editorial) {
        //         ds($asset);
        //         $this->createTask($editorial->reporter, $asset, $editorial);

        //         // $this->createToplineTask($asset->assignee, $asset, $editorial);
        //     });
        // }
    }
}
