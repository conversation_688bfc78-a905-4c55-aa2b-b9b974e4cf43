<?php

namespace App\Domains\Betterflow\V1\Editorials\Commands;

use App\Domains\Shared\Contracts\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;

class UserAssigned implements Command
{
    use Dispatchable;

    public function __construct(
        public readonly string $userId,
        public readonly string $editorialId,
        public readonly ?string $role = null,
    ) {}
}
