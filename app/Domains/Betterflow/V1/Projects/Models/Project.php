<?php

namespace App\Domains\Betterflow\V1\Projects\Models;

use App\Domains\Shared\Models\BaseDomainModel;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\Factory;


/**
 * Project model representing collaborative work and project tracking.
 */
class Project extends BaseDomainModel
{
    use SoftDeletes;

    protected $casts = [];

    public static function newFactory(): Factory|null
    {
        return null;
    }
}
