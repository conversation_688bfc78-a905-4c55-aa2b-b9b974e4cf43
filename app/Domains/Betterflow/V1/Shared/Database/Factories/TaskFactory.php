<?php

namespace App\Domains\Betterflow\V1\Shared\Database\Factories;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Domains\Betterflow\V1\Shared\Tasks\Models\Task>
 */
class TaskFactory extends Factory
{
    protected $model = Task::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        $taskable = fake()->randomElement([Editorial::factory(), Asset::factory()]);

        $taskable = $taskable->create();

        $tasksTypes = $this->tasksFor($taskable);

        return [
            'user_id' => User::factory(),
            'type' => fake()->randomElement($tasksTypes),
            'progress' => fake()->numberBetween(0, 100),
            'taskable_type' => $taskable->getMorphClass(),
            'taskable_id' => $taskable->getKey(),
        ];
    }

    private function tasksFor($taskable) {
        return match($taskable::class) {
            Editorial::class => ['assign_editors', 'test_editorial_task'],
            Asset::class => ['test_asset_task', 'test_assist'],
        };
    }
}
