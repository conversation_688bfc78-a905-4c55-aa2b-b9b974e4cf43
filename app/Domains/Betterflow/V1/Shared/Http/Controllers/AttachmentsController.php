<?php

namespace App\Domains\Betterflow\V1\Shared\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AttachmentsController extends Controller
{
    public function update(Request $request, Media $attachment): void
    {
    }

    public function destroy(Request $request, Media $attachment): void
    {
        $attachment->delete();
    }
}
