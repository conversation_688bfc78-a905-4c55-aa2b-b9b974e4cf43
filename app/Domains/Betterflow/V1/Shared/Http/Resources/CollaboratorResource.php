<?php

namespace App\Domains\Betterflow\V1\Shared\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollaboratorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $photo_url = $this->resource->getPhotoUrl();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'photo_url' => $photo_url,
            // 'role' => $this->whenLoaded('roles', $this->loadMissing('roles')->roles?->first()?->name),
            'has_session' => $this->resource->loadMissing('session')->session ?? false,
        ];
    }
}
