<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Listeners;

use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskProgressUpdated;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Queue\InteractsWithQueue;

class LogTaskActivity
{
    use HandlesActivity;
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TaskProgressUpdated $event): void
    {
        $this->recordActivity(new ActivityObject(
            logName: str($event->task->taskable_type)->plural()->toString(),
            on: $event->task->taskable,
            by: $event->userId,
            event: RecordableEvents::TaskProgressUpdated,
            description: 'Updated progress on this task',
            properties: [
                'changes' => $event->task,
            ],
        ));
    }
}
