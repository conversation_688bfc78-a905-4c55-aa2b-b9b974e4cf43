<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Listeners;

use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCreated;
use App\Domains\Betterflow\V1\Shared\Tasks\Notifications\TaskAdded as NotificationsTaskAdded;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendsNotificationWhenTaskIsAdded implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    public function handle(TaskCreated $event)
    {
        $task = $event->task;

        $notificationType = data_get($task->data, 'properties.notification_type', $task->type);

        $task->user->notify(new NotificationsTaskAdded(
            task: $task,
            title: $task->title,
            message: $task->description,
            type: $notificationType,
            byUser: $event->user,
        ));
    }
}
