<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Listeners;

use App\Domains\Betterflow\V1\Editorials\Events\TaskCompleted as EditorialEventsTaskCompleted;
use App\Domains\Betterflow\V1\Pitches\Listeners\Traits\HandlesActivity;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted;
use App\Domains\Shared\Data\ActivityObject;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogTaskCompleted implements ShouldQueue
{
    use HandlesActivity;
    use InteractsWithQueue;

    public $tries = 3;

    public $queue = 'compass';

    /**
     * Handle the event.
     */
    public function handle(TaskCompleted|EditorialEventsTaskCompleted $event): void
    {
        $this->recordActivity(new ActivityObject(
            logName: str($event->task->taskable_type)->plural()->toString(),
            on: $event->task->taskable,
            by: $event->userId,
            event: RecordableEvents::TaskCompleted,
            description: 'Completed this task',
            properties: [
                'changes' => $event->task,
            ],
        ));
    }
}
