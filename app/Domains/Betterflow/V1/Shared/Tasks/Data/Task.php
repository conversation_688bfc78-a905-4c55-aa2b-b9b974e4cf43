<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Data;

use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;
use Carbon\CarbonImmutable;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Carbon;

final readonly class Task implements Arrayable
{
    public function __construct(
        public readonly string $userId,
        public readonly string $title,
        public readonly string $description,
        public readonly TaskType|string $type,
        public readonly ?string $requestedById = null,
        public readonly null|Carbon|CarbonImmutable $dueAt = null,
        public readonly ?TaskData $data = null,
    ) {}

    public function toArray(): array
    {
        return [
            'user_id' => $this->userId,
            'requested_by_id' => $this->requestedById ?? null,
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'due_at' => $this->dueAt ?? null,
            'data' => $this->data?->toArray(),
        ];
    }
}
