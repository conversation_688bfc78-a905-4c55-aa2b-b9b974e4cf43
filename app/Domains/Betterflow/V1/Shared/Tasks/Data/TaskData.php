<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Data;

use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;

final readonly class TaskData implements Arrayable
{
    public function __construct(
        public Model|Asset $model,
        public string $label,
        public ?array $properties = [],
    ) {}

    public function toArray(): array
    {
        $modelType = $this->model->getMorphClass();

        return [
            $modelType => $this->model,
            'action' => [
                'label' => $this->label,
            ],
            'properties' => $this->properties,
        ];
    }

    public static function make(array $attributes): self
    {
        return new self(
            model: data_get($attributes, 'model'),
            label: data_get($attributes, 'label'),
            properties: data_get($attributes, 'properties', []),
        );
    }
}
