<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Shared\Tasks\Events\UserReassigned;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Gate;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ReassignTask
{
    use AsAction;

    public static function routes(Router $router)
    {
        $router->put('/shared/tasks/{task}/reassign', static::class)->name('common.tasks.reassign');
    }

    public function authorize(ActionRequest $request, #[RouteParameter('task')] Task $task)
    {
        return Gate::authorize('reassign', $task);
    }

    public function handle(Task $task, User $toUser, mixed $notes = '')
    {
        throw_if($task->user->is($toUser), new HttpException(Response::HTTP_CONFLICT, 'task is already assigned to this user'));
        $fromUser = $task->user;
        $task->assignToUser($toUser, $notes);

        UserReassigned::dispatch(
            $task->getKey(),
            $fromUser->getKey(),
            $toUser->getKey(),
            $notes
        );

        return $task;
    }

    public function rules()
    {
        return [
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'notes' => ['nullable', 'string'],
        ];
    }

    public function asController(Task $task, Request $request)
    {
        $notes = '';

        $toUser = User::findOrFail($request->integer('user_id'));

        if ($request->has('notes')) {
            $notes = $request->input('notes');
        }

        return $this->handle($task, $toUser, $notes);
    }

    public function jsonResponse(Task $task)
    {
        return TaskResource::make($task->fresh());
    }
}
