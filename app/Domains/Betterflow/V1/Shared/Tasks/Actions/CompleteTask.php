<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Exception;
use Illuminate\Auth\Access\Response;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Gate;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CompleteTask
{
    use AsAction;

    public static function routes(Router $router)
    {
        $router->put('tasks/{task}/complete', static::class)->name('tasks.complete');
        $router->put('/shared/tasks/{task}/complete', static::class)->name('common.tasks.complete');
    }


    public function handle(Task $task, mixed $notes = ''): Task
    {
        throw_if($task->completed(), new HttpException(HttpResponse::HTTP_CONFLICT, 'This Task is already completed'));
        throw_if(!$task->taskable, new ModelNotFoundException('task is missing taskable model'));

        try {
            $task->markComplete($notes);

            return $task;
        } catch (ModelNotFoundException $th) {
            throw $th;
        }
    }

    public function rules(): array
    {
        return [
            'notes' => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'notes.string' => 'Notes must be a string',
        ];
    }

    public function authorize(ActionRequest $request, #[RouteParameter('task')] Task $task): Response
    {
        return Gate::authorize('complete', $task);
    }

    public function asController(ActionRequest $request, Task $task)
    {
        $notes = '';

        if ($request->has('notes')) {
            $notes = $request->input('notes');
        }

        return $this->handle($task, $notes);
    }

    public function jsonResponse(?Task $task): TaskResource|null
    {
        throw_unless($task, new Exception('Something went wrong while trying to complete this task'));

        return TaskResource::make($task->fresh());
    }
}
