<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Http\Request;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Gate;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

class IncompleteTask
{
    use AsAction;

    public static function routes(Router $router)
    {
        $router->put('tasks/{task}/incomplete', static::class)->name('tasks.incomplete');
        $router->put('/shared/tasks/{task}/incomplete', static::class)->name('common.tasks.incomplete');
    }

    public function authorize(ActionRequest $request, #[RouteParameter('task')] Task $task)
    {
        return Gate::authorize('incomplete', $task);
    }

    public function handle(Task $task, string $notes = '')
    {
        throw_unless($task->completed(), new HttpException(Response::HTTP_CONFLICT, 'This Task is not completed'));

        $task->markIncomplete($notes);

        return $task;
    }

    public function rules()
    {
        return [
            'notes' => ['nullable', 'string'],
        ];
    }

    public function asController(Task $task, Request $request)
    {
        $notes = '';

        if ($request->has('notes')) {
            $notes = $request->input('notes');
        }

        return $this->handle($task, $notes);
    }

    public function jsonResponse(?Task $task)
    {
        throw_unless($task, new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, 'Something went wrong while trying to mark this task as incomplete'));

        return TaskResource::make($task->fresh());
    }
}
