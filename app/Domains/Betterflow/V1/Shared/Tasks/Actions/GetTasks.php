<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Routing\Router;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTasks
{
    use AsAction;

    public static function routes(Router $router): void
    {
        $router->get('/tasks', static::class)->name('tasks.index');
    }

    public function handle()
    {
        abort_if(app()->isProduction(), 403, 'Not allowed on production');

        return TaskResource::collection(Task::query()->with(['taskable'])->get());
    }
}
