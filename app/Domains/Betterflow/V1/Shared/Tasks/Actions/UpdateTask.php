<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Actions;

use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskProgressUpdated;
use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Auth\Access\Response;
use Illuminate\Container\Attributes\RouteParameter;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Gate;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\HttpException;

class UpdateTask
{
    use AsAction;

    public static function routes(Router $router): void
    {
        // Old endpoint
        $router->put('/tasks/{task}', static::class)->name('tasks.update');
        // New endpoint
        $router->put('/shared/tasks/{task}/update', static::class)->name('common.tasks.update');
    }

    public function handle(Task $task, array $data): Task
    {
        throw_unless($task->taskable, new HttpException(HttpResponse::HTTP_CONFLICT, 'This task is missing taskable model'));
        throw_if($task->completed(), new HttpException(HttpResponse::HTTP_CONFLICT, 'This task is already completed'));
        $progress = data_get($data, 'progress', null);

        $notes = data_get($data, 'notes', '');

        if (data_get($data, 'complete', false)) {
            $task->markComplete($notes);
        }

        if ($progress) {
            $task->fill([
                'progress' => $progress,
            ]);

            if ($progress === 100) {
                $task->markComplete($notes);
            } else {
                $task->recordEvent(new TaskProgressUpdated($task->fresh(), auth()->id()));
            }



            $task->persist();
        }

        return $task;
    }

    public function asController(Task $task, ActionRequest $request): Task
    {
        return $this->handle($task, $request->validated());
    }

    public function jsonResponse(Task $task, Request $request): TaskResource
    {
        return TaskResource::make($task->fresh());
    }

    public function authorize(ActionRequest $request, #[RouteParameter('task')] Task $task): Response
    {
        return Gate::authorize('update', $task);
    }

    public function rules(): array
    {
        return [
            'progress' => ['required_without:complete', 'integer', 'min:0', 'max:100'],
            'complete' => ['required_without:progress', 'boolean'],
            'notes' => ['nullable', 'string'],
        ];
    }

    public function getValidationMessages(): array
    {
        return [
            'progress.required_without' => 'Progress is required when complete is not provided',
            'complete.required_without' => 'Complete is required when progress is not provided',
            'progress.integer' => 'Progress must be an integer',
            'progress.min' => 'Progress must be more than 0',
            'progress.max' => 'Progress must be less than 100',
            'complete.boolean' => 'Complete must be either true or false',
        ];
    }

    public function getValidationErrorBag(): string
    {
        return 'task';
    }
}
