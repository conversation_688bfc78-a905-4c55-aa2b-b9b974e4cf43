<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Models;

use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Betterflow\V1\Shared\Database\Factories\TaskFactory;
use App\Domains\Betterflow\V1\Shared\Tasks\Casts\TaskTypeCast;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\Task as ContractsTask;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\RecordableEvents;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\TaskStatus;
use App\Domains\Betterflow\V1\Shared\Tasks\Events\TaskCompleted;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Users\Models\User;
use DateTimeInterface;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Parental\HasChildren;

/**
 * Task model representing workflow tasks and assignments.
 */
class Task extends BaseDomainModel implements ContractsTask
{
    protected $fillable = [
        'title',
        'taskable_id',
        'taskable_type',
        'status',
        'progress',
        'description',
        'type',
        'notes',
        'user_id',
        'requested_by_id',
        'data',
        'due_at',
        'completed_at',
    ];

    protected $casts = [
        'data' => 'json',
        'status' => TaskStatus::class,
        // 'type' => TaskTypeCast::class,
        'completed_at' => 'datetime',
        'due_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:sP');
    }    

    protected static function booted()
    {
        static::creating(function ($task): void {
            if (! $task->isDirty('status')) {
                $task->status = TaskStatus::Pending;
            }
        });

        static::created(function ($task): void {
            $activity = new ActivityObject(
                logName: str($task->taskable_type)->plural(),
                on: $task->taskable,
                event: RecordableEvents::TaskCreated,
                description: $task->data->label ?? $task->title ?? 'Default description',
                properties: [],
            );

            RecordActivity::dispatch($activity);
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by_id');
    }

    public function hasRequester(): bool
    {
        return $this->requestedBy()->exists();
    }

    public function taskable(): MorphTo
    {
        return $this->morphTo('taskable');
    }

    public function isOwnerTask(): bool
    {
        return $this->taskable->assignee?->is($this->user) && ! $this->isAssist();
    }

    public function completed(): bool
    {
        return $this->status === TaskStatus::Completed && ! is_null($this->completed_at);
    }

    public function cancelled(): bool
    {
        return $this->status === TaskStatus::Canceled;
    }

    public function failed(): bool
    {
        return $this->status === TaskStatus::Failed;
    }

    public function isPending(): bool
    {
        return $this->status === TaskStatus::Pending;
    }

    public function isAssist(): bool
    {
        return ! is_null($this->requested_by_id) || $this instanceof Assist;
    }

    public function placeOnHold(): void
    {
        $this->status = TaskStatus::OnHold;
        $this->persist();

        $this->user->notify(new DefaultNotification(
            forWhat: $this->taskable,
            title: 'Your task is on hold',
            message: 'Your task is on hold while waiting for some assists to complete',
            type: $this->type,
        ));
    }

    public function removeHold(): void
    {
        $this->status = TaskStatus::Pending;
        $this->persist();

        $this->user->notify(new DefaultNotification(
            forWhat: $this->taskable,
            title: 'Your task is no longer on hold',
            message: 'All tasks/assists have been completed, and you can resume your task',
            type: $this->type,
        ));
    }

    /**
     * Log detailed task state changes for debugging
     *
     * @param string $action
     * @param array $additionalContext
     * @return void
     */
    private function logStateChange(string $action, array $additionalContext = []): void
    {
        $context = [
            'task_id' => $this->id,
            'current_status' => $this->status,
            'type' => $this->type,
            'user_id' => $this->user_id,
            'taskable_type' => $this->taskable_type,
            'taskable_id' => $this->taskable_id,
            'action' => $action,
        ];

        // Merge additional context
        $context = array_merge($context, $additionalContext);

        // Use DS logging for detailed tracking
        logger()->info('Task State Change', $context);
    }

    public function markComplete(string $notes = '', ?int $completedBy = null): bool|self
    {
        throw_if(is_null($this->taskable), new ModelNotFoundException('task model is missing.'));
        throw_if(is_null($this->user), new ModelNotFoundException('task user is missing.'));

        $this->logStateChange('complete_attempt');

        if (!$this->canTransitionTo(TaskStatus::Completed)) {
            $this->logStateChange('complete_prevented');
        }

        if ($this->completed()) {
            $this->logStateChange('complete_prevented_already_completed');
            return $this;
        }

        if ($this->canTransitionTo(TaskStatus::Completed)) {
            $this->status = TaskStatus::Completed;
            $this->notes = $notes;
            $this->completed_at = now()->utc();
            $this->progress = '100';

            $saved = tap($this)->save();
            $this->logStateChange('complete_success', ['saved' => $saved]);
            TaskCompleted::dispatch($this, $completedBy ?? auth()->id());
            return $saved;
        }

        return false;
    }

    public function markIncomplete(string $notes = ''): void
    {
        $this->status = TaskStatus::Pending;
        $this->completed_at = null;
        $this->notes = $notes;
        $this->progress = 0;

        $this->persist();
    }

    public function assignToUser(User $user, string $notes = ''): void
    {
        $this->notes = $notes;
        $this->user()->associate($user);
        $this->persist();
    }

    public function canTransitionTo(TaskStatus $targetStatus): bool
    {
        // Implement state transition rules
        switch ($targetStatus) {
            case TaskStatus::Completed:
                // Add specific validation logic for completion
                if ($this->status === TaskStatus::Canceled) {
                    return false;
                }

                // Check if all prerequisites are met
                if (method_exists($this->taskable, 'canCompleteTask')) {
                    return $this->taskable->canCompleteTask($this);
                }

                return true;

            case TaskStatus::Pending:
                // Prevent unnecessary reversion to pending
                return $this->status !== TaskStatus::Completed;

            default:
                return true;
        }
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', TaskStatus::Pending);
    }

    public function scopeActiveAndRecent(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->where('status', TaskStatus::Pending)
                ->orWhere('completed_at', '>=', now()->subDays(7));
        });
    }

    public static function findPendingTasksWithContext(array $filters = []): Collection
    {
        $query = self::query()
            ->with('taskable')
            ->where('status', TaskStatus::Pending);

        // Apply additional filters
        foreach ($filters as $key => $value) {
            $query->where($key, $value);
        }

        $tasks = $query->get();

        return $tasks;
    }

    public function scopeAssists(Builder $query): Builder
    {
        return $query->where('type', 'like', '%_assist')->whereNotNull('requested_by_id');
    }

    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', TaskStatus::Completed)->whereNotNull('completed_at')->whereProgress(100);
    }

    public function scopeOnHold(Builder $query): Builder
    {
        return $query->where('status', TaskStatus::OnHold);
    }

    public function scopeOverdue(Builder $query, Carbon $referenceDate = null): Builder
    {
        $referenceDate = $referenceDate ?? now();

        return $query->where(function ($q) use ($referenceDate) {
            $q->whereNotNull('due_at')
                ->where('due_at', '<', $referenceDate)
                ->whereIn('status', [
                    TaskStatus::Pending
                ]);
        });
    }

    public function cancel(): void
    {
        $this->status = TaskStatus::Canceled;
        $this->persist();
    }

    public function fail(): void
    {
        $this->status = TaskStatus::Failed;
        $this->persist();
    }

    public function shouldAutoComplete(): bool
    {
        // Ensure taskable is loaded
        if (!$this->taskable) {
            $this->loadMissing('taskable');
        }

        // Get the auto-complete tasks for the current phase
        $autoCompleteTasks = $this->taskable->autoCompleteTasks();

        // Convert enum to string for comparison
        $taskTypeValue = $this->type instanceof TaskType
            ? $this->type->value
            : $this->type;

        $shouldAutoComplete = in_array($this->type, $autoCompleteTasks);

        // Detailed debugging
        // ds('Auto Complete Check Details', [
        //     'task_id' => $this->id,
        //     'taskable_type' => get_class($this->taskable),
        //     'auto_complete_tasks' => $autoCompleteTasks,
        //     'current_task_type' => $taskTypeValue,
        //     'should_auto_complete' => $shouldAutoComplete,
        // ]);

        return $shouldAutoComplete;
    }

    public function autoCompleteIfNeeded(): void
    {
        // Ensure taskable is loaded
        if (!$this->taskable) {
            $this->loadMissing('taskable');
        }

        $autoCompleteTasks = [];

        // Get the auto-complete tasks for the current phase
        if (method_exists($this->taskable, 'autoCompleteTasks')) {
            $autoCompleteTasks = $this->taskable->autoCompleteTasks();
        }

        // Convert enum to string for comparison
        $taskTypeValue = $this->type instanceof TaskType
            ? $this->type->value
            : $this->type;

        $shouldAutoComplete = in_array($taskTypeValue, $autoCompleteTasks);

        if ($shouldAutoComplete && $this->isPending()) {
            // Complete the task
            $this->markComplete();

            // Force a save with a fresh timestamp
            $this->updated_at = now();
            $this->persist();
        }
    }

    public function scopeNotAssists($query): Builder
    {
        return $query->whereNull('requested_by_id')->whereNot('type', 'LIKE', '%_assist');
    }

    protected static function newFactory(): Factory
    {
        return TaskFactory::new();
    }
}
