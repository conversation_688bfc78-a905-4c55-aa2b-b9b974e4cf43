<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Notifications;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class AssistRequestedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public readonly Task $task,
        public readonly ?Editorial $editorial,
        public readonly ?Asset $asset,
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $requester = $this->task->requestedBy ?? null;
        $messageModel = $this->asset ?? $this->editorial;
        $frontendRoute = $this->asset ? [
                'path' => 'editorial/{editorial}/asset/{asset}/overview',
                'params' => [
                    'editorial' => $this->editorial->getResourceKey(),
                    'asset' => $this->asset->slug,
            ]
            ] : [
                'path' => 'editorial/{editorial}/asset/{asset}/overview',
                'params' => ['editorial' => $this->editorial->getResourceKey(), 'asset' => 'reporting'],
            ];

        $message = (new MailMessage)
            ->subject(sprintf('%s requested your assistance', $requester->name))
            ->line(sprintf('%s requested your assistance on: %s', $requester->name, $messageModel->getName()))
            ->action('Notification Action', URL::frontendUrlFromPath(data_get($frontendRoute, 'path'), data_get($frontendRoute, 'params')))
            ->line('Thank you for using our application!');

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'who_did_it' => SimpleUserResource::make($this->task->requestedBy),
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'task' => NotificationSubjectResource::make($this->task),
                'asset' => NotificationSubjectResource::make($this->asset ?? null),
                'editorial' => NotificationSubjectResource::make($this->editorial ?? null)
            ]
        ];
    }

    public function databaseType(object $notifiable): string
    {
        $notificationType = data_get($this->task->data, 'properties.notification_type', $this->task->type);
        if ($notificationType instanceof TaskType) {
            return $notificationType->value;
        }

        return $notificationType;
    }
}
