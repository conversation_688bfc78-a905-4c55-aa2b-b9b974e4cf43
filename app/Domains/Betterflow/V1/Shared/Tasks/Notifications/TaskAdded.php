<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Notifications;

use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\Task;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Notifications\Http\Resources\NotificationUserResource;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class TaskAdded extends Notification
{
    use Queueable;

    public function __construct(
        public readonly ?Task $task,
        public readonly ?string $title,
        public readonly ?string $message,
        public readonly ?string $type,
        public readonly ?User $byUser,
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        $resourceType = $this->task->taskable->getMorphClass();

        return [
            'who_did_it' => NotificationUserResource::make($this->byUser) ?? null,
            'from_where' => config('lighthouse.betterflow.namespace'),
            'resources' => [
                'task' => NotificationSubjectResource::make($this->task),
                $resourceType => NotificationSubjectResource::make($this->task->taskable),
            ],
        ];
    }

    public function databaseType(object $notifiable): string
    {
        return $this->type ?? $this->task->type ?? 'task_added';
    }
}
