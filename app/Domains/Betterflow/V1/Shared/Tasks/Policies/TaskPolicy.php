<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Policies;

use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Auth\Access\Response;

class TaskPolicy
{
    public function update(User $user, Task $task): Response
    {
        return $user->is($task->user) ? Response::allow() : Response::deny('You do not own this task.');
    }

    public function complete(User $user, Task $task): Response
    {
        return $user->is($task->user) ? Response::allow() : Response::deny('You do not own this task.');
    }

    public function incomplete(User $user, Task $task): Response
    {
        return $user->is($task->user) ? Response::allow() : Response::deny('You do not own this task.');
    }

    public function reassign(User $user, Task $task): Response
    {
        return $user->is($task->user) ? Response::allow() : Response::deny('You do not own this task.');
    }
}
