<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Casts;

use App\Domains\Betterflow\V1\Shared\Tasks\Contracts\TaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\EditorialTaskType;
use App\Domains\Betterflow\V1\Shared\Tasks\Enums\PitchTaskType;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class TaskTypeCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): TaskType|string|null
    {
        $modelType =  $model->taskable->getMorphClass();

        // ds('Task Type Cast', [
        //     'modelType' => $modelType,
        //     'key' => $key,
        //     'attributes' => $attributes,
        //     'value' => $value,
        // ]);

        return EditorialTaskType::tryFrom($value);

        return match($modelType) {
            'editorial' => EditorialTaskType::tryFrom($value),
        //     'pitch' => PitchTaskType::tryFrom($value),
        //     'asset' => $value,
        //     default => $value,   
        };
     }

    public function set(Model $model, string $key, mixed $value, array $attributes) { 
        if ($value instanceof TaskType) { 
            return $value->value;
        }
        
        return $value;
    }
    
}
