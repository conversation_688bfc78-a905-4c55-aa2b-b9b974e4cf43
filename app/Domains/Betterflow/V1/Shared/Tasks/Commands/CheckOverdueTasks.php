<?php

namespace App\Domains\Betterflow\V1\Shared\Tasks\Commands;

use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use Illuminate\Console\Command;

class CheckOverdueTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:overdue-tasks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Checks for overdue tasks';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Checking overdue tasks');

        $tasks = Task::overdue()->with(['user', 'taskable'])->get();
        $this->info(sprintf('Found %d overdue %s.', $tasks->count(), str('task')->plural($tasks->count())));

        foreach ($tasks as $task) {
            if ($task->user->unreadNotifications()->where('type', 'overdue_task')->where('data->for_what->id', $task->id)->exists()) {
                $this->info('User already notified, and still waiting to read notification');
                continue;
            }

            $task->user->notify(new DefaultNotification(
                forWhat: $task,
                message: 'Your task is overdue',
                title: 'Overdue Task',
                type: 'overdue_task',
            ));

            $this->info('Sent notification to ' . $task->user->name);
        }
    }
}
