<?php

namespace App\Domains\Betterflow\V1\Shared;

use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Betterflow\V1\Shared\Tasks\Policies\TaskPolicy;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class SharedServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        Gate::policy(Task::class, TaskPolicy::class);
    }
}
