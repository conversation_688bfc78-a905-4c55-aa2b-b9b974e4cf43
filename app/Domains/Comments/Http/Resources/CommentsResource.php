<?php

namespace App\Domains\Comments\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Likes\Http\Resources\LikesResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $comment = $this->resource->loadMissing(['replies', 'user', 'likes']);

        return [
            'id' => $comment->getKey(),
            'comment' => $comment->comment,
            'user' => SimpleUserResource::make($comment->user),
            'parent_id' => $comment->parent_id,
            'commentable_type' => $comment->commentable_type,
            'commentable_id' => $comment->commentable_id,
            'created_at' => $comment->created_at,
            'updated_at' => $comment->updated_at,
            'replies' => $this->whenLoaded('replies', self::collection($comment->replies)),
            'like_count' => $comment->likes->count(),
            'likes' => $this->whenLoaded('likes', LikesResource::collection($comment->likes)),
        ];
    }
}
