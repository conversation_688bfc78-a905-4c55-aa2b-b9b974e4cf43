<?php

namespace App\Domains\Comments\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Likes\Http\Resources\LikesResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivityCommentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->resource->loadMissing(['user', 'likes']);

        return [
            'id' => $this->getKey(),
            'comment' => $this->resource->comment,

            'user' => SimpleUserResource::make($this->user),
            'like_count' => $this->resource->likes->count(),
            'likes' => $this->whenLoaded('likes', LikesResource::collection($this->resource->likes)),
            // 'parent_id' => $this->parent_id,
            // 'commentable_type' => $this->commentable_type,
            // 'commentable_id' => $this->commentable_id,
            // 'created_at' => $this->created_at,
            // 'updated_at' => $this->updated_at,
        ];
    }
}
