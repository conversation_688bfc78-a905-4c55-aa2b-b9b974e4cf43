<?php

namespace App\Domains\Comments\Http\Controllers;

use App\Domains\Comments\Http\Resources\CommentsResource;
use App\Domains\Comments\Models\Comment;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @tags Comments
 */
class CommentReplyController extends Controller
{
    /**
     * Store a new comment reply.
     *
     * @param  Request  $request  The HTTP request instance.
     * @param  Comment  $comment  The comment to reply to.
     * @return JsonResource The newly created comment resource.
     */
    public function __invoke(Request $request, Comment $comment): JsonResource
    {
        // Create a new comment instance.
        $reply = new Comment();

        // Set the comment and parent ID.
        $reply->comment = $request->get('comment');
        $reply->parent_id = $comment->getKey();

        // Associate the comment with the authenticated user.
        $reply->user()->associate($request->user());

        // Save the comment to the database.
        $reply->save();

        // Return the newly created comment resource.
        return CommentsResource::make($reply);
    }
}
