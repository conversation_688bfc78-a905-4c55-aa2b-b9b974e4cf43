<?php

namespace App\Domains\Comments\Http\Controllers;

use App\Domains\Comments\Models\Comment;
use App\Domains\Shared\Models\CommentLike;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CommentToggleLikeController extends Controller
{
    /**
     * Handle the request to toggle a like on a comment.
     *
     * @param  \Illuminate\Http\Request  $request  The HTTP request instance.
     * @param  \App\Domains\Comments\Models\Comment  $comment  The comment to toggle the like status for.
     *
     * @return void
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     *
     * @tags Comments
     */

    public function __invoke(Request $request, Comment $comment): void
    {
        // Retrieve the metadata from the request or set it to an empty array.
        $metadata = $request->meta ?? [];

        // Retrieve the value from the request or set it to null.
        $value = $request->value ?? null;

        // Check if the authenticated user has already liked the comment.
        /* @phpstan-ignore-next-line */
        if (CommentLike::has($comment, $request->user())) {
            // If yes, unlike the comment.
            $request->user()->unlike(model: $comment);
        } else {
            // If no, like the comment with the specified value and metadata.
            $request->user()->like(model: $comment, value: $value, data: $metadata);
        }
    }
}
