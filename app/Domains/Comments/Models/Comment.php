<?php

namespace App\Domains\Comments\Models;

use App\Domains\Betterflow\V1\Pitches\Jobs\RecordActivity;
use App\Domains\Comments\Enums\RecordableEvents;
use App\Domains\Comments\Http\Resources\ActivityCommentResource;
use App\Domains\Shared\Data\ActivityObject;
use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Maize\Markable\Markable;
use Maize\Markable\Models\Like;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * Comment model representing user-generated comments and discussions.
 */
class Comment extends BaseDomainModel
{
    use LogsActivity;
    use Markable;

    protected function casts(): array
    {
        return [
            'comment' => 'json',
        ];
    }

    protected static $marks = [
        Like::class,
    ];

    protected static function booted(): void
    {
        static::saved(function (Comment $comment): void {
            $log = str($comment->commentable_type)->plural();
            $event = $comment->parent_id !== null ? 'ReplyAdded' : 'CommentAdded';
            $logNote = $comment->parent_id !== null ? 'Replied to a comment' : 'Posted a comment';

            $activity = activity($log);

            $activity = new ActivityObject(
                logName: $log,
                on: $comment->commentable,
                by: $comment->user->getKey(),
                event: RecordableEvents::tryFrom($event),
                description: $logNote,
                properties: [
                    'changes' => [
                        'comment' => ActivityCommentResource::make($comment),
                    ],
                ],
            );

            RecordActivity::dispatch($activity);
        });
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->useLogName(str($this->commentable_type)->plural())->logOnlyDirty()->dontSubmitEmptyLogs();
    }

    public function commentable(): MorphTo
    {
        return $this->morphTo();
    }

    public function replies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id')->whereNotNull('parent_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
