<?php

namespace App\Domains\Users\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RolesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->resource->name,
            // 'guard' => $this->guard_name,
            'permissions' => $this->resource->permissions->map(fn($p): array => [
                'name' => $p->name,
            ]),
        ];
    }
}
