<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use Parental\HasParent;

class PhotoEditor extends User
{
    use HasParent;

    protected static function seedPermissions(): array
    {
        return [
            Permission::VIEW_SUBMITTED_PITCHES,
            Permission::UPDATE_PITCH,
            Permission::CREATE_PITCH,
            Permission::DELETE_OWN_PITCH,
            Permission::REQUEST_EDITORIAL_ASSIST,
        ];
    }
}
