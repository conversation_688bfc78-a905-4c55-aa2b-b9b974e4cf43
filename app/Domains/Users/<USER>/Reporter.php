<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Parental\HasParent;

class Reporter extends User implements MustVerifyEmail
{
    use HasParent;

    protected static function seedPermissions(): array
    {
        return [
            Permission::CREATE_PITCH,
            Permission::SUBMIT_PITCH,
            Permission::VIEW_PITCH,
            Permission::ASSIGN_COLLABORATORS,
            Permission::VIEW_REPORTS,
            Permission::TRANSLATE_PITCH,
            Permission::REQUEST_EDITORIAL_ASSIST,
        ];
    }
}
