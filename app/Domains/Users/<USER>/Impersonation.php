<?php

namespace App\Domains\Users\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Facades\DB;
use Laravel\Sanctum\PersonalAccessToken;
use Lunarstorm\LaravelDDD\Models\DomainModel;

/**
 * Impersonation model tracking user impersonation events.
 */
class Impersonation extends DomainModel
{
    use HasFactory;

    protected $fillable = ['user_id', 'personal_access_token_id'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function impersonatedUser(): HasOneThrough
    {
        return $this->hasOneThrough(User::class, PersonalAccessToken::class, 'id', 'id', 'personal_access_token_id', 'tokenable_id');
    }

    public function personalAccessToken(): HasOne
    {
        return $this->hasOne(PersonalAccessToken::class, 'id', 'personal_access_token_id');
    }

    public function delete(): void
    {
        DB::transaction(function () {
            $this->impersonatedUser->deleteApiKeys();
            $this->personalAccessToken()->delete(); // Delete the associated token
            parent::delete(); // Call the parent delete method to delete the impersonation
        });
    }
}
