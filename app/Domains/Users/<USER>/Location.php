<?php

namespace App\Domains\Users\ValueObjects;

use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Shared\ValueObjects\Timezone;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;

class Location implements CastsAttributes
{
    public function __construct(
        public ?string $city = null,
        public ?string $country = null,
        public ?Timezone $timezone = null,
        public ?string $language = null,
    ) {}

    public function toArray(): array
    {
        return [
            'city' => $this->city,
            'country' => $this->country,
            'timezone' => $this->timezone,
        ];
    }

    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return new self(
            city: $attributes['location_city'] ?? null,
            country: $attributes['location'] ?? null,
            timezone: $attributes['timezone'] ? TimeHelper::timezone((int) $attributes['timezone']) : null,
            language: $attributes['local_language'] ?? null,
        );
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (! $value instanceof Location) {
            throw new InvalidArgumentException('Location must be an instance of Location');
        }

        return [
            'location_city' => $value->city,
            'location' => $value->country,
            'timezone' => $value->timezone->key,
            'local_language' => $value->language,
        ];
    }
}
