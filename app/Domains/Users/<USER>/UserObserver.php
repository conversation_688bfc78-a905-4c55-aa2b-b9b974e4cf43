<?php

namespace App\Domains\Users\Observers;

use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class UserObserver
{
    public function __construct(protected Request $request) {}

    public function creating(User $user): void
    {
        if ($this->request->has('roles')) {
            if (!$user->isDirty('type')) {
                $roles = data_get($this->request, 'roles', []) ?? $user::getRoleName();
                $user->type = $this->determineTypeFromClass($roles[0]);
            }
            
        }
        Cache::forget('users');
    }

    public function updating(User $user): void
    {
        if ($this->request->has('roles')) {
            if (!$user->isDirty('type')) {
                $roles = data_get($this->request, 'roles', []) ?? $user::getRoleName();
                $user->type = $this->determineTypeFromClass($roles[0]);
            }
            
        }
        Cache::forget('users');
    }

    public function saving(Model $user)
    {
        if ($this->request->has('roles')) {
            if (!$user->isDirty('type')) {
                $roles = data_get($this->request, 'roles', []) ?? $user::getRoleName();
                $user->type = $this->determineTypeFromClass($roles[0]);
            }
            
        }
        Cache::forget('users');
    }

    public function deleted(User $user): void
    {
        Cache::forget('users');
    }

    private function determineTypeFromClass(string $role = ''): string
    {
        $roleClass = str($role)->studly();

        $fullyQualifiedClassName = 'App\Domains\Users\Models\\' . $roleClass; // Prepend namespace

        // Check if class exists for the role.
        if (class_exists($fullyQualifiedClassName)) {
            return $fullyQualifiedClassName::getRoleName();

            // No class, check if it's a child type.
        } else {
            /** @phpstan-ignore-next-line */
            if ($key = collect(User::childTypes())->keys()->search($role)) {
                /** @phpstan-ignore-next-line */
                $childType = collect(User::childTypes())->flatten()[$key];

                return $childType::getRoleName();
            }
        }

        return 'user';
    }
}
