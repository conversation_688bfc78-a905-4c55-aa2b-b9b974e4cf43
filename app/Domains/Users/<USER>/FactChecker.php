<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Auth\MustVerifyEmail as AuthMustVerifyEmail;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Parental\HasParent;

class <PERSON>act<PERSON>hecker extends User implements MustVerifyEmail
{
    use HasParent;
    use AuthMustVerifyEmail;

    protected static function seedPermissions(): array
    {
        return [
            Permission::CREATE_PITCH,
            Permission::EDIT_EDITORIAL,
            Permission::REQUEST_EDITORIAL_ASSIST  
        ];
    }
}
