<?php

namespace App\Domains\Users\Notifications;

use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationUserResource;
use App\Domains\Shared\Data\NotificationAction;
use App\Domains\Shared\Data\NotificationDTO;
use App\Domains\Shared\Enums\MessageType;
use App\Domains\Users\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserVerified extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected MustVerifyEmail|User $user) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $verifyUrl = $this->user->frontendUserUrl();

        return (new MailMessage)
            ->subject('User registered in Lighthouse')
            ->line('Hello, a new user has completed their registration.')
            ->line('You need to finish setting up their profile, by adding a role')
            ->action('Complete User Setup', $verifyUrl);
    }

    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return (new BroadcastMessage($this->toArray($notifiable)))->onQueue('notifications');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return (new NotificationDTO(
            origin: 'lighthouse',
            user: $notifiable,
            title: 'User registered in Lighthouse',
            message: 'A new user has completed their registration. You need to finish setting up their profile, by adding a role',
            forWhat: NotificationUserResource::make($this->user)->resolve(),
            type: 'user_verified',
            action: new NotificationAction(
                severity: MessageType::Muted,
                buttonLabel: 'Complete User Setup',
                message: 'Complete User Setup',
            ),
        ))->toArray();
    }
}
