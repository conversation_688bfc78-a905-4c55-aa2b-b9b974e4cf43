<?php

namespace App\Domains\Users\Http\Controllers;

use App\Domains\Users\Models\Impersonation;
use App\Domains\Users\Models\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ImpersonationController extends Controller
{
    /**
     * Impersonate user.
     */
    public function impersonate(Request $request, User $user): JsonResponse
    {
        $impersonator = $request->user();
        $impersonatedUser = $user;

        // Check if persona user exists, can be impersonated and if the impersonator has the right to do so.
        abort_if(!$impersonatedUser, 404, 'User not found');
        abort_if(!$impersonatedUser->canBeImpersonated(), 403, 'This user is not authorized to be impersonated');
        abort_if(!$impersonator->canImpersonate(), 403, 'This user is not authorized to impersonate');

        // Create new token for persona
        $personaToken = $impersonatedUser->rollApiKey('IMPERSONATION token');

        // Save impersonator and persona token references
        $impersonation = new Impersonation();
        $impersonation->user_id = $impersonator->id;
        $impersonation->personal_access_token_id = $personaToken->accessToken->id;
        $impersonation->save();

        // Return persona token
        $response = [   
            'token' => $personaToken->plainTextToken,
        ];

        return response()->json($response, 200);
    }

    /**
     * Leave the impersonation
     */
    public function leave(Request $request): JsonResponse
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable|User $impersonatedUser */
        $impersonatedUser = $request->user();
        $bearerTokenId = str($request->bearerToken())->before('|');

        // Find the user impersonation and token
        $currentAccessToken = $impersonatedUser->tokens()->where('id', $bearerTokenId)->first();
        $impersonation = Impersonation::where('personal_access_token_id', $currentAccessToken->id)->latest()->first();
        $impersonator = $impersonation->user;
        
        $impersonatorToken = $impersonator->rollApiKey();
        
        // Logout impersonated user
        $impersonatedUser->deleteApiKeys();

        // Return new impersonator token
        $response = [
            'token' => $impersonatorToken->plainTextToken
        ];

        return response()->json($response, 200);
    }
}
