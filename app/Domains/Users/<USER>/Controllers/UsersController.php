<?php

namespace App\Domains\Users\Http\Controllers;

use App\Domains\Auth\Http\Resources\UsersResource;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Shared\Http\QueryFilters\FilterTopic;
use App\Domains\Shared\Http\QueryFilters\FilterVertical;
use App\Domains\Users\Http\QueryFilters\FilterIsOnline;
use App\Domains\Users\Http\QueryFilters\FilterRole;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Pipeline;

/**
 * @tags Application / Users
 */
class UsersController extends Controller
{
    /**
     * Get all users
     *
     * @query role string[] Roles to filter
     * @query topic string[] Topics to filter
     * @query online bool Is online
     */
    public function index(Request $request): JsonResource|Collection
    {

        $data = $request->validate([
            /** @query */
            'grouped' => ['sometimes', 'boolean'],
            'online' => ['sometimes', 'boolean'],
        ]);

        $users = Pipeline::send(
            User::query()
                ->active()
                ->verified()
                ->with(['roles'])
        )
            ->through([
                FilterRole::class,
                // FilterVertical::class,
                // FilterTopic::class,
                FilterIsOnline::class,
            ])->thenReturn()->get();

        if (data_get($data, 'grouped')) {
            $users = $users->groupBy('type')->map;
        }

        return UsersResource::collection($users);
    }
}
