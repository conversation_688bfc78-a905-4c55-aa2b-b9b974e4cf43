<?php

namespace App\Domains\Users\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Upload Profile Photos Controller
 *
 * @group Users
 *
 * @tags User Profile
 */
class UploadProfilePhotosController extends Controller
{
    /**
     * Upload a profile photo for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request  The request object containing the photo file.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the success message and the URL of the uploaded photo.
     */
    public function __invoke(Request $request): JsonResponse
    {
        // Validate that the request contains an image file.
        $request->validate([
            'photo' => 'required|image',
        ]);

        // Get the uploaded photo file from the request.
        $photo = $request->file('photo');

        // Update the photo for the authenticated user and get the URL of the updated photo.
        $url = $request->user()->updatePhoto($photo);

        // Return a JSON response with a success message and the URL of the uploaded photo.
        return response()->json(
            [
                'message' => 'Profile photo updated.',
                'photo_url' => $url,
            ],
            200,
        );
    }
}
