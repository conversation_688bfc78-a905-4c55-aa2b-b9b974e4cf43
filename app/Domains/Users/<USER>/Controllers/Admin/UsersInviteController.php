<?php

namespace App\Domains\Users\Http\Controllers\Admin;

use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Users\Mail\InviteUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class UsersInviteController extends Controller
{
    public function __invoke(Request $request)
    {
        $data = $request->validate([
            'email' => ['required', 'email'],
        ]);

        $email = data_get($data, 'email');

        // TODO: Send email
        Mail::to([$email])->send(new InviteUser($email));

        return response()->json(['status' => 'success']);
    }
}
