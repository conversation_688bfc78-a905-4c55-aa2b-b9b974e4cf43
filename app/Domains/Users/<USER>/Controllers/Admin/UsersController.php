<?php

namespace App\Domains\Users\Http\Controllers\Admin;

use App\Domains\Auth\Http\Resources\UsersResource;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Users\Events\UserCreated;
use App\Domains\Users\Http\Requests\CreateUserRequest;
use App\Domains\Users\Http\Requests\UpdateUserRequest;
use App\Domains\Users\Models\User;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Admin / Users
 */
class UsersController extends Controller
{
    /**
     * List all users.
     */
    public function index()
    {
        $users = User::with(['sessions', 'session', 'permissions', 'roles.permissions', 'tasks.taskable'])->get();

        return UsersResource::collection($users);
    }

    /**
     * Create a new user.
     */
    public function store(CreateUserRequest $request)
    {
        $this->db->beginTransaction();

        $user = User::query()->create([
            ...$request->payload(),
            'active' => false,
            'email_verified_at' => now(),
            'password' => bcrypt(str()->random(16)),
        ]);
        if ($request->hasFile('photo')) {
            $user->updatePhoto($request->file('photo'));
        }

        if ($request->has('roles')) {
            $user->syncRoles($request->roles);
        }

        UserCreated::dispatch($user);
        $this->db->commit();

        Cache::forget('users');
        return response()->json(['user' => UsersResource::make($user)]);
    }

    /**
     * Show a user.
     */
    public function show(User $user)
    {
        return response()->json(['user' => UsersResource::make($user)]);
    }

    /**
     * Update user.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $this->db->beginTransaction();
        if ($request->hasFile('photo')) {
            $user->updatePhoto($request->file('photo'));
        }

        if ($request->has('roles')) {
            if (! $user->is($request->user())) {
                $user->syncRoles($request->roles);
            }
        }

        $user->update($request->payload());
        $this->db->commit();

        Cache::forget('users');

        return response()->json(['user' => UsersResource::make($user->refresh())]);
    }

    /**
     * Delete user.
     */
    public function destroy(User $user): void
    {
        $user->delete();
        Cache::forget('users');
    }
}
