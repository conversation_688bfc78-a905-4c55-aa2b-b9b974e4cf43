<?php

namespace App\Domains\Users\Http\Controllers;

use App\Domains\Auth\Http\Resources\AuthenticatedUserResource;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @tags User Profile
 */
class PreferencesController extends Controller
{
    /**
     * Update the user's preferences.
     *
     * This endpoint updates the authenticated user's preferences.
     *
     * @param  Request  $request  The request object containing the updated preferences.
     * @return AuthenticatedUserResource The updated user resource.
     */
    public function update(Request $request): JsonResource
    {
        $data = $request->validate([
            'preferences.panels' => ['nullable', 'array'],
            'preferences.pitchStageOrder' => ['nullable', 'array'],
            'preferences.calendarTimezones' => ['sometimes', 'array'],
            'preferences.calendarSettings' => ['sometimes', 'array'],
        ]);

        // Get the authenticated user
        $user = $request->user();

        // Update the user's preferences with the request data
        $user->update($data);

        // Return the updated user resource
        return AuthenticatedUserResource::make($user->fresh(['session']));
    }
}
