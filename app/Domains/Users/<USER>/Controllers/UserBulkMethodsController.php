<?php

namespace App\Domains\Users\Http\Controllers;

use App\Domains\Users\Models\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * @tags Admin / Users
 */
class UserBulkMethodsController extends Controller
{
    /**
     * Deactivates multiple users.
     */
    public function deactivate(Request $request): JsonResponse
    {
        $userIds = $request->users;

        DB::transaction(function () use ($userIds): void {
            User::whereIn('id', $userIds)->update(['active' => false]);
        });

        return response()->json();
    }

    /**
     * Activates multiple users.
     */
    public function activate(Request $request): JsonResponse
    {
        $userIds = $request->users;

        DB::transaction(function () use ($userIds): void {
            User::whereIn('id', $userIds)->update(['active' => true]);
        });

        return response()->json();
    }
}
