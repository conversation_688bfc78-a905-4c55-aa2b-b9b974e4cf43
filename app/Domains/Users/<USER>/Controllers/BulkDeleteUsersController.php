<?php

namespace App\Domains\Users\Http\Controllers;

use App\Domains\Users\Models\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @tags Admin / Users
 */
class BulkDeleteUsersController extends Controller
{
    /**
     * Delete multiple users.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $userIds = $request->users;
        User::whereIn('id', $userIds)->delete();

        return response()->json(['deleted' => true]);
    }
}
