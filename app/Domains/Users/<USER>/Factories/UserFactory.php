<?php

namespace App\Domains\Users\Database\Factories;

use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Shared\Helpers\LanguageHelper;
use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\User;
use App\Domains\Verticals\Models\Vertical;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Domains\Users\Models\User>
 */
class UserFactory extends Factory
{
    protected $model = User::class;

    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        $created_at = now()->subWeeks(rand(0, 99));
        $updated_at = clone $created_at->subWeeks(rand(0, 20));

        return [
            'name' => fake()->name(),
            'email' => fake()->unique(true)->userName() . fake()->word() . '@' . fake()->randomElement(['globalpress.co', 'globalpressjournal.com', 'gp.co', 'gpj.com']),
            'email_verified_at' => now(),
            'password' => 'password',
            'photo_url' => fake()->imageUrl(),
            'active' => true,
            'job_title' => fake()->randomElement(['Editor', 'Reporter', 'Administrator', 'Copy Editor', 'Fact Checker']),
            'local_language' => fake()->randomElement(['en-EN']),
            'bio' => fake()->paragraph(),
            'location' => fake()->randomElement(LanguageHelper::countries()->pluck('code')),
            'timezone' => TimeHelper::timezone(336)->name,
            'slack_id' => 'D' . str(fake()->words(2, true))->replace(' ', '')->upper(),
            'created_at' => $created_at,
            'updated_at' => $updated_at,
        ];
    }

    public function setModel(string $model): static
    {
        $this->model = $model;

        return $this;
    }

    public function configure(): static
    {
        return $this
            ->afterMaking(function (User $user): void {})
            ->afterCreating(function (User $user): void {
                if (! $user->type) {
                    $user->type = 'user';
                    $user->save();
                }
            });
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn(array $attributes): array => [
            'email_verified_at' => null,
        ]);
    }

    public function verified(): static
    {
        return $this->state(fn(array $attributes): array => [
            'email_verified_at' => now(),
        ]);
    }

    public function active(): static
    {
        return $this->state(fn(array $attributes): array => [
            'active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn(array $attributes): array => [
            'active' => false,
        ]);
    }

    public function withRole(?string $role = null): static
    {
        return $this->state(function (array $attributes) use ($role): array {
            return [
                'type' => $role::getRoleName(),
            ];
        })->afterCreating(function (Model $user) use ($role): void {
            $userRole = Role::findOrCreate($role::getRoleName(), 'api');
            $user->assignRole($userRole);
            $user->save();

        });
    }

    public function withPitches(int $count, array $state = [], int $comments = 1): static
    {
        return $this->afterCreating(function (Model $user) use ($count, $state, $comments): void {
            $topic = Topic::inRandomOrder()->first();
            Pitch::factory($count)
                ->withCollaborators(rand(1, 5))
                ->withComments($comments)
                ->withUpdates(3)
                ->withStates()
                ->create([
                    ...$state,
                    'created_by' => $user->getKey(),
                    'topic_id' => $topic,
                    'vertical_id' => Vertical::where('topic_id', $topic->getKey())->inRandomOrder()->first()?->getKey() ?? 1,
                ]);
        });
    }
}
