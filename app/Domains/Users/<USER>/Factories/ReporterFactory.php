<?php

namespace App\Domains\Users\Database\Factories;

use App\Domains\Users\Models\Reporter;
use App\Domains\Users\Models\User;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class ReporterFactory extends UserFactory
{
    protected $model = Reporter::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $randomEmailId = Str::random(4);

        return [
            ...parent::definition(),
            'email' => sprintf('<EMAIL>', $randomEmailId),
            'type' => $this->model::getRoleName(),
        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (Reporter|User $reporter): void {
            // Assign the 'reporter' role to the newly created reporter
            $role = Role::findOrCreate('reporter', 'api');
            $reporter->type = 'reporter';
            $reporter->assignRole($role);
        });
    }
}
