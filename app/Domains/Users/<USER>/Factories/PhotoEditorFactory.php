<?php

namespace App\Domains\Users\Database\Factories;

use App\Domains\Users\Models\PhotoEditor;

class PhotoEditorFactory extends UserFactory
{
    protected $model = PhotoEditor::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ...parent::definition(),
            'type' => 'photo_editor',
        ];
    }
}
