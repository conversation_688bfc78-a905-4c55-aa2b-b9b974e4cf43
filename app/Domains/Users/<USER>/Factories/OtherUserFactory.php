<?php

namespace App\Domains\Users\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Spatie\Permission\Models\Role;

class OtherUserFactory extends UserFactory
{
    protected ?Role $role;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ...parent::definition(),
            'photo_url' => 'https://gpjs3bucket.s3.amazonaws.com/wp-content/uploads/2020/04/26122703/0024_Naki-150x150c.jpg',
        ];
    }

    public function asType(string $type): Factory
    {
        return $this->state(function (array $attributes) use ($type): array {
            $class = 'App\\Models\\Roles\\' . str($type)->replace('_', '')->title();
            if (class_exists($class)) {
                $this->model = $class;
            }

            return [
                'type' => $type,
            ];
        });
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function ($user): void {
            if ($this->role !== null) {
                $user->assignRole($this->role);
            } elseif ($user->type) {
                $role = Role::findOrCreate($user->type, 'api');
                $user->assignRole($role);
            }
        });
    }

    // public function withRole(string $role): static
    // {
    //     $this->role = Role::findOrCreate($role, 'api');

    //     return $this;
    // }
}
