<?php

namespace App\Domains\Users\Database\Factories;

use App\Domains\Users\Models\Translator;
use App\Domains\Users\Models\User;
use Spatie\Permission\Models\Role;

class TranslatorFactory extends UserFactory
{
    protected $model = Translator::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ...parent::definition(),
            'type' => 'translator',
        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (User $translator): void {
            // Assign the 'translator' role to the newly created translator
            $role = Role::findOrCreate('translator', 'api');
            $translator->assignRole($role);
        });
    }
}
