<?php

namespace App\Domains\Users\Database\Factories;

use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\User;

class EditorFactory extends UserFactory
{
    protected $model = Editor::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            ...parent::definition(),
        ];
    }

    /**
     * Configure the model factory.
     */
    // public function configure(): static
    // {
    //     return $this->afterCreating(function (Editor $editor) {
    //         // Assign the 'editor' role to the newly created editor
    //     });
    // }

    public function as(string $type): static
    {
        return $this->afterCreating(function (Editor|User $editor) use ($type): void {
            /** @phpstan-ignore-next-line */
            $types = $editor::childTypes();

            if (array_key_exists($type, $types)) {
                $editor->type = $types[$type]::getRoleName();
            } else {
                $editor->type = 'editor';
            }

            $editor->save();
        });
    }
}
