<?php

namespace App\Domains\Users\Database\Factories;

use App\Domains\Users\Models\AudienceLiaison;
use App\Domains\Users\Models\User;
use Spatie\Permission\Models\Role;

class AudienceLiaisonFactory extends UserFactory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    protected $model = AudienceLiaison::class;

    public function definition(): array
    {
        return [
            ...parent::definition(),
            // 'email_verified_at' => now(),

        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (AudienceLiaison|User $audienceLiaison): void {
            // Assign the 'audience_liaison' role to the newly created audience liaison
            $role = Role::findOrCreate('audience_liaison', 'api');
            $audienceLiaison->assignRole($role);
        });
    }
}
