<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Parental\HasParent;

class Translator extends User implements MustVerifyEmail
{
    use HasParent;

    protected static function seedPermissions(): array
    {
        return [
            Permission::TRANSLATE_PITCH,
            Permission::TRANSLATE_EDITORIAL,
        ];
    }
}
