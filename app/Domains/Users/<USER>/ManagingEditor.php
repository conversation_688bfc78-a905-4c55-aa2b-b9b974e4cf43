<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Parental\HasParent;

class ManagingEditor extends User implements MustVerifyEmail
{
    use HasParent;

    protected static function seedPermissions(): array
    {
        return [
            Permission::CREATE_PITCH,
            Permission::SUBMIT_PITCH,
            Permission::MOVE_PITCH,
            Permission::REJECT_PITCH,
            Permission::ASSIGN_COLLABORATORS,
            Permission::ESCALATE_EDITORIAL,
            Permission::ASSIGN_USERS,
            Permission::REQUEST_EDITORIAL_ASSIST,
        ];
    }
}
