<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Parental\HasParent;

class EditorialAdmin extends User implements MustVerifyEmail
{
    use HasParent;

    protected static function seedPermissions(): array
    {
        return [
            Permission::SUBMIT_PITCH,
            Permission::CREATE_PITCH,
            Permission::UPDATE_PITCH,
            Permission::VIEW_PITCHES,
            Permission::APPROVE_TRAVEL,
            Permission::REQUEST_EDITORIAL_ASSIST,
        ];   
    }
}
