<?php

namespace App\Domains\Users\Http\Requests;

use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Shared\Rules\CompanyEmail;
use App\Domains\Users\Models\User;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(
        #[CurrentUser]
        User $user,
    ): bool {
        return $user->can('update', $this->user);
    }

    protected function prepareForValidation() {}

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['nullable', 'string', 'max:255'],
            'email' => ['filled', 'email', Rule::unique('users', 'email')->ignore($this->user->id)],
            'photo' => ['nullable', 'file', Rule::imageFile()],
            'active' => ['nullable', 'boolean'],
            'bio' => ['nullable', 'string', 'max:1200'],
            'local_language' => ['nullable', 'string', 'max:255'],
            'job_title' => ['nullable', 'string', 'max:255'],
            'location' => ['nullable', 'string', 'max:255'],
            'timezone' => ['sometimes', 'string', Rule::in(TimeHelper::timezones()->pluck('name'))],
            'slack_id' => ['nullable', 'string', 'max:255'],
            'password' => ['nullable', 'confirmed'],
            'password_confirmation' => ['nullable', 'required_with:password'],
            'roles' => ['nullable', 'array', 'min:1', 'max:1'],
            'roles.*' => ['string', Rule::exists('roles', 'name')],
        ];
    }

    public function payload(): array
    {
        return collect($this->validated())->except('photo', 'roles')->toArray();
    }
}
