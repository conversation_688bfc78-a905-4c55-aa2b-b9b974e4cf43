<?php

namespace App\Domains\Users\Http\Requests;

use App\Domains\Shared\Helpers\LanguageHelper;
use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Shared\Rules\CompanyEmail;
use App\Domains\Users\Models\User;
use Illuminate\Container\Attributes\CurrentUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(
        #[CurrentUser]
        User $user,
    ): bool {

        return $user->can('create', User::class);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'email' => str($this->email)->lower()->toString(),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', $this->shouldCheckForCompanyEmail(), 'max:255', 'unique:users'],
            'bio' => ['sometimes', 'string', 'max:1200'],
            'photo' => ['sometimes', Rule::imageFile()],
            'job_title' => ['sometimes', 'string', 'max:255'],
            'local_language' => ['sometimes', 'string', 'max:255'],
            'location' => ['sometimes', 'string', 'max:255'],
            'slack_id' => ['sometimes', 'string', 'max:255'],
            'timezone' => ['sometimes', 'string', Rule::in(TimeHelper::timezones()->pluck('name'))],
            'active' => ['sometimes', 'boolean'],
            'primary_language' => ['sometimes', Rule::in(LanguageHelper::languageCodes()->keys())],
            'roles' => ['sometimes', 'array', 'min:1', 'max:1'],
            'roles.*' => ['string', Rule::exists('roles', 'name')],
        ];
    }

    public function payload(): array
    {
        $data = collect($this->validated())->except('photo', 'topic', 'roles');

        $data->put('email_verified_at', now());

        return $data->toArray();
    }

    protected function shouldCheckForCompanyEmail(): ?CompanyEmail
    {

        if($this->user()->isAdmin()) {
            return null;
        }

        return new CompanyEmail;
    }
}
