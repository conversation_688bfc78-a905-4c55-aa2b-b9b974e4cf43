<?php

namespace App\Domains\Users\Http\QueryFilters;

use App\Domains\Shared\Http\QueryFilters\FilterRequest;
use Closure;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;

class FilterRole extends FilterRequest
{
    public function handle(Builder $query, Closure $next): Builder
    {
        $rolesFromRequest = is_array($this->request->role) ?
            collect($this->request->role)
            : str($this->request->role)->explode(',')->filter();

        $rolesFromRequest = [
            'roles' => $rolesFromRequest->toArray(),
        ];

        $validatedRoles = Validator::make($rolesFromRequest, [
            /** @query */
            'roles' => ['array'],
            'roles.*' => ['exists:roles,name'],
        ])->validate();

        $roles = data_get($validatedRoles, 'roles');

        $query->when(! empty($roles), function ($query) use ($roles): void {
            $query->role($roles)->orWhereIn('type', $roles);
        });

        return $next($query);
    }
}
