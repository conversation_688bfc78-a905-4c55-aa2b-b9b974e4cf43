<?php

namespace App\Domains\Users\Http\QueryFilters;

use App\Domains\Shared\Http\QueryFilters\FilterRequest;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class FilterIsOnline extends FilterRequest
{
    public function handle(Builder $query, Closure $next): Builder
    {
        $onlineUsers = (bool) $this->request->is_online;

        $query->when($onlineUsers, function ($query): void {
            $query->whereNotNull('api_token')->has('session');
        });

        return $next($query);
    }
}
