<?php

namespace App\Domains\Users\Policies;

use App\Domains\Users\Models\User;

class UserPolicy
{
    /**
     * Create a new policy instance.
     */
    public function create(User $user): bool
    {
        return $user->getAllPermissions()->pluck('name')->contains('create users');
    }

    /**
     * Create a new policy instance.
     */
    public function update(User $user): bool
    {
        return $user->getAllPermissions()->pluck('name')->contains('update users');
    }

    public function impersonate(User $user): bool
    {
        return $user->getAllPermissions()->pluck('name')->contains('impersonate');
    }

    public function be_impersonated(User $user): bool
    {
        return $user->getAllPermissions()->pluck('name')->contains('be impersonated');
    }
}
