<?php

namespace App\Domains\Users\Listeners;

use App\Domains\Users\Models\EditorialAdmin;
use App\Domains\Users\Notifications\UserVerified;
use Illuminate\Auth\Events\Verified;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class HandleVerifiedUser implements ShouldQueue
{
    use InteractsWithQueue;
    
    /**
     * Handle the event.
     */
    public function handle(Verified $event): void
    {
        EditorialAdmin::query()->each(function (EditorialAdmin $admin) use ($event): void {
            $admin->notify(new UserVerified($event->user));
        });
    }
}
