<?php

namespace App\Domains\Users\Models;

use App\Domains\Shared\Enums\Permission;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Database\Factories\EditorFactory;
use App\Domains\Verticals\Models\Vertical;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Parental\HasParent;

class Editor extends User
{
    use HasParent;

    protected $hasChildren = false;

    protected static function seedPermissions(): array
    {
        return [
            Permission::SUBMIT_PITCH,
            Permission::CREATE_PITCH,
            Permission::DELETE_OWN_PITCH,
            Permission::UPDATE_PITCH,
            Permission::VIEW_SUBMITTED_PITCHES,
            Permission::TRANSLATE_PITCH,
            Permission::ASSIGN_USERS,
            Permission::EDIT_EDITORIAL,
            Permission::REQUEST_EDITORIAL_ASSIST
        ];
    }

    public function assignTopic(Topic $topic): self
    {
        $this->topic()
            ->save($topic)
            ->saveQuietly();

        return $this;
    }

    public function assignVertical(Vertical $vertical): self
    {
        $this->vertical()
            ->save($vertical)
            ->saveQuietly();

        return $this;
    }

    public function topic(): HasOne
    {
        return $this->hasOne(Topic::class, 'assigned_editor_id', 'id');
    }

    public function vertical(): HasOne
    {
        return $this->hasOne(Vertical::class, 'assigned_editor_id', 'id');
    }

    public function scopeVerticalEditor(Builder $query): Builder
    {
        return $query->role('vertical_editor');
    }

    public function scopeTopicEditor(Builder $query): Builder
    {
        return $query->role('topic_editor');
    }

    public function allEditors(Builder $query): Builder
    {
        return $query->where('type', 'like', '%editor%');
    }

    protected static function newFactory(): EditorFactory
    {
        return EditorFactory::new();
    }
}
