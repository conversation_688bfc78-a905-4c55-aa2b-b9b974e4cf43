<?php

namespace App\Domains\Application\Actions;

use App\Domains\Betterflow\V1\Editorials\Notifications\DefaultNotification;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\JsonResponse;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class CreateNotification
{
    use AsAction;

    public function handle(ActionRequest $request)
    {
        $userOrUsersToNotify = $request->users;

        if (! is_array($userOrUsersToNotify)) {
            $userOrUsersToNotify = [$userOrUsersToNotify];
        }

        $models = Relation::morphMap();

        $notificaiton = new DefaultNotification(
            new ($models[$request->subject_type])(),
            $request->title,
            $request->message,
            $request->type,
        );

        return collect($userOrUsersToNotify)
            ->map(fn($userId) => User::query()->findOrFail($userId))
            ->each(function ($user) use ($notificaiton): void {
                $user->notify($notificaiton);
            });
    }

    public function asController(ActionRequest $request)
    {
        return $this->handle($request);
    }

    public function jsonResponse(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Notification/s created successfully',
        ]);
    }
}
