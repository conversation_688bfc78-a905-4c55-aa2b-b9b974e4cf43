<?php

namespace App\Domains\Spaces\Models;

use App\Domains\Shared\Models\BaseDomainModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\Factory;


/**
 * Setting model representing configurable system or space parameters.
 */
class Setting extends BaseDomainModel
{
    protected $fillable = [
        'key',
        'value',
    ];

    public function space(): BelongsTo
    {
        return $this->belongsTo(Space::class);
    }

    public static function newFactory(): Factory|null
    {
        return null;
    }
}
