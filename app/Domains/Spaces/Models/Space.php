<?php

namespace App\Domains\Spaces\Models;

use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Lunarstorm\LaravelDDD\Factories\HasDomainFactory;

/**
 * Space model representing collaborative workspaces or environments.
 */
class Space extends BaseDomainModel
{
    use HasDomainFactory;

    protected $fillable = [
        'name',
        'description',
        'permissions',
    ];

    public function settings(): HasMany
    {
        return $this->hasMany(Setting::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'current_space_id');
    }
}
