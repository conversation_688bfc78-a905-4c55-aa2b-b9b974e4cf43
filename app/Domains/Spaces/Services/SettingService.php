<?php

namespace App\Domains\Spaces\Services;

use App\Domains\Spaces\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingService
{
    public function create(string $key, string $value): \App\Domains\Spaces\Models\Setting
    {
        $setting = new Setting();
        $setting->key = $key;
        $setting->value = $value;
        $setting->save();

        // Cache the setting for 1 hour
        Cache::forever('setting:' . $key, $value);

        return $setting;
    }

    public function get(string $key)
    {
        $cachedValue = Cache::get('setting:' . $key);
        if ($cachedValue) {
            return $cachedValue;
        }

        $setting = Setting::where('key', $key)->first();
        if (! $setting) {
            return null;
        }

        return $setting->value;
    }
}
