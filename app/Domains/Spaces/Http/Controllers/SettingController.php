<?php

namespace App\Domains\Spaces\Http\Controllers;

use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Spaces\Models\Setting;
use App\Domains\Spaces\Services\SettingService;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    public function __construct(private SettingService $settingService) {}

    public function index()
    {
        $settings = Setting::all();

        return response()->json($settings);
    }

    public function store(Request $request)
    {
        $setting = $this->settingService->create($request->input('key'), $request->input('value'));

        return response()->json([
            'setting' => $setting,
            'message' => 'Setting created successfully!',
        ], 201);
    }

    public function show(Setting $setting)
    {
        return response()->json($setting);
    }
}
