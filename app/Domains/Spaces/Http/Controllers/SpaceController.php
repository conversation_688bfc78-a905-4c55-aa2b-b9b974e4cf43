<?php

namespace App\Domains\Spaces\Http\Controllers;

use App\Domains\Shared\Exceptions\IllegalActionException;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Spaces\Models\Space;
use App\Domains\Spaces\Resources\Space as ResourcesSpace;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SpaceController extends Controller
{
    public function index()
    {
        $spaces = Space::query()->with(['users'])->get();

        return ResourcesSpace::collection($spaces);
    }

    public function store(Request $request)
    {
        $space = new Space();
        $space->name = $request->input('name');
        $space->description = $request->input('description');
        $space->permissions = $request->input('permissions');
        $space->save();

        // Create a setting for the space

        return response()->json([
            'space' => $space,
            'message' => 'Space created successfully!',
        ], 201);
    }

    public function show(Space $space)
    {
        return response()->json($space);
    }

    public function switch(Request $request, Space $space): JsonResponse
    {
        $user = $request->user();

        // Check if the user has access to this space
        // if (! $user->spaces->contains($space)) {
        //     return response()->json(['message' => 'You do not have access to this space.'], 403);
        // }

        throw_if($user->current_space_id === $space->getKey(), new IllegalActionException('User is already logged into this space'));

        // Set the current space for the user
        $user->current_space_id = $space->id;
        $user->save();

        return response()->json([
            'message' => 'Successfully switched to space.',
            'space' => $space,
        ]);
    }

    public function current(Request $request): JsonResponse
    {
        $user = $request->user();
        $currentSpace = $user->currentSpace;

        abort_if(! $currentSpace, 404, 'The user is not logged into any space');

        return response()->json([
            'space' => $currentSpace,
        ]);
    }
}
