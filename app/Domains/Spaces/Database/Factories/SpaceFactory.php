<?php

namespace App\Domains\Spaces\Database\Factories;

use App\Domains\Spaces\Models\Space;
use Illuminate\Database\Eloquent\Factories\Factory;

class SpaceFactory extends Factory
{
    protected $model = Space::class;

    public function definition()
    {
        return [
            'name' => fake()->words(3, true),
            'description' => fake()->words(3, true),
            'permissions' => '[*]',
        ];
    }
}
