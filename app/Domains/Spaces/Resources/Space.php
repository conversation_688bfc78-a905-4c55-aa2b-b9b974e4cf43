<?php

namespace App\Domains\Spaces\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Space extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->resource->name,
            'slug' => $this->resource->slug,
            'permissions' => $this->resource->permissions,
            'description' => $this->resource->description,
            'users' => SimpleUserResource::collection($this->whenLoaded('users')),
        ];
    }
}
