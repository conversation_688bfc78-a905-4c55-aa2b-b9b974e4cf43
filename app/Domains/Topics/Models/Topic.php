<?php

namespace App\Domains\Topics\Models;

use App\Domains\Shared\Models\BaseDomainModel;
use App\Domains\Shared\Models\BaseModel;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\User;
use App\Domains\Verticals\Models\Vertical;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Topic model representing content subject areas and themes.
 */
class Topic extends BaseDomainModel
{
    protected $fillable = [
        'name',
        'description',
        'active',
        'color',
        'slug',
        'active',
        'icon',
        'assigned_editor_id',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function verticals(): HasMany
    {
        return $this->hasMany(Vertical::class, 'topic_id', 'id');
    }

    public function editor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_editor_id', 'id');
    }

    public function assignEditor(Editor $editor): self
    {
        $this->editor()->associate($editor)->save();

        return $this;
    }
}
