<?php

namespace App\Domains\Topics\Http\Requests;

use App\Domains\Users\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTopicRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole('admin', 'super_admin');
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string', 'max:300'],
            'color' => ['sometimes', 'string', 'min:7', 'max:7', 'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/'],
            'active' => ['sometimes', 'boolean'],
            'icon' => ['sometimes', 'string'],
            'assigned_editor_id' => ['sometimes', 'numeric', 'exists:users,id'],
        ];
    }

    protected function prepareForValidation()
    {
        if ($assigned_editor = $this->input('assigned_editor')) {
            $this->merge([
                'assigned_editor_id' => $assigned_editor,
            ]);
        }
    }

    public function payload(): array
    {
        return $this->validated();
    }
}
