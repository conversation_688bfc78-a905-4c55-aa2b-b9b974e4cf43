<?php

namespace App\Domains\Topics\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;


class StoreTopicRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole('admin', 'super_admin');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:255'],
            'slug' => ['required', 'string', 'max:255', 'unique:topics,slug'],
            'color' => ['required', 'string', 'max:7', 'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/'],
            'active' => ['sometimes', 'boolean'],
            'icon' => ['sometimes', 'string'],
            'assigned_editor_id' => ['sometimes', 'numeric', 'exists:users,id'],
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($assigned_editor = $this->input('assigned_editor')) {
            $this->merge([
                'assigned_editor_id' => $assigned_editor,
            ]);
        }
    }

    public function payload(): array
    {
        return $this->validated();
    }
}
