<?php

namespace App\Domains\Topics\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Admin\Crud\Http\Resources\CrudResource;
use Illuminate\Http\Resources\Json\JsonResource;

class TopicResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            ...CrudResource::make($this)->toArray($request),
            'assigned_editor' => SimpleUserResource::make($this->whenLoaded('editor')),
            'verticals' => CrudResource::collection($this->whenLoaded('verticals')),
        ];
    }
}
