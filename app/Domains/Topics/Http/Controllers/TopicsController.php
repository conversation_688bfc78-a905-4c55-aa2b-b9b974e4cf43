<?php

namespace App\Domains\Topics\Http\Controllers;

use App\Domains\Topics\Http\Requests\StoreTopicRequest;
use App\Domains\Topics\Http\Requests\UpdateTopicRequest;
use App\Domains\Topics\Http\Resources\TopicResource;
use App\Domains\Topics\Models\Topic;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Admin / Topics
 */
class TopicsController extends Controller
{
    /**
     * Display a listing of topics.
     */
    public function index()
    {
        return TopicResource::collection(Topic::with('editor', 'verticals')->orderBy('id')->get());
    }

    /**
     * Store a topic.
     */
    public function store(StoreTopicRequest $request): \App\Domains\Topics\Http\Resources\TopicResource
    {
        $topic = Topic::create($request->payload());

        Cache::forget('topics');

        return new TopicResource($topic);
    }

    /**
     * Display topic.
     */
    public function show(Topic $topic): \App\Domains\Topics\Http\Resources\TopicResource
    {
        return new TopicResource($topic->loadMissing('editor', 'verticals'));
    }

    /**
     * Update topic.
     */
    public function update(UpdateTopicRequest $request, Topic $topic): \App\Domains\Topics\Http\Resources\TopicResource
    {
        $topic->update($request->payload());

        Cache::forget('topics');

        return new TopicResource($topic);
    }

    /**
     * Delete topic.
     */
    public function destroy(Topic $topic)
    {
        $topic->delete();

        Cache::forget('topics');

        return response()->noContent();
    }
}
