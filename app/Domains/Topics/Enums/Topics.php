<?php

namespace App\Domains\Topics\Enums;

use App\Domains\Topics\Models\Topic;
use Illuminate\Database\Eloquent\Collection;

enum Topics: string
{
    case Civil_Liberties = 'civil-liberties';
    case Global_Health = 'global-health';
    case Environment = 'environment';

    public function label(): string
    {
        return match ($this) {
            self::Civil_Liberties => 'Civil Liberties',
            self::Global_Health => 'Global Health',
            self::Environment => 'Environment',
        };
    }

    public function slug(): string
    {
        return match ($this) {
            self::Civil_Liberties => 'civil-liberties',
            self::Global_Health => 'global-health',
            self::Environment => 'environment',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::Civil_Liberties => 'Civil liberties description',
            self::Global_Health => 'Global health description',
            self::Environment => 'Environment description',
        };
    }

    public function topic(): Topic
    {
        return Topic::where('slug', $this->slug())->first();
    }

    public function verticals(): Collection
    {
        return $this->topic()->verticals;
    }

    public function color(): string
    {

        return match ($this) {
            self::Civil_Liberties => '#ccfbf1',
            self::Global_Health => '#FDF6B2',
            self::Environment => '#ede9fe',
        };
    }
}
