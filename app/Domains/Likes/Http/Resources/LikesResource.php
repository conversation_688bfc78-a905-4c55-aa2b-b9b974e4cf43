<?php

namespace App\Domains\Likes\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $like = $this->resource->loadMissing(['user']);

        return [
            'user' => SimpleUserResource::make($like->user),
            'metadata' => $like->metadata ?? [],
        ];
    }
}
