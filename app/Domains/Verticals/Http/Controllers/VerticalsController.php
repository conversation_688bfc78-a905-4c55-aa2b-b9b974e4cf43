<?php

namespace App\Domains\Verticals\Http\Controllers;

use App\Domains\Verticals\Actions\CreateVertical;
use App\Domains\Verticals\Data\CreateVerticalData;
use App\Domains\Verticals\Http\Requests\StoreVerticalRequest;
use App\Domains\Verticals\Http\Requests\UpdateVerticalsRequest;
use App\Domains\Verticals\Http\Resources\VerticalsResource;
use App\Domains\Verticals\Models\Vertical;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

/**
 * @tags Admin / Verticals
 */
class VerticalsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): AnonymousResourceCollection
    {
        return VerticalsResource::collection(Vertical::query()->with('topic')->orderBy('id')->get());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVerticalRequest $request, CreateVertical $createVertical): VerticalsResource
    {
        $data = $request->payload();

        $vertical = $createVertical->execute(
            new CreateVerticalData($data, data_get($data, 'topic_id')),
        );

        Cache::forget('verticals');

        return VerticalsResource::make($vertical);
    }

    /**
     * Display the specified resource.
     */
    public function show(Vertical $vertical): VerticalsResource
    {
        return VerticalsResource::make($vertical->loadMissing('topic', 'editor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVerticalsRequest $request, Vertical $vertical): VerticalsResource
    {
        $vertical->update($request->payload());
        Cache::forget('verticals');

        return VerticalsResource::make($vertical->fresh('editor'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vertical $vertical): JsonResponse
    {
        $vertical->delete();
        Cache::forget('verticals');

        return response()->json([], Response::HTTP_NO_CONTENT);
    }
}
