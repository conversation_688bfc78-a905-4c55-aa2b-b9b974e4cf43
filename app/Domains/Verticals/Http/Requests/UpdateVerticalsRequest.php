<?php

namespace App\Domains\Verticals\Http\Requests;

use App\Domains\Users\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateVerticalsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole('admin', 'super_admin');
    }

    protected function prepareForValidation()
    {
        if ($assigned_editor = $this->input('assigned_editor')) {
            $this->merge([
                'assigned_editor_id' => $assigned_editor,
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string', 'max:255'],
            // 'slug' => ['sometimes', 'string', 'max:255', 'unique:verticals,slug'],
            'color' => ['sometimes', 'string', 'max:7', 'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/'],
            'active' => ['sometimes', 'boolean'],
            'icon' => ['sometimes', 'string'],
            'assigned_editor_id' => ['sometimes', 'exists:users,id'],
        ];
    }

    /**
     * Get the validated data from the request.
     */
    public function payload(): array
    {
        return $this->validated();
    }
}
