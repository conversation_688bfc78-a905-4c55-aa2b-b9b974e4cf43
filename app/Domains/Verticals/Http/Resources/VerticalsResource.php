<?php

namespace App\Domains\Verticals\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Topics\Http\Resources\TopicResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VerticalsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->resource->loadMissing('editor');

        return [
            'id' => $this->resource->getKey(),
            'name' => $this->resource->name,
            'description' => $this->resource->description,
            'slug' => $this->resource->slug,
            'color' => $this->resource->color,
            'active' => $this->resource->active,
            'icon' => $this->resource->icon,
            'topic' => TopicResource::make($this->whenLoaded('topic')),
            'assigned_editor' => SimpleUserResource::make($this->whenLoaded('editor')),
        ];
    }
}
