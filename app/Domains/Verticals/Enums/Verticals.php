<?php

namespace App\Domains\Verticals\Enums;

use App\Domains\Verticals\Models\Vertical;
use Illuminate\Database\Eloquent\Model;

enum Verticals: string
{
    // Civil Liberties
    case Shifiting_Democracies = 'shifting-democracies';
    case Stateless_People = 'stateless-people';

    // Global Health
    case Access_To_Basic_Needs = 'access-to-basic-needs';
    case The_Mass_Exodus = 'the-mass-exodus';

    // Environment
    case Climate_Mitigation = 'climate-mitigation';
    case Environmental_Justice = 'environmental-justice';

    public function label(): string
    {
        return match ($this) {
            // Civil Liberties
            self::Shifiting_Democracies => 'Shifting Democracies',
            self::Stateless_People => 'Stateless People',

            // Global Health
            self::Access_To_Basic_Needs => 'Access to Basic Needs',
            self::The_Mass_Exodus => 'The Mass Exodus',

            // Environment
            self::Climate_Mitigation => 'Climate Mitigation',
            self::Environmental_Justice => 'Environmental Justice',
        };
    }

    public function slug(): string
    {
        return match ($this) {
            // Civil Liberties
            self::Shifiting_Democracies => 'shifting-democracies',
            self::Stateless_People => 'stateless-people',

            // Global Health
            self::Access_To_Basic_Needs => 'access-to-basic-needs',
            self::The_Mass_Exodus => 'the-mass-exodus',

            // Environment
            self::Climate_Mitigation => 'climate-mitigation',
            self::Environmental_Justice => 'environmental-justice',
        };
    }

    public function description(): string
    {
        return match ($this) {
            // Civil Liberties
            self::Shifiting_Democracies => 'Shifting Democracies description',
            self::Stateless_People => 'Stateless People description',

            // Global Health
            self::Access_To_Basic_Needs => 'Access to Basic Needs description',
            self::The_Mass_Exodus => 'The Mass Exodus description',

            // Environment
            self::Climate_Mitigation => 'Climate Mitigation description',
            self::Environmental_Justice => 'Environmental Justice description',
        };
    }

    public function topic(): ?Model
    {
        return Vertical::whereSlug($this->slug())->first()?->topic;
    }
}
