<?php

namespace App\Domains\Verticals\Actions;

use App\Domains\Verticals\Data\CreateVerticalData;
use App\Domains\Verticals\Models\Vertical;
use Illuminate\Database\DatabaseManager;

class CreateVertical
{
    public function __construct(
        protected DatabaseManager $db,
    ) {}

    public function execute(CreateVerticalData $data): Vertical
    {
        return $this->db->transaction(function () use ($data) {
            
            $vertical = new Vertical();
            
            $vertical->fill($data->data);

            $vertical->topic_id = $data->topicId;

            $vertical->save();

            $vertical->triggerEvent('topicUpdated');

            return $vertical;
        });
    }
}
