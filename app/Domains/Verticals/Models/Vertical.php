<?php

namespace App\Domains\Verticals\Models;

use App\Domains\Betterflow\V1\Admin\Crud\Models\CrudModel;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Vertical extends CrudModel
{
    protected $fillable = [
        'name',
        'description',
        'active',
        'color',
        'slug',
        'active',
        'icon',
        'topic_id',
        'assigned_editor_id',
    ];

    protected function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    public function topic(): BelongsTo
    {
        return $this->belongsTo(Topic::class);
    }

    public function assignTopic(int $topicId): self
    {
        $this->topic_id = $topicId;
        $this->save();

        return $this;
    }

    public function editor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_editor_id');
    }

    public function assignEditor(Editor|User $editor): self
    {
        $this->editor()->associate($editor)->save();

        return $this;
    }

    // Morph map?

    // public function pitches(): MorphToMany
    // {
    //     return $this->morphedByMany(Pitch::class, 'verticalable', 'verticalables');
    // }

    // public function users(): MorphToMany
    // {
    //     return $this->morphedByMany(User::class, 'verticalable', 'verticalables');
    // }
}
