<?php

namespace App\Domains\Auth\Actions\Fortify;

use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Shared\Rules\CompanyEmail;
use App\Domains\Users\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /* @phpstan-ignore-next-line */
    public function create(array $input): User
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(User::class, 'email'),
                new CompanyEmail(),
            ],
            'password' => $this->passwordRules(),
        ])->validate();

        $timezone = data_get($input, 'timezone.name', null);
        $timezone = $timezone ? TimeHelper::timezone($timezone) : TimeHelper::defaultTimezone();
        

        return User::query()->create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
            'timezone' => $timezone->key,
            'type' => 'user',
        ]);
    }
}
