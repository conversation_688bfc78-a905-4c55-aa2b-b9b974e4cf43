<?php

namespace App\Domains\Auth\Actions\Fortify;

use App\Domains\Shared\Helpers\LanguageHelper;
use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Users\Models\User;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    public function update(Authenticatable $user, array $input): void
    {
        Validator::make($input, [
            'name' => ['nullable', 'string', 'max:255'],
            'photo' => ['nullable', 'file', Rule::imageFile()],
            'email' => [
                'sometimes',
                'string',
                'email:rfc,spoof,filter',
                'max:255',
                Rule::unique('users', 'email')->ignore($user->id),
            ],
            'bio' => ['nullable', 'string'],
            'timezone' => ['nullable', 'string', Rule::in(TimeHelper::timezones()->pluck('name'))],
            'location' => ['nullable', 'string'],
            'local_language' => ['nullable', 'string', Rule::in(LanguageHelper::languageCodes()->keys())],
            'slack_id' => ['nullable', 'string'],
            'job_title' => ['nullable', 'string'],
        ], [
            'type.in' => 'The selected user type is invalid.',
        ])->validateWithBag('updateProfileInformation');

        if ($input['timezone']) {
            $user->timezone = TimeHelper::timezone($input['timezone']);
        }

        if (
            $input['email'] !== $user->email &&
            $user instanceof MustVerifyEmail
        ) {
            $this->updateVerifiedUser($user, $input);
        } else {
            /** @var User $user */
            if ($photo = data_get($input, 'photo')) {
                /** @var UploadedFile $photo */
                $user->updatePhoto($photo);
            }

            $user->forceFill([
                'name' => data_get($input, 'name', $user->name),
                'email' => data_get($input, 'email', $user->email),
                'bio' => data_get($input, 'bio', $user->bio),
                'location' => data_get($input, 'location', $user->location),
                'local_language' => data_get($input, 'local_language', $user->local_language),
                'slack_id' => data_get($input, 'slack_id', $user->slack_id),
                'job_title' => data_get($input, 'job_title', $user->job_title),
            ])->save();
        }
    }

    /**
     * Update the given verified user's profile information.
     *
     * @param  array<string, string>  $input
     */
    protected function updateVerifiedUser(MustVerifyEmail|User $user, array $input): void
    {
        $user->forceFill([
            'name' => data_get($input, 'name', $user->name),
            'email' => data_get($input, 'email', $user->email),
            'email_verified_at' => null,
        ])->save();

        $user->sendEmailVerificationNotification();
    }
}
