<?php

namespace App\Domains\Auth\Http\Middleware;

use Illuminate\Contracts\Auth\Factory as AuthContract;

use Illuminate\Contracts\Auth\Middleware\AuthenticatesRequests;

class AuthenticateCheck implements AuthenticatesRequests
{   
    public function __construct(protected AuthContract $auth)
    {}  

    public function handle($request, \Closure $next, ...$guards)
    {
        
        return $this->authenticate($request, $guards, $next);
    }

    protected function authenticate($request, array $guards, \Closure $next)
    {
        if (empty($guards)) {
            $guards = ['null'];
        }

        // Loop through each guard and check if the user is authenticated
        foreach ($guards as $guard) {
            if ($this->auth->guard($guard)->check()) {
                // Set the user pn the request 
                $request->setUserResolver(function () use ($guard) {
                    return $this->auth->guard($guard)->user();
                });
                return $next($request);
            }
        }

        return $this->unauthenticated($request, $guards);
    }

    protected function unauthenticated($request, array $guards)
    {
        return response()->json([
            'authenticated' => false,
        ], 401);
    }
}
