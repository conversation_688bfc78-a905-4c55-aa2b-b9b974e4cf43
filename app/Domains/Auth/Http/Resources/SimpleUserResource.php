<?php

namespace App\Domains\Auth\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimpleUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->name,
            'email' => $this->email,
            'photo_url' => $this->resource->getPhotoUrl(),
            'roles' => $this->loadMissing('roles')->getRoleNames(),
            'has_session' => $this->session ?? false,
            'job_title' => $this->job_title,
            ...$this->additional
        ];
    }
}
