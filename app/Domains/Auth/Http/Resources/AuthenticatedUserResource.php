<?php

namespace App\Domains\Auth\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuthenticatedUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $userResource = UsersResource::make($this->resource)->toArray($request);

        return [
            ...$userResource,
            'roles' => $this->resource->getRoleNames(),
            /**
             * @var array{id: int, can: string, guard: string}
             */
            'permissions' => $this->resource->getAllPermissions()->map(fn($p): array => [
                'id' => $p->id,
                'can' => $p->name,
                'guard' => $p->guard_name,
            ]),
            /** @var array{panels: array{name: string}[], pitchStageOrder: array{id: int, slug: string}[]} */
            'preferences' => $this->resource->preferences ?? [],
            /** @var bool */
            'two_factor_enabled' => $this->resource->two_factor_enabled,
            'two_factor' => $this->when($this->resource->two_factor_enabled, [
                'recovery_codes' => $this->resource->recovery_codes,
                'qr_code' => $this->resource->qr_code,
            ]),
        ];
    }
}
