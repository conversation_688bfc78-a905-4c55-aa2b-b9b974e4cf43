<?php

namespace App\Domains\Auth\Http\Resources;

use App\Domains\Betterflow\V1\Shared\Tasks\Http\Resources\TaskResource;
use App\Domains\Users\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UsersResource extends JsonResource
{
    /** @return array<string,mixed> */
    public function toArray(Request $request): array
    {

        /** @var User $user */
        $user = $this->resource->loadMissing(['roles.permissions', 'permissions']);

        return [
            'id' => $user->getKey(),
            /* @var bool */
            'active' => $user->isActive(),
            'bio' => $user->bio,
            'email' => $user->email,
            /* @var bool */
            'email_verified' => $user->email_verified_at !== null,
            /** @var array{id: int, user_id: int, ip_address: string, user_agent: string, payload: string, last_activity: \Carbon\CarbonImmutable} */
            'has_session' => $user->session ?? false,
            'job_title' => $user->job_title,
            'local_language' => $user->local_language,
            'location' => $user->location,
            'location_city' => $user->location_city,
            'name' => $user->name,
            'type' => $this->when(! app()->isProduction(), $user->type),
            'roles' => $user->roles->map(fn($r) => $r->name),
            'permissions' => $user->getAllPermissions()->map(fn($p): array => [
                'id' => $p->id,
                'can' => $p->name,
                'guard' => $p->guard_name,
            ]),
            'photo_url' => $user->getPhotoUrl(),
            'slack_id' => $user->slack_id,
            /** @var array{id: int, name: string} */
            'timezone' => $user->timezone_readable,
            'tasks' => TaskResource::collection($this->whenLoaded('tasks')),
        ];
    }
}
