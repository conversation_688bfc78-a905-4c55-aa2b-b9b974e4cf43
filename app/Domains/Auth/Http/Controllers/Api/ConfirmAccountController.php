<?php

namespace App\Domains\Auth\Http\Controllers\Api;

use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Users\Events\AccountConfirmed;
use App\Domains\Users\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class ConfirmAccountController extends Controller
{
    public function __invoke(Request $request, string $id): JsonResponse
    {
        $user = User::findOrFail($id);

        $request->validate([
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user->forceFill([
            'password' => Hash::make($request->password),
            'active' => true,
        ]);

        $user->save();

        event(new AccountConfirmed($user));

        return response()->json([
            'message' => 'Account confirmed, you can now login.',
        ]);
    }
}
