<?php

namespace App\Domains\Auth\Http\Controllers\Api;

use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
class AuthCheckController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {   
        $data = [
            'authenticated' => true,
        ];

        if($request->user() && $request->user()->hasImpersonations()) {
            $data['authenticated'] = true;
            $data['is_impersonated'] = true;
        }

        return response()->json($data);
    }
}
