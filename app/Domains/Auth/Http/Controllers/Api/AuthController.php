<?php

namespace App\Domains\Auth\Http\Controllers\Api;

use App\Domains\Auth\Http\Requests\LoginRequest;
use App\Domains\Shared\Http\Controllers\Controller;
use App\Domains\Users\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

/**
 * @tags Authentication
 */
class AuthController extends Controller
{
    public function store(LoginRequest $request): JsonResponse
    {
        // Check if the user exists in the database
        if (User::where('email', $request->get('email'))->exists()) {
            /** @var \Illuminate\Contracts\Auth\Authenticatable|User $user */
            $user = User::where('email', $request->get('email'))->first();

            if ($user->disabled()) {
                return response()->json([
                    'message' => 'Your account has been deactivated.',
                ], Response::HTTP_FORBIDDEN);
            }

            // Check if the given password matches the hashed password in the database
            $valid = Auth::getProvider()->validateCredentials($user, [
                'password' => $request->get('password'),
            ]);

            // If the user exists and the password is correct, generate a new API token and return the user data
            if ($user && $valid) {
                if ($request->has('space')) {
                    $user->enterSpace($request->space());
                }

                $apiKey = $user->rollApiKey();

                return response()->json([
                    'message' => 'Successfully logged in!',
                    'token' => $apiKey->plainTextToken,
                ], Response::HTTP_OK);
            }
        }

        // If the user doesn't exist or the password is incorrect, return an error message
        return response()->json([
            'message' => 'Unauthorized, check your credentials.',
        ], Response::HTTP_UNAUTHORIZED);
    }

    /**
     * Logout.
     *
     * @param  Request  $request  The request object containing the authenticated user.
     * @return array The JSON response containing the success message.
     */
    public function destroy(Request $request): JsonResponse
    {
        // Delete the API keys of the authenticated user
        $request->user()->deleteApiKeys();

        // Leave the space of the authenticated user
        if (isset($request->user()->current_space_id)) {
            $request->user()->leaveCurrentSpace();
        }

        // If user has any impersonations, delete them
        if($request->user()->hasImpersonations()) {
            $request->user()->deleteImpersonations();
        }

        // Regenerate the session to invalidate the current session ID
        // $request->session()->regenerate(true);

        $guards = array_keys(config('auth.guards'));

        // Loop through each guard and logout.
        foreach ($guards as $guard) {
            $guard = app('auth')->guard($guard);

            // Not all guard types have a logout method. The SessionGuard (web) does,
            // the TokenGuard (api) does not. Only call the method if it exists
            // on the guard.
            if (method_exists($guard, 'logout')) {
                $guard->logout();
            }
        }

        // Return a success message
        return response()->json([
            'message' => 'You have been successfully logged out.',
        ]);
    }
}
