<?php

namespace App\Domains\Auth\Http\Controllers;

use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Fortify\Contracts\EmailVerificationNotificationSentResponse;
use <PERSON><PERSON>\Fortify\Http\Responses\RedirectAsIntended;

/* @phpstan-ignore-next-line */
/**
 * @tags Authentication
 */
class EmailVerificationNotificationController extends Controller
{
    public function store(Request $request): JsonResponse|EmailVerificationNotificationSentResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return $request->wantsJson()
                        ? response()->json(['message' => 'Email already verified.'], 204)
                        : app(RedirectAsIntended::class, ['name' => 'email-verification']);
        }

        $request->user()->sendEmailVerificationNotification();

        return app(EmailVerificationNotificationSentResponse::class);
    }
}
