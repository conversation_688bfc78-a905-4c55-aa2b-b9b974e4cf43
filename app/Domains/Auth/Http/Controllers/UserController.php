<?php

namespace App\Domains\Auth\Http\Controllers;

use App\Domains\Auth\Http\Resources\AuthenticatedUserResource;
use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;

/**
 * @tags Authentication
 */
class UserController extends Controller
{
    /**
     * Retrieve the authenticated user.
     *
     * This endpoint returns the authenticated user as a JSON response.
     *
     * @param  \Illuminate\Http\Request  $request  The HTTP request object.
     * @return \App\Domains\Auth\Http\Resources\AuthenticatedUserResource The authenticated user resource.
     */
    public function __invoke(Request $request)
    {
        // Retrieve the authenticated user from the request.
        // Then, transform the user into an authenticated user resource.
        // Finally, return the authenticated user resource.
        return AuthenticatedUserResource::make($request->user());
    }
}
