<?php

namespace App\Domains\Auth\Http\Controllers;

use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Fortify\Contracts\ProfileInformationUpdatedResponse;
use <PERSON><PERSON>\Fortify\Contracts\UpdatesUserProfileInformation;
use <PERSON><PERSON>\Fortify\Fortify;

/**
 * @tags User Profile
 */
class ProfileInformationController extends Controller
{
    /**
     * Update the user's profile information.
     *
     * @param  \Illuminate\Http\Request  $request  The request object.
     * @param  \Laravel\Fortify\Contracts\UpdatesUserProfileInformation  $updater  The updater object.
     * @return \Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse The response object.
     */
    public function update(
        Request $request,
        UpdatesUserProfileInformation $updater,
    ): ProfileInformationUpdatedResponse {

        // If usernames should be lowercased, modify the request.
        if (config('fortify.lowercase_usernames')) {
            // Retrieve the username field from the request and lowercase it.
            $username = Str::lower($request->{Fortify::username()});

            // Merge the lowercased username with the request.
            $request->merge([Fortify::username() => $username]);
        }

        // If the email field is empty, fill it with the current user's email.
        if (! $request->email) {
            // Retrieve the user's email from the request.
            $email = $request->user()->email;

            // Merge the email with the request.
            $request->merge(['email' => $email]);
        }

        // Update the user's profile information.
        /* @phpstan-ignore-next-line */
        $updater->update($request->user(), $request->all());

        // Return the appropriate response.
        return app(ProfileInformationUpdatedResponse::class);
    }
}
