<?php

namespace App\Domains\Auth\Http\Controllers;

use App\Domains\Shared\Http\Controllers\Controller;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Fortify\Contracts\CreatesNewUsers;

/**
 * @tags Authentication
 */
class RegisteredUserController extends Controller
{
    /**
     * Register new user.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request, CreatesNewUsers $action): JsonResponse
    {
        $user = $action->create($request->all());

        event(new Registered($user));

        $token = $user->createToken('lighthouse', ['lighthouse'])->plainTextToken;

        return response()->json([
            'token' => $token,
        ], Response::HTTP_CREATED);
    }
}
