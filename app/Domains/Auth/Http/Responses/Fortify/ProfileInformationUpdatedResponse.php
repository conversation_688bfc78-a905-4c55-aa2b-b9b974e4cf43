<?php

declare(strict_types=1);

namespace App\Domains\Auth\Http\Responses\Fortify;

use Illuminate\Http\JsonResponse;
use Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse as ProfileInformationUpdatedResponseContract;

class ProfileInformationUpdatedResponse implements ProfileInformationUpdatedResponseContract
{
    public function toResponse($request): JsonResponse
    {
        return response()->json([
            'message' => 'Profile information updated successfully.',
        ]);
    }
}
