<?php

namespace App\Domains\Auth\Http\Responses\Fortify;

use <PERSON><PERSON>\Fortify\Contracts\LoginResponse as LoginResponseContract;
use Laravel\Fortify\Fortify;

class LoginResponse implements LoginResponseContract
{
    public function toResponse($request)
    {
        return $request->expectsJson()
                    ? response()->json([
                        'two_factor' => $request->user()->two_factor_enabled,
                        'token' => $request->user()->createToken('lighthouse', ['lighthouse'])->plainTextToken,
                    ])
                    : redirect()->intended(Fortify::redirects('login'));
    }
}
