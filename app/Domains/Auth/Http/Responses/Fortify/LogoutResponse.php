<?php

namespace App\Domains\Auth\Http\Responses\Fortify;

use <PERSON><PERSON>\Fortify\Contracts\LogoutResponse as ContractsLogoutResponse;
use Laravel\Fortify\Fortify;

class LogoutResponse implements ContractsLogoutResponse
{
    public function toResponse($request)
    {
        return $request->expectsJson()
            ? response()->json(['message' => 'You are now logged out.'])
            : redirect()->intended(Fortify::redirects('login'));
    }
}
