<?php

namespace App\Domains\Notifications\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationAssetResource extends JsonResource
{
    public function toArray($request)
    {
        if (!$this->resource) {
            return null;
        }

        $this->loadMissing('editorial');

        return [
            'id' => $this->getResourceKey() ?? $this->id ?? null,
            'name' => $this->name ?? 'Unnamed Asset',
            'type' => $this->type?->value ?? 'unknown',
        ];
    }
}
