<?php

namespace App\Domains\Notifications\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationEditorialResource extends JsonResource
{
    public function toArray($request)
    {
        if (!$this->resource) {
            return null;
        }

        return [
            'id' => $this->resource->getResourceKey() ?? $this->resource->id ?? null,
            'name' => $this->resource->name ?? 'Unnamed Editorial',
            'phase' => $this->resource->phase?->value ?? $this->resource->phase ?? null,
            'sub_phase' => $this->resource->sub_phase?->value ?? $this->resource->sub_phase ?? null,
        ];
    }
}
