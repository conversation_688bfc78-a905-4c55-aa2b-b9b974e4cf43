<?php

namespace App\Domains\Notifications\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Betterflow\V1\Pitches\Http\Resources\NotificationUserResource;
use App\Domains\Betterflow\V1\Pitches\Models\Pitch;
use App\Domains\Betterflow\V1\Shared\Tasks\Models\Task;
use App\Domains\Users\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $this->loadMissing('notifiable');

        $resources = collect(data_get($this->data, 'resources', []))
            ->filter() // Remove any null/empty entries
            ->map(function ($resource) {
                // Validate resource structure
                if (!is_array($resource) || !isset($resource['type'], $resource['id'])) {
                    logger()->warning('NotificationsResource: Invalid resource structure', [
                        'notification_id' => $this->id,
                        'resource' => $resource
                    ]);
                    return null;
                }

                $resourceId = data_get($resource, 'id');
                $resourceType = data_get($resource, 'type');

                if (empty($resourceId) || empty($resourceType)) {
                    logger()->warning('NotificationsResource: Missing resource ID or type', [
                        'notification_id' => $this->id,
                        'resource_type' => $resourceType,
                        'resource_id' => $resourceId
                    ]);
                    return null;
                }

                try {
                    $model = match($resourceType) {
                        'task', 'assist' => Task::find($resourceId),
                        'asset' => Asset::findByPublicId($resourceId),
                        'editorial' => Editorial::findByPublicId($resourceId),
                        'pitch' => Pitch::findByPublicId($resourceId),
                        default => null,
                    };

                    if (is_null($model)) {
                        logger()->warning('NotificationsResource: Model not found', [
                            'notification_id' => $this->id,
                            'resource_type' => $resourceType,
                            'resource_id' => $resourceId
                        ]);
                    }

                    return $model;
                } catch (\Exception $e) {
                    logger()->error('NotificationsResource: Error finding model', [
                        'notification_id' => $this->id,
                        'resource_type' => $resourceType,
                        'resource_id' => $resourceId,
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            })
            ->filter() // Remove null results from model lookup
            ->map(function (Model $resource) {
                try {
                    $morphClass = $resource->getMorphClass();
                    
                    $transformedResource = match($morphClass) {
                        'task' => NotificationTaskResource::make($resource),
                        'asset' => NotificationAssetResource::make($resource),
                        'editorial' => NotificationEditorialResource::make($resource),
                        'pitch' => NotificationPitchResource::make($resource),
                        default => null,
                    };

                    if (is_null($transformedResource)) {
                        logger()->warning('NotificationsResource: Unknown morph class', [
                            'notification_id' => $this->id,
                            'morph_class' => $morphClass,
                            'model_class' => get_class($resource)
                        ]);
                    }

                    return $transformedResource;
                } catch (\Exception $e) {
                    logger()->error('NotificationsResource: Error transforming resource', [
                        'notification_id' => $this->id,
                        'model_class' => get_class($resource),
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            })
            ->filter() // Remove any null transformation results
            ->values(); // Re-index array

        $whoDidIt = data_get($this->data, 'who_did_it', null);
        $whoDidItResource = null;

        if ($whoDidIt && is_array($whoDidIt)) {
            $userId = data_get($whoDidIt, 'id');
            if ($userId) {
                try {
                    $user = User::find($userId);
                    if ($user) {
                        $whoDidItResource = SimpleUserResource::make($user);
                    } else {
                        logger()->warning('NotificationsResource: Who did it user not found', [
                            'notification_id' => $this->id,
                            'user_id' => $userId
                        ]);
                    }
                } catch (\Exception $e) {
                    logger()->error('NotificationsResource: Error finding who_did_it user', [
                        'notification_id' => $this->id,
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        return [
            'id' => $this->id,
            'read' => ! is_null($this->read_at),
            'notification_type' => str($this->type)->studly(),
            'to_whom' => SimpleUserResource::make($this->notifiable),
            'created_at' => $this->created_at,
            'created_at_format' => $this->created_at->diffForHumans(),
            
            ...$this->data,
            'who_did_it' => $whoDidItResource,
            'resources' => $resources,
        ];
    }
}
