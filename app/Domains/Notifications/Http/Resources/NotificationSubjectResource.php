<?php

namespace App\Domains\Notifications\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationSubjectResource extends JsonResource
{
    public function toArray(Request $request): array
    {
    
        return [
            'id' => $this->resource?->getResourceKey(),
            'model' => class_basename($this->resource),
            'type' => $this->resource?->getMorphClass(),
            ...$this->additional,
        ];
    }
}
