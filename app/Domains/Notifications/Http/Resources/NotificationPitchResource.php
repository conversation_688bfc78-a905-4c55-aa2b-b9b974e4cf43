<?php

namespace App\Domains\Notifications\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationPitchResource extends JsonResource
{
    public function toArray($request)
    {
        if (!$this->resource) {
            return null;
        }

        return [
            'id' => $this->getResourceKey() ?? $this->id ?? null,
            'short_name' => $this->short_name ?? 'Unnamed Pitch',
        ];
    }
}
