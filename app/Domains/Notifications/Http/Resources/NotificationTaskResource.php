<?php

namespace App\Domains\Notifications\Http\Resources;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        if (!$this->resource) {
            return null;
        }

        return [
            'id' => $this->resource->getResourceKey() ?? $this->resource->id ?? null,
            'title' => $this->resource->title ?? 'Untitled Task',
            'description' => $this->resource->description ?? null,
            'type' => $this->resource->type ?? 'unknown',
            'notes' => $this->resource->notes ?? null,
            'instructions' => data_get($this->resource, 'properties.instructions') ?? data_get($this->resource, 'data.instructions') ?? null,
            'assignee' => $this->resource->user ? SimpleUserResource::make($this->resource->user) : null,
            'due_at' => $this->resource->due_at ?? null,
        ];
    }
}
