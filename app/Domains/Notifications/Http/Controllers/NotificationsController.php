<?php

namespace App\Domains\Notifications\Http\Controllers;

use App\Domains\Notifications\Http\Resources\NotificationsResource;
use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\DB;

/**
 * @tags Application / Notifications
 */
class NotificationsController extends Controller
{
    /**
     * Get all notifications for the current user with possible pagination
     *
     * @param  Request  $request  The HTTP request
     * @return \Illuminate\Http\Resources\Json\JsonResource JSON response with the paginated notifications
     */
    public function index(Request $request)
    {
        $origin = $request->input('origin') ?? 'betterflow';
        
        // Check if there are pagination parameters
        if ($request->has('page') || $request->has('per_page')) {
            $query = $request->user()?->notificationsFor($origin)->latest();
            $perPage = $request->input('per_page', 20);
            $page = $request->input('page', 1);
            
            $notifications = $query->paginate($perPage, ['*'], 'page', $page);
            return NotificationsResource::collection($notifications);
        } else {
            // Works like before with no pagination to keep backward compatibility
            return NotificationsResource::collection($request->user()?->notificationsFor($origin)->get());
        }
    }

    public function store(Request $request): JsonResponse
    {
        return response()->json([
            'message' => 'Not implemented',
        ]);
    }

    /**
     * Mark all notifications as read for the current user
     *
     * @param  Request  $request  The HTTP request
     * @return \Illuminate\Http\JsonResponse JSON response with the result of the operation
     */
    public function markAllRead(Request $request): JsonResponse
    {
        $data = $request->validate([
            'notifications' => ['nullable', 'array'],
            'notifications.*' => ['uuid', 'exists:notifications,id'],
        ]);

        if (! $request->notifications) {
            $request->user()->unreadNotifications()->each(function (DatabaseNotification $notification): void {
                $notification->markAsRead();
            });

            return response()->json([
                'message' => 'Marked all unread notifications read.',
            ]);
        }

        try {
            if ($notifications = data_get($data, 'notifications')) {
                // $origin = $request->input('origin') ?? 'betterflow';
                DB::beginTransaction();
                collect($notifications)->each(function ($notification) use ($request): void {
                    if ($notification = $request->user()->unreadNotifications()->where('id', $notification)->first()) {
                        $notification->markAsRead();
                    } else {
                        throw new ModelNotFoundException('No notification found for ID');
                    }
                });
                DB::commit();

                DB::afterCommit(function (): void {});

                return response()->json([
                    'message' => 'All notifications marked as read',
                ], 200);
            }
        } catch (\Throwable $throwable) {
            return response()->json([
                'error' => true,
                'message' => $throwable->getMessage(),
            ], 500);
        }

        return response()->json([
            'error' => true,
            'message' => 'No notifications found',
        ], 500);
    }

    /**
     * Toggle the read status of a notification
     *
     * @param  Request  $request  The HTTP request
     * @param  DatabaseNotification  $notification  The notification to update
     * @return \Illuminate\Http\JsonResponse JSON response with the result of the update
     */
    public function toggle(Request $request, DatabaseNotification $notification)
    {
        // Check if the notification is already read
        if ($notification->read_at) {
            // Mark the notification as unread
            $notification->markAsUnread();
        } else {
            // Mark the notification as read
            $notification->markAsRead();
        }

        // Return a JSON response with the result of the update
        return response()->json([
            'message' => 'Notification marked as ' . ($notification->read_at ? 'read' : 'unread'),
        ], 200);
    }
}
