<?php

namespace App\Domains\Shared\Concerns;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

trait StreamsMedia
{
    /**
     * Stream the media file
     *
     * @param Media $media
     * @param string|null $conversion
     * @return StreamedResponse
     */
    protected function streamMedia(Media $media, ?string $conversion = null)
    {
        $disk = Storage::disk($media->getDiskDriverName());

        // Determine the path based on whether a conversion is requested
        if ($conversion && $media->hasGeneratedConversion($conversion)) {
            $path = $media->getPathRelativeToRoot($conversion);
            $disk = Storage::disk($media->getConversionsDiskDriverName());
        } else {
            $path = $media->getPathRelativeToRoot();
        }

        // dd($disk);
        //
        if (!$disk->exists($path)) {
            return response()->json(['error' => 'File not found on disk'], Response::HTTP_NOT_FOUND);
        }

        $mimeType = $media->mime_type;
        $size = $disk->size($path);
        $fileName = $media->file_name;

        // Create a streamed response
        return response()->stream(
            function () use ($disk, $path) {
                $stream = $disk->readStream($path);
                fpassthru($stream);
                if (is_resource($stream)) {
                    fclose($stream);
                }
            },
            Response::HTTP_OK,
            [
                'Content-Type' => $mimeType,
                'Content-Length' => $size,
                'Content-Disposition' => 'inline; filename="' . $fileName . '"',
                'Cache-Control' => 'public, max-age=86400 immutable',
            ]
        );
    }
}
