<?php

namespace App\Domains\Shared\Concerns;

use App\Domains\Betterflow\V1\Pitches\Models\PitchState;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait HasState
{
    public function state(): MorphOne
    {
        return $this->morphOne($this->stateModelClass(), 'statable')->latestOfMany();
    }

    public function states(): MorphMany
    {
        return $this->morphMany($this->stateModelClass(), 'statable')->chaperone('statable');
    }

    public function rawStates(): MorphMany
    {
        return $this->morphMany($this->stateModelClass(), 'statable');
    }

    public function firstState(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->states()->oldest()->first() ?? null,
        );
    }

    public function firstStateId(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->firstState?->state,
        );
    }

    public function currentState(): Attribute
    {
        return Attribute::make(
            get: fn(): ?PitchState => $this->state instanceof PitchState
                ? $this->state
                : null,
        );
    }

    public function currentStateId(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->currentState?->state,
        );
    }

    public function previousState(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->states()->count() > 1 ? $this->states()->where('id', '<', $this->state?->id)->orderByDesc('id')->first() : $this->firstState,
        );
    }

    public function previousStateId(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->previousState?->state,
        );
    }

    protected function stateModelClass(): string
    {
        return $this->stateModelClass;
    }
}
