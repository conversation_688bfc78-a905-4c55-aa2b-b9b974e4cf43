<?php

namespace App\Domains\Shared\ValueObjects;

use App\Domains\Shared\Helpers\TimeHelper;

class Timezone
{
    public function __construct(
        public string $key,
        public string $name,
    ) {}

    public function setKeyName(string $key, string $name): self
    {
        $this->key = $key;
        $this->name = $name;

        return $this;
    }

    public static function fromKey(string|int $key): self
    {
        return TimeHelper::timezone($key);
    }

    public function toArray(): array
    {
        return [
            'key' => $this->key,
            'name' => $this->name,
        ];
    }
}
