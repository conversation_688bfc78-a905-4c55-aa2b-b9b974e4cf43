<?php

namespace App\Domains\Shared\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\Events\JobFailed;
use Notification;

class JobFailedNotificaiton extends Notification
{

    public function __construct(
        public readonly JobFailed $event,
        public readonly ?string $message = null,
    )
    {}

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line(sprintf('A job has failed on the queue: %s', $this->event->connectionName))
            ->line(sprintf('Queue: %s', $this->event->job->queue))
            ->line(sprintf('Payload: %s', $this->event->job->getRawBody()))
            ->line(sprintf('Exception: %s', $this->event->exception));
    }
}
