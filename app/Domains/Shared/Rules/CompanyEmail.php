<?php

namespace App\Domains\Shared\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CompanyEmail implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $allowedDomains = explode(',', config('globalpress.email_domains'));

        if ($allowedDomains) {
            $allowedDomains = array_map('trim', $allowedDomains);
            $emailDomain = str($value)->afterLast('@');

            if (! in_array($emailDomain, $allowedDomains)) {
                $fail('The :attribute must be a valid ' . collect($allowedDomains)->aggregateSimple('or') . ' email address.')->translate();
            }
        }
    }
}
