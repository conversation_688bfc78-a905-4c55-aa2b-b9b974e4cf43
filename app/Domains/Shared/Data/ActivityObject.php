<?php

namespace App\Domains\Shared\Data;

use App\Domains\Shared\Contracts\RecordableEvent;
use Illuminate\Database\Eloquent\Model;
use Spatie\LaravelData\Data;

class ActivityObject extends Data
{
    public function __construct(
        public readonly string $logName,
        public readonly Model $on,
        public readonly RecordableEvent $event,
        public readonly ?string $description = '',
        public readonly ?array $properties = [],
        public readonly ?string $by = null,
    ) {}
}
