<?php

namespace App\Domains\Shared\Data;

use App\Domains\Auth\Http\Resources\SimpleUserResource;
use App\Domains\Notifications\Http\Resources\NotificationSubjectResource;
use App\Domains\Shared\Enums\MessageType;
use App\Domains\Users\Models\User;
use Illuminate\Contracts\Support\Arrayable;

class NotificationDTO implements Arrayable
{
    public function __construct(
        // Origin of the notification
        public string $origin,
        // Title of the notification
        public string $title,
        // Type of notification
        public ?string $type,
        // Message of the notification
        public string $message,
        // Subject of the notification
        public mixed $forWhat,
        // User that will receive the notification
        public User|array|null $user = null,
        // Action to be taken by the user
        public ?NotificationAction $action = null,
    ) {}

    public function toArray(): array
    {
        $data = [
            'what_happened' => $this->title,
            'message' => $this->message,
            'to_whom' => SimpleUserResource::make($this->user) ?? null,
            'from_where' => $this->origin,
            'for_what' => NotificationSubjectResource::make($this->forWhat),
        ];
        if ($this->action !== null) {
            $data['whats_next'] = [
                'severity' => data_get($this->action, 'severity') ?? MessageType::Muted,
                'button_label' => data_get($this->action, 'buttonLabel') ?? 'View Details',
                'message' => data_get($this->action, 'message') ?? 'View Pitch',
            ];
        }

        return $data;
    }
}
