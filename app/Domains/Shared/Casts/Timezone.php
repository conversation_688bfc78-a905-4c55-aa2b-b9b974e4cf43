<?php

namespace App\Domains\Shared\Casts;

use App\Domains\Shared\Helpers\TimeHelper;
use App\Domains\Shared\ValueObjects\Timezone as ValueObjectsTimezone;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class Timezone implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {   

        if($value !== null && $value instanceof ValueObjectsTimezone) {
            return $value;
        }

        return ValueObjectsTimezone::fromKey((int) $value);
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {

        if (is_int($value)) {
            return $value;
        }

        if (is_array($value)) {
            return (int) $value['id'];
        }

        if (is_object($value)) {
            return (int) $value->key;
        }

        if (str($value)->contains('/')) {
            $timezone = TimeHelper::timezones()->where('name', $value)->first();

            return (int) $timezone->key;
        }

        return (int) $value;
    }
}
