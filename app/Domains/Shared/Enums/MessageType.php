<?php

namespace App\Domains\Shared\Enums;

enum MessageType: string
{
    case Info = 'info';
    case Success = 'success';
    case Warning = 'warning';
    case Danger = 'danger';
    case Muted = 'muted';
    case Primary = 'primary';

    public static function toArray(): array
    {
        return array_map(fn($value) => $value->value, self::cases());
    }

    public static function get(string $value): string
    {
        return self::from($value)->value;
    }
}
