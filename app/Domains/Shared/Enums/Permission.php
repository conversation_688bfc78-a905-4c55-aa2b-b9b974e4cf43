<?php

namespace App\Domains\Shared\Enums;

enum Permission: string
{
    case CREATE_PITCH = 'create pitch';
    case SUBMIT_PITCH = 'submit pitch';
    case VIEW_PITCH = 'view pitch';
    case EDIT_PITCH = 'edit pitch';
    case ASSIGN_COLLABORATORS = 'assign collaborators';
    case VIEW_REPORTS = 'view reports';
    case TRANSLATE_PITCH = 'translate pitch';
    case MOVE_PITCH = 'move pitch';
    case REJECT_PITCH = 'reject pitch';
    case ESCALATE_EDITORIAL = 'escalate editorial';
    case UPDATE_PITCH = 'update pitch';
    case VIEW_PITCHES = 'view pitches';
    case VIEW_TRANSLATED_PITCHES = 'translate pitches';
    case DELETE_OWN_PITCH = 'delete own pitch';
    case VIEW_SUBMITTED_PITCHES = 'view submitted pitches';
    case ASSIGN_USERS = 'assign users';
    case APPROVE_PITCH = 'approve pitch';
    case APPROVE_TRAVEL = 'approve travel';

        // Editorials
    case CREATE_EDITORIAL = 'create editorial';
    case VIEW_EDITORIAL = 'view editorial';
    case EDIT_EDITORIAL = 'edit editorial';
    case DELETE_EDITORIAL = 'delete editorial';
    case ASSIGN_EDITORIALS_TO_USERS = 'assign editorials to users';
    case REQUEST_EDITORIAL_ASSIST = 'request editorial assist';
    case TRANSLATE_EDITORIAL = 'translate editorial';

        // Assets
    case CREATE_ASSET = 'create asset';
    case UPDATE_ASSET = 'update asset';
    case DELETE_ASSET = 'delete asset';
    case REQUEST_ASSIST = 'request assist';
    case REQUEST_ASSET_ASSIST = 'request asset assist';

    case IMPERSONATE = 'impersonate';
    case BE_IMPERSONATED = 'be impersonated';
}
