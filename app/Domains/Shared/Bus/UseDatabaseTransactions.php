<?php

namespace App\Domains\Shared\Bus;

use Closure;
use Illuminate\Support\Facades\DB;

class UseDatabaseTransactions
{
    public function handle($command, Closure $next)
    {
        if (app()->runningInConsole()) {
            return $next($command);
        }

        return DB::transaction(function () use ($command, $next) {
            return $next($command);
        }, attempts: 3);
    }
}
