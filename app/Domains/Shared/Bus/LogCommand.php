<?php

namespace App\Domains\Shared\Bus;

use Closure;
use Illuminate\Support\Facades\DB;
use Psr\Log\LoggerInterface;

class LogCommand
{
    protected \Psr\Log\LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function handle($command, Closure $next)
    {
        $result = $next($command);

        $this->logger->debug('DB Transactionlevel: ' . DB::transactionLevel());

        $this->logger->debug('Command handled: ' . get_class($command));

        return $result;
    }
}
