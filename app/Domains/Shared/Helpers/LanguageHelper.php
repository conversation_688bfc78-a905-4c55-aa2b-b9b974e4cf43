<?php

namespace App\Domains\Shared\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ItemNotFoundException;
use stdClass;

class LanguageHelper
{
    public static function languageCodes()
    {
        return Cache::rememberForever('language_codes', function () {
            return collect(json_decode(File::get(resource_path('misc/language_codes.json')), true));
        });
    }

    public static function language($code): \stdClass
    {
        $language = static::languageCodes()->firstOrFail(function ($lang, $key) use ($code): bool {
            return $code === $key;
        });

        throw_if(! $language, new \Exception('Language not found'));

        $language_model = new stdClass();

        $language_model->code = $code;
        $language_model->name = $language['name'];
        $language_model->nativeName = $language['nativeName'];

        return $language_model;
    }

    public static function countries()
    {
        return Cache::rememberForever('country_codes', function () {
            $countries = File::json(resource_path('misc/countries.json'));

            return collect($countries);
        });
    }

    public static function country($code): \stdClass
    {
        $country = static::countries()->first(function (array $country, $key) use ($code): bool {
            return $code === $country['code'];
        });

        throw_if(! $country, new ItemNotFoundException('Country not found'));

        $country_model = new stdClass();

        $country_model->code = $code;
        $country_model->name = $country['name'];

        return $country_model;
    }
}
