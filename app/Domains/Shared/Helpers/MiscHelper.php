<?php

namespace App\Domains\Shared\Helpers;

use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

class MiscHelper
{
    public static function aggregate_items(Collection|EloquentCollection|array $items, $column = 'name', $replacer = 'and')
    {
        if (is_array($items)) {
            $items = collect($items);
        }

        return str($items->pluck($column)->implode(', '))->replaceLast(', ', sprintf(' %s ', $replacer));
    }
}
