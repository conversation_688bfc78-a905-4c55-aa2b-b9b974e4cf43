<?php

namespace App\Domains\Shared\Helpers;

use App\Domains\Betterflow\V1\Admin\Crud\Models\EditorialStage;
use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchHoldReason;
use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchStage;
use App\Domains\Betterflow\V1\Admin\Crud\Models\PitchType;
use App\Domains\Topics\Models\Topic;
use App\Domains\Users\Models\Admin;
use App\Domains\Users\Models\AudienceLiaison;
use App\Domains\Users\Models\CopyEditor;
use App\Domains\Users\Models\Editor;
use App\Domains\Users\Models\EditorialAdmin;
use App\Domains\Users\Models\FactChecker;
use App\Domains\Users\Models\GraphicsEditor;
use App\Domains\Users\Models\Interpreter;
use App\Domains\Users\Models\ManagingEditor;
use App\Domains\Users\Models\PhotoEditor;
use App\Domains\Users\Models\Reporter;
use App\Domains\Users\Models\Researcher;
use App\Domains\Users\Models\SuperAdmin;
use App\Domains\Users\Models\System;
use App\Domains\Users\Models\Translator;
use App\Domains\Users\Models\User;
use App\Domains\Verticals\Models\Vertical;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Spatie\Permission\Models\Role;

class CrudHelper
{
    public static function seedRoles(): array
    {
        return Cache::remember('roles', now()->addDay(), function (): array {
            return [
                System::getRoleName(),
                SuperAdmin::getRoleName(),
                Admin::getRoleName(),
                AudienceLiaison::getRoleName(),
                CopyEditor::getRoleName(),
                Editor::getRoleName(),
                'topic_editor',
                'vertical_editor',
                'story_editor',
                'web_editor',
                'research_editor',
                'visual_editor',
                FactChecker::getRoleName(),
                GraphicsEditor::getRoleName(),
                Interpreter::getRoleName(),
                ManagingEditor::getRoleName(),
                PhotoEditor::getRoleName(),
                Reporter::getRoleName(),
                EditorialAdmin::getRoleName(),
                Researcher::getRoleName(),
                Translator::getRoleName(),
                User::getRoleName(),
                'illustrator',
                'photo_archivist',
            ];
        });
    }

    public static function roles(): EloquentCollection
    {
        return Cache::remember('roles', now()->addDay(), function () {
            return Role::whereNot('name', 'system')->get();
        });
    }

    public static function verticals(): Collection
    {
        return Cache::remember('verticals', now()->addDay(), function () {
            return Vertical::select(['id', 'slug', 'name', 'description', 'topic_id', 'assigned_editor_id', 'color', 'active', 'icon'])->orderBy('id')->get();
        });
    }

    public static function topics(): Collection
    {
        return Cache::remember('topics', now()->addDay(), function () {
            return Topic::select(['id', 'slug', 'name', 'description', 'assigned_editor_id', 'color', 'active', 'icon'])->with('verticals')->orderBy('id')->get();
        });
    }

    public static function pitch_types(): Collection
    {
        return Cache::remember('pitch_types', now()->addDay(), function () {
            return PitchType::select(['id', 'slug', 'name', 'description', 'color', 'active', 'icon'])->orderBy('id')->get();
        });
    }

    public static function pitch_stages(): Collection
    {
        return Cache::remember('pitch_stages', now()->addDay(), function () {
            return PitchStage::where('slug', '<>', 'approved')->select(['id', 'slug', 'name', 'description', 'color', 'active'])->orderBy('id')->get();
        });
    }

    public static function editorial_stages(): Collection
    {
        return Cache::remember('editorial_stages', now()->addDay(), function () {
            return EditorialStage::select(['id', 'slug', 'name', 'description', 'color', 'active'])->get();
        });
    }

    public static function pitch_hold_reasons(): Collection
    {
        return Cache::remember('pitch_hold_reasons', now()->addDay(), function () {
            return PitchHoldReason::select(['id', 'slug', 'name', 'description', 'color', 'active'])->get();
        });
    }

    public static function colors(): array
    {
        return [
            '#FDE8E8',
            '#FDF6B2',
            '#DEF7EC',
            '#E1EFFE',
            '#E5EDFF',
            '#EDEBFE',
            '#FCE8F3',
            '#FFEDD5',
            '#FEF3C7',
            '#ECFCCB',
            '#D1FAE5',
            '#CCFBF1',
            '#CFFAFE',
            '#E0F2FE',
            '#EDE9FE',
            '#FAE8FF',
            '#FFE4E6',
            '#F1FBF4',
            '#F2F8FC',
            '#FDFAE9',
            '#FEF6E3',
            '#FCF0FE',
            '#F6EDEB',
            '#CBDDEF',
            '#F8EDCF',
            '#E6F1EE',
            '#DDDAF2',
            '#DDECF7',
            '#E1EBEB',
            '#F3EBE1',
            '#FDF1F5',
            '#F5EEEA',
            '#D8E3E7',
            '#EBCFC0',
        ];
    }
}
