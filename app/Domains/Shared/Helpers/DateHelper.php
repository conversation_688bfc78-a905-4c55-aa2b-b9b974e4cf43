<?php

namespace App\Domains\Shared\Helpers;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Support\Facades\Date;

class DateHelper
{
    public static function convertToUTC(string $eventDate, string|int $userTimezone): CarbonInterface
    {
        // Convert to UTC
        $timezone = TimeHelper::timezone($userTimezone);

        return Date::createFromDate($eventDate, $timezone->name)->utc();
    }

    public static function convertFromUTC(string $eventDate, string|int|array $userTimezone): CarbonInterface
    {
        // Convert from UTC
        $timezone = TimeHelper::timezone($userTimezone);

        return Date::createFromDate($eventDate)->setTimezone($timezone->name);
    }
}
