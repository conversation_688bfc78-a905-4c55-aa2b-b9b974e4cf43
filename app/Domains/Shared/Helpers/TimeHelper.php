<?php

namespace App\Domains\Shared\Helpers;

use App\Domains\Shared\ValueObjects\Timezone;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class TimeHelper
{
    public static function timezones(): Collection
    {
        return Cache::remember('timezones', now()->addDay(), function () {
            return collect(timezone_identifiers_list())->map(function ($tz, $index): \App\Domains\Shared\ValueObjects\Timezone {
                return new Timezone((string) $index, $tz);
            });
        });
    }

    public static function timezone(string|int $tz_code_or_name): Timezone
    {
        $timezone = static::timezones()->first(function ($tz) use ($tz_code_or_name): bool {
            if (is_int($tz_code_or_name)) {
                return $tz_code_or_name === (int) $tz->key;
            }

            return $tz->name === $tz_code_or_name;

        });

        throw_if(! $timezone, new ModelNotFoundException(sprintf('Timezone %s not found', $tz_code_or_name)));

        return $timezone;
    }

    public static function defaultTimezone(): Timezone
    {
        return static::timezone('America/New_York');
    }
}
