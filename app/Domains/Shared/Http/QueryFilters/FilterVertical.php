<?php

namespace App\Domains\Shared\Http\QueryFilters;

use Closure;
use Illuminate\Database\Eloquent\Builder;

class FilterVertical extends FilterRequest
{
    public function handle(Builder $query, Closure $next): Builder
    {
        $verticalsFromRequest = $this->request->vertical ?? $this->request->verticals;

        $query->when($verticalsFromRequest, function ($query, $verticals): void {
            $verticals = str($verticals)->explode(',')->filter()->toArray();
            $query->whereHas('verticals', function ($query) use ($verticals): void {
                $query->whereIn('slug', $verticals);
            });
        });

        return $next($query);
    }
}
