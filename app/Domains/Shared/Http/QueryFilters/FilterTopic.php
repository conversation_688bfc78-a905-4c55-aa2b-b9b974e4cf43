<?php

namespace App\Domains\Shared\Http\QueryFilters;

use Closure;
use Illuminate\Database\Eloquent\Builder;

class FilterTopic extends FilterRequest
{
    public function handle(Builder $query, Closure $next): Builder
    {
        $topicsFromRequest = $this->request->topic ?? $this->request->topics;

        $query->when($topicsFromRequest, function ($query, $topics): void {
            if (! is_array($topics)) {
                $topics = str($topics)->explode(',')->filter()->toArray();
            }

            $query->whereHas('topic', function ($query) use ($topics): void {
                $query->whereIn('slug', $topics);
            });
        });

        return $next($query);
    }
}
