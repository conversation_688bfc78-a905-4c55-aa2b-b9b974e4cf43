<?php

namespace App\Domains\Shared\Http\Controllers;

use App\Domains\Shared\Concerns\StreamsMedia;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\StreamedResponse;

class MediaStreamController extends Controller
{
    use StreamsMedia;
    /**
     * Stream a media file by UUID
     *
     * @param Request $request
     * @param Media $media
     * @return Response|StreamedResponse
     */
    public function streamByUuid(Request $request, Media $media)
    {
        if (!$media) {
            return response()->json(['error' => 'Media not found'], Response::HTTP_NOT_FOUND);
        }

        return $this->streamMedia($media, $request->get('conversion'));
    }

    /**
     * Stream a media file by filename
     *
     * @param Request $request
     * @param Media $media
     * @return Response|StreamedResponse
     */
    public function streamByFilename(Request $request, Media $media)
    {
        if (!$media) {
            return response()->json(['error' => 'Media not found'], Response::HTTP_NOT_FOUND);
        }

        return $this->streamMedia($media, $request->get('conversion'));
    }
}
