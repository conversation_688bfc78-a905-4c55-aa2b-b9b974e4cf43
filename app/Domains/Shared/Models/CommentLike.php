<?php

namespace App\Domains\Shared\Models;

use Maize\Markable\Models\Like;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CommentLike extends Like
{
    use LogsActivity;

    protected $table = 'reactions_likes';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logAll();
    }

    public static function markableRelationName(): string
    {
        return 'likers';
    }
}
