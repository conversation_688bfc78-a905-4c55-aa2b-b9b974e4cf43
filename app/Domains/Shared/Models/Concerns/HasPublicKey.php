<?php

namespace App\Domains\Shared\Models\Concerns;

use App\Domains\Shared\Contracts\HasPublicKey as HasPublicKeyContract;
use Illuminate\Support\Str;
use InvalidArgumentException;

trait HasPublicKey
{
    protected static function bootHasPublicKey(): void
    {
        static::creating(function (HasPublicKeyContract $model): void {
            $publicKeyName = property_exists($model, 'publicKey')
            ? $model->publicKey
            : 'public_id';

            if (! $model->isDirty($publicKeyName)) {
                $model->setAttribute($publicKeyName, Str::orderedUuid()->toString());
            }
        });
    }

    protected $publicKey = 'public_id';

    public function getPublicKeyName(): string
    {
        return $this->publicKey ?? 'public_id';
    }

    public function getPublicKey(): string
    {
        return $this->getAttribute($this->getPublicKeyName());
    }

    /**
     * Get the public id alias for getPub<PERSON><PERSON><PERSON>.
     */
    public function getPublicId(): string
    {
        return $this->getPublicKey();
    }

    public static function findByPublicId(string $publicId): ?static
    {
        if (! str($publicId)->isUuid()) {
            throw new InvalidArgumentException('Invalid public id');
        }

        $model = static::query()
            ->where('public_id', $publicId)
            ->first();

        return $model instanceof HasPublicKeyContract ? $model : null;
    }
}
