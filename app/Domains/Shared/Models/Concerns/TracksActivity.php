<?php

namespace App\Domains\Shared\Models\Concerns;

trait TracksActivity
{
    public static function bootTracksActivity(): void
    {
        static::pivotSyncing(function ($model, $relationName): void {
            // ds('syncing', $model, $relationName);
            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Syncing';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}();

        });

        static::pivotSynced(function ($model, $relationName, $changes): void {
            // ds('synced', $model, $relationName);
            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Synced';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($changes);
        });

        static::pivotAttaching(function ($model, $relationName, $pivotIds, $pivotIdsAttributes): void {
            // ds('attaching', $model, $relationName, $pivotIds, $pivotIdsAttributes);
            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Attaching';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($pivotIds, $pivotIdsAttributes);
        });

        static::pivotAttached(function ($model, $relationName, $pivotIds, $pivotIdsAttributes): void {
            // ds('attached', $model, $relationName, $pivotIds, $pivotIdsAttributes);
            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Attached';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($pivotIds, $pivotIdsAttributes);
        });

        static::pivotDetaching(function ($model, $relationName, $pivotIds): void {
            // ds('detaching', $model, $relationName, $pivotIds);

            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Detaching';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($pivotIds);
        });

        static::pivotDetached(function ($model, $relationName, $pivotIds): void {
            // ds('detached', $model, $relationName, $pivotIds);

            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Detached';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($pivotIds);
        });

        static::pivotUpdating(function ($model, $relationName, $pivotIds, $pivotIdsAttributes): void {
            // ds('updating', $model, $relationName, $pivotIds, $pivotIdsAttributes);

            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Updating';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($pivotIds, $pivotIdsAttributes);
        });

        static::pivotUpdated(function ($model, $relationName, $pivotIds, $pivotIdsAttributes): void {
            // ds('updated', $model, $relationName, $pivotIds, $pivotIdsAttributes);

            if (app()->runningInConsole()) {
                return;
            }

            $method = 'track' . str($relationName)->studly()->plural() . 'Updated';

            if (! method_exists($model, $method)) {
                return;
            }

            $model->{$method}($pivotIds, $pivotIdsAttributes);
        });
    }
}
