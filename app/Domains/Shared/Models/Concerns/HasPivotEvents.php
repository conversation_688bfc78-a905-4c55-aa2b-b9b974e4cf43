<?php

namespace App\Domains\Shared\Models\Concerns;

use GeneaLabs\LaravelPivotEvents\Traits\ExtendFireModelEventTrait;
use GeneaLabs\LaravelPivotEvents\Traits\ExtendRelationsTrait;

trait HasPivotEvents
{
    use ExtendFireModelEventTrait;
    use ExtendRelationsTrait;

    protected $pivotObservables = [
        'pivotSyncing', 'pivotSynced',
        'pivotAttaching', 'pivotAttached',
        'pivotDetaching', 'pivotDetached',
        'pivotUpdating', 'pivotUpdated',
    ];

    public function getObservableEvents(): array
    {
        return array_merge(
            parent::getObservableEvents(),
            $this->pivotObservables,
        );
    }

    public static function pivotSyncing($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotSyncing', $callback);
    }

    public static function pivotSynced($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotSynced', $callback);
    }

    public static function pivotAttaching($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotAttaching', $callback);
    }

    public static function pivotAttached($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotAttached', $callback);
    }

    public static function pivotDetaching($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotDetaching', $callback);
    }

    public static function pivotDetached($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotDetached', $callback);
    }

    public static function pivotUpdating($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotUpdating', $callback);
    }

    public static function pivotUpdated($callback, $priority = 0): void
    {
        static::registerModelEvent('pivotUpdated', $callback);
    }
}
