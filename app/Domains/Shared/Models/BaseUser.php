<?php

namespace App\Domains\Shared\Models;

use App\Domains\Users\Database\Factories\UserFactory;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Contracts\Auth\MustVerifyEmail as AuthMustVerifyEmail;
use Illuminate\Foundation\Auth\Access\Authorizable;

abstract class BaseUser extends BaseDomainModel implements AuthenticatableContract, AuthMustVerifyEmail, AuthorizableContract, CanResetPasswordContract
{
    use Authenticatable;
    use Authorizable;
    use CanResetPassword;
    use MustVerifyEmail;

    protected static function newFactory()
    {
        $class = str(basename(str_replace('\\', '/', static::class))) . 'Factory';

        $classPath = str(static::class)->before('\\Models')->append('\\Database\\Factories\\')->toString();

        if (class_exists($classPath . $class, false)) {
            $factory = ($classPath . $class)::new();
        } else {
            $factory = UserFactory::new();
        }

        return $factory->withRole(static::class)->setModel(static::class);
    }
}
