<?php

namespace App\Domains\Shared\Models;

use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\Factory;
/**
 * Abstract state model representing state tracking and management.
 */
abstract class State extends BaseDomainModel
{
    protected $table = 'states';

    protected $with = ['stateModel'];

    protected $fillable = ['name', 'state', 'attributes'];

    abstract public function stateModel();

    public function statable(): MorphTo
    {
        return $this->morphTo();
    }

    public static function newFactory(): Factory|null
    {
        return null;
    }
}
