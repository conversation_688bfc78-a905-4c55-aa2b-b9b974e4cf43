<?php

namespace App\Domains\Shared\Media;

use Spa<PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\Support\PathGenerator\PathGenerator;

class PitchAttachmentsPathGenerator implements PathGenerator
{
    /*
     * Get the path for the given media, relative to the root storage path.
     */
    public function getPath(Media $media): string
    {
        return $this->getBasePath($media);
    }

    /*
     * Get the path for conversions of the given media, relative to the root storage path.
     */
    public function getPathForConversions(Media $media): string
    {
        return sprintf('%sconversions/', $this->getBasePath($media));
    }

    /*
     * Get the path for responsive images of the given media, relative to the root storage path.
     */
    public function getPathForResponsiveImages(Media $media): string
    {
        return sprintf('%sresponsive-images/', $this->getBasePath($media));
    }

    /*
     * Get a unique base path for the given media.
     */
    protected function getBasePath(Media $media): string
    {
        $model = $media->loadMissing('model')->model;

        return sprintf('pitches/%s/attachments/', $model->getPublicId());
    }
}
