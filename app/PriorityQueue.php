<?php

namespace App;

class PriorityQueue
{
    private $queue = [];

    public function addTask(callable $task, int $priority)
    {
        $this->queue[] = ['task' => $task, 'priority' => $priority];
        usort($this->queue, function ($a, $b) {
            return $b['priority'] <=> $a['priority'];
        });
    }

    public function executeTasks()
    {
        while ($task = array_pop($this->queue)) {
            $task['task']();
        }
    }
}
