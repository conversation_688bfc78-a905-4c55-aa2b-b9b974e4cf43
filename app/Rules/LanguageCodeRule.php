<?php

namespace App\Rules;

use App\Domains\Shared\Helpers\LanguageHelper;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class LanguageCodeRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // The value must be an accepted language code, it has this format: en-EN fr-FR
        if (! preg_match('/^[a-z]{2}-[A-Z]{2}$/', $value)) {
            $fail('The :attribute must be a valid language code.');
        }

        // Check if the language code is in the list of accepted language codes
        if (! in_array($value, LanguageHelper::languageCodes()->pluck('code')->toArray())) {
            $fail('The :attribute must be an existing language code.');
        }
    }
}
