# GitHub Action for <PERSON><PERSON> with PostgreSQL and Redis
name: Lighthouse Test
on: [push, pull_request]
jobs:
  tests:
    name: Run Tests
    runs-on: ubuntu-latest

    env:
      BROADCAST_DRIVER: log
      CACHE_DRIVER: array
      QUEUE_CONNECTION: sync
      SESSION_DRIVER: array
      DB_CONNECTION: pgsql
      DB_PASSWORD: postgres
      DB_USERNAME: postgres
      DB_DATABASE: lighthouse_db

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: lighthouse_db
        ports:
          - 5432/tcp
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 3

      redis:
        image: redis
        ports:
          - 6379/tcp
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            composer-

      - name: Install PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3

      - name: Install composer dependencies
        run: |
          composer install --no-scripts --no-interaction --dev

      - name: Prepare the application
        run: |
          php -r "file_exists('.env') || copy('.env.example', '.env');"
          php artisan key:generate

      - name: Run Migrations
        run: php artisan migrate -v --force
        env:
          DB_HOST: localhost
          DB_PORT: ${{ job.services.postgres.ports[5432] }}

      - name: Run Test suite
        run: php artisan test
        env:
          DB_HOST: localhost
          DB_PORT: ${{ job.services.postgres.ports[5432] }}
