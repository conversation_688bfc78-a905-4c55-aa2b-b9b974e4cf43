FROM richarvey/nginx-php-fpm:3.1.6

RUN docker-php-ext-install pcntl
RUN docker-php-ext-configure pcntl --enable-pcntl

COPY . .

ADD ./docker/supervisor/app.conf /etc/supervisor/conf.d/app.conf

# Image config
ENV SKIP_COMPOSER 1
ENV WEBROOT /var/www/html/public
ENV PHP_ERRORS_STDERR 1
ENV RUN_SCRIPTS 1
ENV REAL_IP_HEADER 1

# <PERSON><PERSON> config
ENV APP_ENV production
ENV APP_DEBUG false
ENV LOG_CHANNEL stderr

# Allow composer to run as root
ENV COMPOSER_ALLOW_SUPERUSER 1

CMD ["/start.sh"]