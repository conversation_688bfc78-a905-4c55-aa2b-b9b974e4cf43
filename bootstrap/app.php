<?php

use App\Http\Middleware\EnsureHttpsSecured;
use App\Http\Middleware\Guest;
use App\Http\Middleware\ImpersonateSanctum;
use App\Http\Middleware\SecurityHeadersMiddleware;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession; // Import the new middleware
use Symfony\Component\HttpKernel\Exception\HttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([])
    ->withSchedule(function (Schedule $schedule) {
        $schedule->command('backup:run', ['--only-db'])
            ->weekdays()->at('01:00')->timezone('America/New_York')
            ->onSuccessWithOutput(fn() => logger('Backup complete'))
            ->onFailureWithOutput(fn() => logger('Backup failed'));

        $schedule->command('backup:clean', ['--only-db'])
            ->monthly()->at('01:00')->timezone('America/New_York')
            ->onSuccessWithOutput(fn() => logger('Backup cleanup complete'))
            ->onFailureWithOutput(fn() => logger('Backup cleanup failed'));

        $schedule->command('check:overdue-tasks')->daily()->timezone('America/New_York');
        $schedule->command('sanctum:prune-expired')->daily()->timezone('America/New_York');
        if (!app()->isProduction()) {
            $schedule->command('horizon:snapshot')->everyFiveMinutes();
        }
    })
    ->withEvents(
        discover: [
            app_path('Domains'),
            app_path('Domains/Betterflow/V1'),
            app_path('Domains/Betterflow/V1/Shared'),
            app_path('Domains/Auth'),
            app_path('Domains/Users'),
        ],
    )
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api/routes.php',
        apiPrefix: '',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withBroadcasting(__DIR__ . '/../routes/channels.php')
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->append([
            EnsureHttpsSecured::class,
            SecurityHeadersMiddleware::class, // Added here
        ]);

        $middleware->api(append: [
            StartSession::class,
            ImpersonateSanctum::class,
            SubstituteBindings::class,
        ]);

        $middleware->alias([
            'verified' => \App\Http\Middleware\EnsureEmailIsVerified::class,
            'guest' => Guest::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'ip-check' => \App\Http\Middleware\HorizonIpCheck::class,
            'use-guard' => \App\Http\Middleware\UseGuard::class,
        ]);

        $middleware->validateCsrfTokens(except: [
            '/status/*',
        ]);

        $middleware->trustProxies(at: "*");
    })
    ->withExceptions(function (Exceptions $exceptions) {
        if (!app()->isLocal()) {
            $exceptions->shouldRenderJsonWhen(function (Request $request, Throwable $e) {
                return $request->expectsJson() || $request->acceptsJson();
            });

            $exceptions->render(function (Throwable $e) {
                $previous = $e->getPrevious();

                if ($e instanceof ModelNotFoundException) {
                    $fullModel = $previous->getModel();
                    $modelName = str($fullModel)->afterLast('\\')->headline();
                    return response()->json([
                        'message' => "{$modelName} not found.",
                        'status' => Response::HTTP_NOT_FOUND
                    ], Response::HTTP_NOT_FOUND);
                }

                if ($e instanceof HttpException) {
                    return response()->json([
                        'message' => $e->getMessage(),
                        'status' => $e->getStatusCode()
                    ], $e->getStatusCode());
                }
            });
        }
    })->create();
