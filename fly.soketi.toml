# fly.toml app configuration file generated for lighthouse-soketi on 2025-05-18T11:23:32+02:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'lighthouse-soketi'
primary_region = 'sea'
kill_signal = 'SIGINT'
kill_timeout = '30s'

[experimental]
  auto_rollback = true

[build]
  image = 'quay.io/soketi/soketi:latest-16-alpine'

[env]
  SOKETI_APP_MANAGER_CACHE_ENABLED = 'true'
  SOKETI_APP_MANAGER_DRIVER = 'postgres'
  SOKETI_DEFAULT_APP_ENABLE_CLIENT_MESSAGES = 'true'
  SOKETI_METRICS_ENABLED = 'true'

[http_service]
  internal_port = 6001
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

[[services]]
  protocol = ''
  internal_port = 0

  [[services.ports]]
    port = 6001
    handlers = ['http']

  [[services.ports]]
    port = 443
    handlers = ['tls', 'http']

[[vm]]
  size = 'performance-2x'
