# Exported from Render on 2025-05-21T10:43:26Z
databases:
- name: lighthouse-db
  databaseName: lighthouse_develop
  user: lighthouse_admin
  plan: free
  region: oregon
  ipAllowList:
  - source: 0.0.0.0/0
    description: everywhere
  postgresMajorVersion: "16"
services:
- type: worker
  name: lighthouse-reverb
  region: oregon
  repo: https://github.com/globalpress/lighthouse-api
  plan: starter
  startCommand: php artisan reverb:start --debug
  envVars:
    - key: REVERB_SERVER_PORT
      sync: false
    - key: REVERB_APP_ID
      sync: false
    - key: REVERB_APP_KEY
      sync: false
    - key: REVERB_APP_SECRET
      sync: false
    - key: REVERB_HOST
      sync: false
    - key: REVERB_PORT
      sync: false
    - key: REVERB_SCHEME
      sync: false
    - key: REVERB_APP_CLUSTER
      sync: false
    - key: REVERB_DEBUG
      sync: false

- type: web
  name: lighthouse-api
  runtime: docker
  repo: https://github.com/globalpress/lighthouse-api
  plan: starter
  envVars:
  - key: PUSHER_PORT
    sync: false
  - key: PUSHER_HOST
    sync: false
  - key: PUSHER_APP_SECRET
    sync: false
  - key: PUSHER_APP_KEY
    sync: false
  - key: PUSHER_APP_ID
    sync: false
  - key: DB_URL
    fromDatabase:
      name: lighthouse-db
      property: connectionString
  - key: ENLIGHTN_USERNAME
    sync: false
  - key: DB_CONNECTION
    sync: false
  - key: DATABASE_URL
    fromDatabase:
      name: lighthouse-db
      property: connectionString
  - key: BACKUP_APP_NAME
    sync: false
  - key: AUTH_GUARD
    sync: false
  - key: APP_NAME
    sync: false
  - key: APP_KEY
    sync: false
  - key: APP_ENV
    sync: false
  - key: APP_DEBUG
    sync: false
  - key: REDIS_PREFIX
    sync: false
  - key: REDIS_PORT
    sync: false
  - key: REDIS_PERSISTENT
    sync: false
  - key: REDIS_PASSWORD
    sync: false
  - key: REDIS_HOST
    fromService:
      name: lighthouse-queue
      type: keyvalue
      property: host
  - key: REDIS_CLIENT
    sync: false
  - key: SCHEDULE_CACHE_DRIVER
    sync: false
  - key: BROADCAST_CONNECTION
    sync: false
  - key: APP_URL
    sync: false
  - key: API_VERSION
    sync: false
  - key: REDIS_CACHE_DB
    sync: false
  - key: CACHE_STORE
    sync: false
  - key: FILESYSTEM_DISK
    sync: false
  - key: SESSION_DRIVER
    sync: false
  - key: LOG_STDERR_FORMATTER
    sync: false
  - key: LOG_CHANNEL
    sync: false
  - key: PUSHER_TLS
    sync: false
  - key: PUSHER_APP_CLUSTER
    sync: false
  - key: PUSHER_SCHEME
    sync: false
  - key: LB_PROJECT_KEY
    sync: false
  - key: LB_KEY
    sync: false
  - key: HORIZON_IPS
    sync: false
  - key: QUEUE_CONNECTION
    sync: false
  - key: CACHE_PREFIX
    sync: false
  - key: ENLIGHTN_API_TOKEN
    sync: false
  - key: BACKUP_ARCHIVE_PASSWORD
    sync: false
  - key: GOOGLE_CALENDAR_IMPERSONATE
    sync: false
  - key: GOOGLE_CALENDAR_ID
    sync: false
  - key: MEDIA_DISK
    sync: false
  - key: CLOUDFLARE_R2_PUBLIC_URL
    sync: false
  - key: CLOUDFLARE_R2_USE_PATH_STYLE_ENDPOINT
    sync: false
  - key: CLOUDFLARE_R2_ENDPOINT
    sync: false
  - key: CLOUDFLARE_R2_BUCKET
    sync: false
  - key: CLOUDFLARE_R2_SECRET_ACCESS_KEY
    sync: false
  - key: CLOUDFLARE_R2_ACCESS_KEY_ID
    sync: false
  - key: MAIL_FROM_NAME
    sync: false
  - key: MAIL_FROM_ADDRESS
    sync: false
  - key: MAIL_PASSWORD
    sync: false
  - key: MAIL_USERNAME
    sync: false
  - key: MAIL_PORT
    sync: false
  - key: MAIL_HOST
    sync: false
  - key: MAIL_MAILER
    sync: false
  region: oregon
  dockerContext: .
  dockerfilePath: ./Dockerfile
  autoDeployTrigger: commit
- type: keyvalue
  name: lighthouse-queue
  plan: starter
  region: oregon
  maxmemoryPolicy: allkeys-lru
  ipAllowList: []
version: "1"
