{"debug": true, "metrics": {"port": 9601, "enabled": true, "driver": "prometheus"}, "port": 6001, "database": {"postgres": {"host": "socketi-db.flycast", "user": "lighthouse_sockets", "password": "buMqFPNnLyP2dl3", "database": "lighthouse_sockets"}}, "appManager": {"driver": "array", "array": {"apps": [{"id": "${PUSHER_APP_1_ID}", "key": "${PUSHER_APP_1_KEY}", "secret": "${PUSHER_APP_1_SECRET}", "maxConnections": 100, "enableClientMessages": true, "enabled": true, "maxBackendEventsPerSecond": 50, "maxClientEventsPerSecond": 25, "maxReadRequestsPerSecond": 100}, {"id": "${PUSHER_APP_2_ID}", "key": "${PUSHER_APP_2_KEY}", "secret": "${PUSHER_APP_2_SECRET}", "maxConnections": 100, "enableClientMessages": true, "enabled": false, "maxBackendEventsPerSecond": 50, "maxClientEventsPerSecond": 25, "maxReadRequestsPerSecond": 100}]}, "postgres": {"table": "applications"}}}