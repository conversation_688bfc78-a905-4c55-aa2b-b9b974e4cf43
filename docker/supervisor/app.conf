# [program:queue]
# command=/usr/local/bin/php /var/www/html/artisan queue:work --queue=default,high,low,notifications,events,betteflow,compass
# autostart=true
# autorestart=true
# priority=5
# user=www-data
# numprocs=4
# stdout_events_enabled=true
# stderr_events_enabled=true
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stderr_logfile=/dev/stderr
# stderr_logfile_maxbytes=0
# process_name=%(program_name)s_%(process_num)02d
# stopsignal=QUIT

[program:horizon]
command=/usr/local/bin/php /var/www/html/artisan horizon
autostart=true
autorestart=true
priority=5
user=www-data
numprocs=2
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
process_name=%(program_name)s_%(process_num)02d
stopsignal=QUIT

[program:reverb]
command=/usr/local/bin/php /var/www/html/artisan reverb:start --port 6001
autostart=true
autorestart=true
priority=5
user=www-data
numprocs=1
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
process_name=%(program_name)s_%(process_num)02d
stopsignal=QUIT
