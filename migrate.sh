#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo "Error: .env file not found"
    exit 1
fi

# Set default values for database connection parameters (matching the working hardcoded values)
SOURCE_DB_HOST="${SOURCE_DB_HOST:-ep-dark-hall-a5s6a7s5.aws-us-east-2.pg.laravel.cloud}"
SOURCE_DB_NAME="${SOURCE_DB_NAME:-lighthouse_production}"
SOURCE_DB_USER="${SOURCE_DB_USER:-laravel}"
TARGET_DB_HOST="${TARGET_DB_HOST:-dpg-d0uo0ifdiees73a6o4og-a.oregon-postgres.render.com}"
TARGET_DB_NAME="${TARGET_DB_NAME:-lighthouse_db}"
TARGET_DB_USER="${TARGET_DB_USER:-lighthouse_admin}"

# Debug: Show loaded environment variables (passwords masked for security)
echo "=== Environment Variables Debug ==="
echo "SOURCE_DB_HOST: $SOURCE_DB_HOST"
echo "SOURCE_DB_NAME: $SOURCE_DB_NAME"
echo "SOURCE_DB_USER: $SOURCE_DB_USER"
echo "SOURCE_DB_PASSWORD: ${SOURCE_DB_PASSWORD:+[SET]}${SOURCE_DB_PASSWORD:-[NOT SET]}"
echo "TARGET_DB_HOST: $TARGET_DB_HOST"
echo "TARGET_DB_NAME: $TARGET_DB_NAME"
echo "TARGET_DB_USER: $TARGET_DB_USER"
echo "TARGET_DB_PASSWORD: ${TARGET_DB_PASSWORD:+[SET]}${TARGET_DB_PASSWORD:-[NOT SET]}"
echo "=================================="

# Check if required environment variables are set
if [ -z "$SOURCE_DB_PASSWORD" ] || [ -z "$TARGET_DB_PASSWORD" ]; then
    echo "Error: Database passwords not set in .env file"
    echo "Please ensure SOURCE_DB_PASSWORD and TARGET_DB_PASSWORD are defined in your .env file"
    exit 1
fi

echo "Starting database migration process..."

# Create .pgpass file for passwordless authentication
echo "Creating .pgpass file..."
echo "$SOURCE_DB_HOST:5432:$SOURCE_DB_NAME:$SOURCE_DB_USER:$SOURCE_DB_PASSWORD" > ~/.pgpass
echo "$TARGET_DB_HOST:5432:$TARGET_DB_NAME:$TARGET_DB_USER:$TARGET_DB_PASSWORD" >> ~/.pgpass
chmod 600 ~/.pgpass

# Debug: Show .pgpass file contents (passwords masked)
echo "=== .pgpass file contents (passwords masked) ==="
sed 's/:[^:]*$/:*****/' ~/.pgpass
echo "=============================================="

# Step 1: Dump the production database
echo "Dumping production database..."
echo "Connecting to: $SOURCE_DB_USER@$SOURCE_DB_HOST:5432/$SOURCE_DB_NAME"
DUMP_FILE="$HOME/Desktop/lighthouse_production.sql"
pg_dump -h "$SOURCE_DB_HOST" -p 5432 -U "$SOURCE_DB_USER" "$SOURCE_DB_NAME" > "$DUMP_FILE"

# Check if dump was successful
if [ $? -ne 0 ]; then
    echo "Error: Failed to dump source database"
    echo "Please check:"
    echo "1. Database connection parameters are correct"
    echo "2. .pgpass file format matches connection parameters exactly"
    echo "3. Network connectivity to source database"
    rm -f ~/.pgpass
    exit 1
fi

echo "✓ Database dump completed successfully"

# Step 2: Modify the dump file
echo "Modifying dump file..."
echo "Replacing '$SOURCE_DB_USER' with '$TARGET_DB_USER' in SQL dump..."
# Use a different approach for macOS compatibility
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS (BSD sed) - Comprehensive replacements to catch ALL role references
    sed -i '' \
        -e "s/ROLE $SOURCE_DB_USER/ROLE $TARGET_DB_USER/gi" \
        -e "s/OWNER TO $SOURCE_DB_USER/OWNER TO $TARGET_DB_USER/gi" \
        -e "s/TO $SOURCE_DB_USER/TO $TARGET_DB_USER/gi" \
        -e "s/FOR ROLE $SOURCE_DB_USER/FOR ROLE $TARGET_DB_USER/gi" \
        -e "s/AS $SOURCE_DB_USER/AS $TARGET_DB_USER/gi" \
        -e "s/USER $SOURCE_DB_USER/USER $TARGET_DB_USER/gi" \
        -e "s/CREATE USER $SOURCE_DB_USER/CREATE USER $TARGET_DB_USER/gi" \
        -e "s/ALTER USER $SOURCE_DB_USER/ALTER USER $TARGET_DB_USER/gi" \
        -e "s/DROP USER $SOURCE_DB_USER/DROP USER $TARGET_DB_USER/gi" \
        -e "s/Owner: $SOURCE_DB_USER/Owner: $TARGET_DB_USER/gi" \
        -e "s/; Owner: $SOURCE_DB_USER/; Owner: $TARGET_DB_USER/gi" \
        -e "s/\\b$SOURCE_DB_USER\\b/$TARGET_DB_USER/gi" \
        "$DUMP_FILE"
else
    # Linux (GNU sed) - Comprehensive replacements to catch ALL role references
    sed -i \
        -e "s/ROLE $SOURCE_DB_USER/ROLE $TARGET_DB_USER/gi" \
        -e "s/OWNER TO $SOURCE_DB_USER/OWNER TO $TARGET_DB_USER/gi" \
        -e "s/TO $SOURCE_DB_USER/TO $TARGET_DB_USER/gi" \
        -e "s/FOR ROLE $SOURCE_DB_USER/FOR ROLE $TARGET_DB_USER/gi" \
        -e "s/AS $SOURCE_DB_USER/AS $TARGET_DB_USER/gi" \
        -e "s/USER $SOURCE_DB_USER/USER $TARGET_DB_USER/gi" \
        -e "s/CREATE USER $SOURCE_DB_USER/CREATE USER $TARGET_DB_USER/gi" \
        -e "s/ALTER USER $SOURCE_DB_USER/ALTER USER $TARGET_DB_USER/gi" \
        -e "s/DROP USER $SOURCE_DB_USER/DROP USER $TARGET_DB_USER/gi" \
        -e "s/Owner: $SOURCE_DB_USER/Owner: $TARGET_DB_USER/gi" \
        -e "s/; Owner: $SOURCE_DB_USER/; Owner: $TARGET_DB_USER/gi" \
        -e "s/\\b$SOURCE_DB_USER\\b/$TARGET_DB_USER/gi" \
        "$DUMP_FILE"
fi

# Additional verification step - show any remaining role references for debugging
echo "Checking for any remaining '$SOURCE_DB_USER' references..."
if grep -i "$SOURCE_DB_USER" "$DUMP_FILE" > /dev/null; then
    echo "WARNING: Found remaining '$SOURCE_DB_USER' references in dump file:"
    grep -n -i "$SOURCE_DB_USER" "$DUMP_FILE" | head -10
    echo "You may need to manually review these references."
else
    echo "✓ All '$SOURCE_DB_USER' references have been successfully replaced."
fi

# Step 3: Import to target database
echo "Importing to target database..."
echo "Dumping from: $TARGET_DB_USER@$TARGET_DB_HOST:5432/$TARGET_DB_NAME"
psql --port 5432 -h "$TARGET_DB_HOST" --username="$TARGET_DB_USER" -d "$TARGET_DB_NAME" < "$DUMP_FILE"

# Check if import was successful
if [ $? -ne 0 ]; then
    echo "Error: Failed to import to target database"
    echo "Please check:"
    echo "1. Target database connection parameters are correct"
    echo "2. Target database exists and is accessible"
    echo "3. User '$TARGET_DB_USER' has sufficient privileges"
    rm -f ~/.pgpass
    exit 1
fi

echo "✓ Database import completed successfully"

# Clean up .pgpass file
rm -f ~/.pgpass

echo "Database migration completed successfully!"


